package iym.common.testcontainer;

import com.zaxxer.hikari.HikariConfig;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.OracleContainer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;

import javax.sql.DataSource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.*;
import java.time.Duration;
import java.util.List;
import java.util.stream.Stream;

/**
 * Abstract base class for Oracle Testcontainer integration tests.
 * <p>
 * This class provides a shared Oracle 11 XE container for all tests that extend it.
 * The container is started once and reused across all test classes to improve performance.
 * <p>
 * AUTOMATIC ORACLE CONFIGURATION:
 * - Automatically configures Oracle TestContainer instead of embedded databases (H2)
 * - Uses @DynamicPropertySource to override Spring Boot auto-configuration
 * - Disables embedded database detection to prevent conflicts
 * - No manual datasource configuration required in extending tests
 * <p>
 * PRODUCTION SCHEMA LOADING:
 * - Container starts with minimal init script
 * - Production scripts from docker/oracle/init are automatically executed
 * - Ensures test environment stays in sync with production schema
 * - No manual maintenance required when production scripts change
 * <p>
 * USAGE PATTERNS:
 * <p>
 * For @DataJpaTest (Recommended - JPA/Database Layer Tests):
 * <pre>
 * {@code
 * @DataJpaTest
 * @Import(OracleTestContainerConfiguration.class)
 * @Testcontainers
 * @ActiveProfiles("testcontainers-oracle")
 * @AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
 * @TestInstance(TestInstance.Lifecycle.PER_CLASS)
 * public class MyJpaTest extends AbstractOracleTestContainer {
 *     // Optional: Control schema loading
 *     @Override
 *     protected boolean shouldLoadProductionSchema() {
 *         return false; // Skip for connection-only tests
 *     }
 * }
 * }
 * </pre>
 * <p>
 * For @SpringBootTest (Full Integration Tests - if needed):
 * <pre>
 * {@code
 * @SpringBootTest
 * @ActiveProfiles("testcontainers-oracle")
 * @AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
 * @TestInstance(TestInstance.Lifecycle.PER_CLASS)
 * public class MyIntegrationTest extends AbstractOracleTestContainer {
 *     // Test implementation
 * }
 * }
 * </pre>
 * <p>
 * FEATURES:
 * - Oracle 11 XE Docker container (gvenzl/oracle-xe:********-slim)
 * - Automatic Oracle vs H2 conflict resolution
 * - Automatic production schema loading from docker/oracle/init
 * - Connection pooling with HikariCP optimized for TestContainers
 * - SQL logging enabled for debugging
 * - Proper cleanup after tests
 * - Thread-safe container sharing across test classes
 */
@Testcontainers
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS) // 🔧 REQUIRED: For non-static @BeforeAll methods
public abstract class AbstractOracleTestContainer {
    protected static final String SQL_FILES_BASE_PATH = "docker/oracle/init";

    /**
     * Flag to track if production schema has been loaded to prevent duplicate loading
     * 🔧 ADDED: Ensures schema is loaded only once across all test classes
     */
    private static volatile boolean schemaInitialized = false;

    @PersistenceContext
    protected EntityManager entityManager;

    /**
     * Spring-managed DataSource automatically configured via @DynamicPropertySource
     * This is injected by Spring's dependency injection mechanism using the Oracle TestContainer properties
     */
    @Autowired
    protected DataSource dataSource;

    private final static SqlPlusToJdbcConverter sqlPlusConverter = new SqlPlusToJdbcConverter();

    /**
     * Override this method in test classes to control schema loading behavior
     * 🔧 ADDED: Allows test classes to skip schema loading for simple connection tests
     *
     * @return true to load production schema, false to skip
     */
    protected boolean shouldLoadProductionSchema() {
        return false;
    }

    /**
     * Oracle 11 XE container using the same image as in docker-compose.yml
     * Container is shared across all test classes for better performance
     * <p>
     * MINIMAL INIT: Container starts with minimal JDBC-compatible script
     * Production schema loaded in @BeforeAll method
     */
    @Container
    protected static final OracleContainer ORACLE_CONTAINER = new OracleContainer("gvenzl/oracle-xe:********-slim")
            .withDatabaseName("XE")
            .withUsername("iym")
            .withPassword("iym")
//            .withEnv("APP_USER_ROLE","DBA")
            .withInitScript("testcontainers-oracle-minimal-init.sql")  // 👈 RESTORED: Script added back
            .withReuse(false) // 🔧 FIXED: Enable container reuse for test isolation and performance
            .withExposedPorts(1521) // Expose Oracle port
            .withEnv("ORACLE_ALLOW_REMOTE", "true") // Allow remote connections
            .waitingFor(Wait.forLogMessage(".*DATABASE IS READY TO USE!.*\\n", 1))
            .withStartupTimeout(Duration.ofMinutes(3)); // 🔧 BACK TO: Static for @DynamicPropertySource compatibility

    // 🔧 REMOVED: Manual DataSource creation not needed - Spring handles this via @DynamicPropertySource and @Autowired

    /**
     * Centralized container and schema initialization
     * This method ensures container is ready and database is accessible before proceeding
     * 🔧 IMPROVED: Non-static for test isolation, each test class gets its own DataSource
     */
    @BeforeAll
    void initializeContainerAndSchema() {
        log.info("🚀 [BeforeAll] Initializing Oracle container and schema...");
        log.info("✅ Container automatically started by @Container annotation");
        log.info("✅ DataSource automatically injected by Spring via @Autowired and @DynamicPropertySource");

        // 🔧 DEBUGGING: Log container and connection details
        logContainerDetails();

        // 🔧 BETTER: Wait for database connectivity with retry logic
        waitForDatabaseReady();

        // 🔧 DEBUGGING: Log connection pool status after initialization
        logConnectionPoolStatus("After container initialization");

        // 🔧 SPRING DI: DataSource automatically injected by Spring - no manual creation needed

        // Grant DBA privileges
        grantDbaToAppUser();

        // Load production schema (idempotent - only once across all test classes)
        // 🔧 IMPROVED: Allow test classes to control schema loading behavior
        if (shouldLoadProductionSchema()) {
            if (!schemaInitialized) {
                synchronized (AbstractOracleTestContainer.class) {
                    if (!schemaInitialized) {
                        try {
                            log.info("🔄 Loading production schema for the first time...");
                            loadProductionSchema();
                            schemaInitialized = true;
                            log.info("✅ Production schema loaded successfully");
                        } catch (Exception e) {
                            log.error("❌ Failed to load production schema", e);
                            throw new RuntimeException("Failed to initialize test schema", e);
                        }
                    }
                }
            } else {
                log.info("🔄 Production schema already initialized, skipping...");
            }
        } else {
            log.info("⏭️ Production schema loading skipped by test class configuration");
        }

        // 🔧 CRITICAL FIX: Ensure fresh connection pool for each test class
        ensureFreshConnectionPool();

        // 🔧 DEBUGGING: Final connection pool status
        logConnectionPoolStatus("After schema initialization completed");

        log.info("✅ Container and schema initialization completed");
    }

    /**
     * 🔧 CRITICAL FIX: Ensure fresh connection pool for each test class
     * This prevents stale connection issues when container is reused
     */
    protected void ensureFreshConnectionPool() {
        try {
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDS = (HikariDataSource) dataSource;

                // Check if pool has stale connections
                HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
                if (poolBean != null) {
                    int activeConnections = poolBean.getActiveConnections();
                    int totalConnections = poolBean.getTotalConnections();

                    log.info("🔧 [POOL REFRESH] Current pool state - Active: {}, Total: {}",
                            activeConnections, totalConnections);

                    // If there are existing connections, they might be stale
                    if (totalConnections > 0) {
                        log.info("🔧 [POOL REFRESH] Existing connections detected, testing connectivity...");

                        // Test connectivity
                        try (var testConn = hikariDS.getConnection()) {
                            testConn.createStatement().executeQuery("SELECT 1 FROM DUAL");
                            log.info("✅ [POOL REFRESH] Existing connections are healthy");
                        } catch (Exception e) {
                            log.warn("⚠️ [POOL REFRESH] Existing connections are stale: {}", e.getMessage());

                            // Force pool refresh by evicting idle connections
                            hikariDS.getHikariPoolMXBean().softEvictConnections();
                            log.info("🔧 [POOL REFRESH] Evicted idle connections");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("⚠️ [POOL REFRESH] Error ensuring fresh connection pool: {}", e.getMessage());
        }
    }

    /**
     * Container cleanup is handled automatically by Testcontainers framework
     * 🔧 FIXED: Removed manual container.stop() to prevent premature shutdown
     * Container will be automatically cleaned up when JVM exits
     * This allows multiple test classes to safely share the same container
     */
    @AfterAll
    protected void cleanupContainer() {
        // 🔧 DEBUGGING: Log connection pool status before cleanup
        logConnectionPoolStatus("Before test class cleanup");

        // 🔧 DEBUGGING: Log container details before cleanup
        logContainerDetails();

        // 🔧 CRITICAL FIX: Close HikariCP pool to prevent stale connections
        closeConnectionPool();

        // 🔧 NO-OP: Container cleanup handled automatically by Testcontainers
        // Manual stop() removed to prevent interference between test classes
        log.info("✅ Test class completed - container will be cleaned up automatically by Testcontainers");
    }

    /**
     * 🔧 CRITICAL FIX: Close HikariCP connection pool to prevent stale connections
     * This prevents the "Network Adapter could not establish the connection" error
     * when the next test class tries to use connections from the previous pool
     */
    protected void closeConnectionPool() {
        try {
            if (dataSource != null && dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDS = (HikariDataSource) dataSource;
                if (!hikariDS.isClosed()) {
                    log.info("🔧 [HIKARI FIX] Closing HikariCP connection pool to prevent stale connections...");
                    hikariDS.close();
                    log.info("✅ [HIKARI FIX] HikariCP connection pool closed successfully");
                } else {
                    log.info("ℹ️ [HIKARI FIX] HikariCP connection pool already closed");
                }
            } else {
                log.info("ℹ️ [HIKARI FIX] DataSource is not HikariDataSource or is null");
            }
        } catch (Exception e) {
            log.warn("⚠️ [HIKARI FIX] Error closing connection pool: {}", e.getMessage());
        }
    }

    /**
     * Wait for database to be ready and accepting connections
     * Uses retry logic with proper timeout handling
     * 🔧 IMPROVED: Tests actual database connectivity, not just container status
     */
    protected void waitForDatabaseReady() {
        int maxAttempts = 15; // 15 attempts = 30 seconds max
        int attempt = 0;

        // 🔧 LOG: Container connection details for debugging
        String jdbcUrl = ORACLE_CONTAINER.getJdbcUrl();
        String username = ORACLE_CONTAINER.getUsername();
        String password = ORACLE_CONTAINER.getPassword();

        log.info("⏳ Waiting for Oracle database to be ready...");
        log.info("🔗 Connection details - URL: {}", jdbcUrl);
        log.info("🔗 Connection details - Username: {}", username);
        log.info("🔗 Connection details - Password: {}", password);

        while (true) {
            try {
                // Test actual database connectivity
                try (Connection conn = DriverManager.getConnection(jdbcUrl, username, password)) {

                    // Test basic query to ensure database is fully operational
                    try (Statement stmt = conn.createStatement()) {
                        stmt.executeQuery("SELECT 1 FROM DUAL");
                    }

                    log.info("✅ Database is ready and accepting connections!");
                    log.info("✅ Final connection details - URL: {}", jdbcUrl);
                    log.info("✅ Final connection details - Username: {}", username);
                    log.info("✅ Connection test successful after {} attempts", attempt + 1);
                    return;
                }
            } catch (SQLException e) {
                attempt++;
                log.info("⏳ Database not ready yet... attempt {}/{} ({})",
                        attempt, maxAttempts, e.getMessage());

                if (attempt >= maxAttempts) {
                    log.error("❌ Database failed to become ready after {} attempts", maxAttempts);
                    throw new RuntimeException("Database failed to become ready after " +
                            (maxAttempts * 2) + " seconds. Last error: " + e.getMessage(), e);
                }

                try {
                    Thread.sleep(2000); // Wait 2 seconds between attempts
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Database readiness check interrupted", ie);
                }
            }
        }
    }

    protected void grantDbaToAppUser() {
        // Konteyner başladıktan sonra bu metot çalışır.
        // SYSTEM kullanıcısı olarak bağlanıp ayrıcalıklı işlemleri yapalım.
        String jdbcUrl = ORACLE_CONTAINER.getJdbcUrl();
        String systemPassword = ORACLE_CONTAINER.getPassword(); // SYSTEM şifresi, 'iym_password' ile aynıdır.

        // Try-with-resources ile Connection ve Statement'ın otomatik kapanmasını sağlıyoruz.
        try (Connection connection = DriverManager.getConnection(jdbcUrl, "system", systemPassword);
             Statement statement = connection.createStatement()) {

            statement.execute("GRANT DBA TO iym");

            log.info("✅ Successfully granted DBA privileges to iym user");

        } catch (Exception e) {
            log.error("❌ Error granting DBA to iym: " + e.getMessage());
            throw new RuntimeException("Failed to setup tablespace and user privileges.", e);
        }
    }
//    /**
//     * Setup production schema before any tests run
//     * This ensures schema is ready before Spring context initialization
//     */
//    @BeforeAll
//    protected static void setupProductionSchema() throws Exception {
//        if (schemaInitialized) {
//            log.info("🔄 Production schema already initialized, skipping...");
//            return;
//        }
//
//        log.info("🚀 [BeforeAll] Starting container and loading production schema...");
//
//        // Ensure container is started
//        if (!ORACLE_CONTAINER.isRunning()) {
//            ORACLE_CONTAINER.start();
//            log.info("✅ Oracle container started");
//        }
//
//        // Load production schema
//        loadProductionSchema();
//        schemaInitialized = true;
//
//        log.info("✅ [BeforeAll] Production schema loaded successfully!");
//    }

    /**
     * Load production schema from docker/oracle/init directory
     */
    public void loadProductionSchema() throws Exception {
        log.info("🔍 Loading production schema from docker/oracle/init...");
        // Get all SQL files from production directory
        List<Path> sqlFiles = discoverProductionScripts();
        log.info("📋 Found {} production SQL files", sqlFiles.size());

        // Execute each SQL file
        for (Path sqlFile : sqlFiles) {
            log.info("📄 Executing: {}", sqlFile.getFileName());
            executeAsJdbcStatements(sqlFile);//                runSqlFile(stmt, sqlFile);
        }

        log.info("✅ All production scripts executed successfully");

    }

    /**
     * Discover production SQL scripts
     */
    private static List<Path> discoverProductionScripts() throws Exception {
        Path scriptsDir;
        // Get current working directory
        Path currentDir = Paths.get(System.getProperty("user.dir"));
        // Check if we're in makos directory, then go up to project root
        if (currentDir.getFileName().toString().equals("makos")) {
            scriptsDir = currentDir.getParent().resolve(SQL_FILES_BASE_PATH);
        } else {
            // We're already in project root
            scriptsDir = currentDir.resolve(SQL_FILES_BASE_PATH);
        }

        log.info("🔍 Looking for scripts in: {}", scriptsDir.toAbsolutePath());

        if (!Files.exists(scriptsDir)) {
            log.error("❌ Production scripts directory not found: {}", scriptsDir.toAbsolutePath());
            log.error("🔍 Current working directory: {}", System.getProperty("user.dir"));
            throw new RuntimeException("Production scripts directory not found: " + scriptsDir.toAbsolutePath());
        }

        try (Stream<Path> paths = Files.walk(scriptsDir)) {
            return paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".sql"))
                    .filter(path -> !isExcludedScript(path))
                    .sorted()
                    .toList();
        }
    }

    /**
     * Check if script should be excluded
     */
    private static boolean isExcludedScript(Path scriptPath) {
        String fileName = scriptPath.getFileName().toString();
        return fileName.contains("healthcheck");
    }

    /**
     * Configure Spring properties dynamically based on the running container
     * 🔧 ENHANCED: Explicitly disable embedded database detection to prevent H2 conflicts
     */
    @DynamicPropertySource
    static void overrideProperties(DynamicPropertyRegistry registry) {
        // 🔧 CRITICAL: Ensure container is started before accessing properties
        if (!ORACLE_CONTAINER.isRunning()) {
            ORACLE_CONTAINER.start();
            log.info("✅ Oracle container started for @DynamicPropertySource");
        }

        // 🔧 CRITICAL: Disable embedded database detection to prevent H2 auto-configuration
        registry.add("spring.datasource.embedded-database-connection", () -> "none");
        registry.add("spring.test.database.replace", () -> "none");

        // Oracle TestContainer datasource configuration
        registry.add("spring.datasource.url", ORACLE_CONTAINER::getJdbcUrl);
        registry.add("spring.datasource.username", ORACLE_CONTAINER::getUsername);
        registry.add("spring.datasource.password", ORACLE_CONTAINER::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "oracle.jdbc.OracleDriver");

        // JPA/Hibernate configuration for Oracle
        registry.add("spring.jpa.database-platform", () -> "org.hibernate.dialect.OracleDialect");
        registry.add("spring.jpa.properties.hibernate.default_schema", () -> "iym");
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "none");
        registry.add("spring.jpa.show-sql", () -> "true");
        registry.add("spring.jpa.properties.hibernate.format_sql", () -> "true");

        // Connection pool configuration - optimized for testcontainers with container reuse
        registry.add("spring.datasource.hikari.connectionTimeout", () -> "60000");
        registry.add("spring.datasource.hikari.maximumPoolSize", () -> "100");
        registry.add("spring.datasource.hikari.minimumIdle", () -> "0"); // 🔧 FIX: No minimum idle to prevent stale connections
        registry.add("spring.datasource.hikari.idleTimeout", () -> "30000"); // 🔧 FIX: Shorter idle timeout (30 seconds)
        registry.add("spring.datasource.hikari.maxLifetime", () -> "1200000"); // 🔧 FIX: Shorter max lifetime (2 minutes)
        registry.add("spring.datasource.hikari.leakDetectionThreshold", () -> "30000");
        registry.add("spring.datasource.hikari.keepaliveTime", () -> "300000"); // 🔧 FIX: Keep connections alive
        registry.add("spring.datasource.hikari.validationTimeout", () -> "5000"); // 🔧 FIX: Connection validation timeout

        log.info("✅ Oracle TestContainer properties configured (embedded database disabled)");
    }

    /**
     * 🔧 DEBUGGING: Log detailed container information
     */
    protected void logContainerDetails() {
        try {
            log.info("🔍 [CONTAINER DEBUG] Container Status:");
            log.info("  - Container ID: {}", ORACLE_CONTAINER.getContainerId());
            log.info("  - Container Running: {}", ORACLE_CONTAINER.isRunning());
            log.info("  - Container Created: {}", ORACLE_CONTAINER.isCreated());
            log.info("  - JDBC URL: {}", ORACLE_CONTAINER.getJdbcUrl());
            log.info("  - Username: {}", ORACLE_CONTAINER.getUsername());
            log.info("  - Database Name: {}", ORACLE_CONTAINER.getDatabaseName());
            log.info("  - Exposed Ports: {}", ORACLE_CONTAINER.getExposedPorts());
            log.info("  - Mapped Port 1521: {}", ORACLE_CONTAINER.getMappedPort(1521));
        } catch (Exception e) {
            log.warn("⚠️ [CONTAINER DEBUG] Error logging container details: {}", e.getMessage());
        }
    }

    /**
     * 🔧 DEBUGGING: Log detailed HikariCP connection pool status
     */
    protected void logConnectionPoolStatus(String context) {
        try {
            log.info("🔍 [HIKARI DEBUG] Connection Pool Status - {}:", context);

            if (dataSource == null) {
                log.warn("  - DataSource is NULL!");
                return;
            }

            log.info("  - DataSource Class: {}", dataSource.getClass().getName());

            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDS = (HikariDataSource) dataSource;
                log.info("  - Pool Name: {}", hikariDS.getPoolName());
                log.info("  - JDBC URL: {}", hikariDS.getJdbcUrl());
                log.info("  - Is Closed: {}", hikariDS.isClosed());
                log.info("  - Is Running: {}", hikariDS.isRunning());

                // Get pool MXBean for detailed stats
                HikariPoolMXBean poolBean = hikariDS.getHikariPoolMXBean();
                if (poolBean != null) {
                    log.info("  - Active Connections: {}", poolBean.getActiveConnections());
                    log.info("  - Idle Connections: {}", poolBean.getIdleConnections());
                    log.info("  - Total Connections: {}", poolBean.getTotalConnections());
                    log.info("  - Threads Awaiting Connection: {}", poolBean.getThreadsAwaitingConnection());
                } else {
                    log.warn("  - Pool MXBean is NULL!");
                }

                // Test a connection
                try (var connection = hikariDS.getConnection()) {
                    log.info("  - Test Connection Successful: {}", !connection.isClosed());
                    log.info("  - Connection Class: {}", connection.getClass().getName());

                    // Try to get Oracle connection ID
                    try (var stmt = connection.createStatement()) {
                        var rs = stmt.executeQuery("SELECT SYS_CONTEXT('USERENV', 'SID') FROM DUAL");
                        if (rs.next()) {
                            log.info("  - Oracle Session ID (SID): {}", rs.getString(1));
                        }
                    } catch (Exception e) {
                        log.warn("  - Could not get Oracle SID: {}", e.getMessage());
                    }

                } catch (Exception e) {
                    log.error("  - Test Connection FAILED: {}", e.getMessage());
                }
            } else {
                log.warn("  - DataSource is not HikariDataSource: {}", dataSource.getClass());
            }

        } catch (Exception e) {
            log.error("⚠️ [HIKARI DEBUG] Error logging connection pool status: {}", e.getMessage(), e);
        }
    }

    // 🔧 REMOVED: OracleTestContainerConfiguration - static class can't access non-static container
    // Using instance DataSource from @BeforeAll instead

    /**
     * Execute SQL script in the Oracle container
     * Useful for test data setup
     */
    protected void executeSqlScript(String sqlScript) {
        try {
            ORACLE_CONTAINER.execInContainer("sqlplus", "-S", "iym/iym@XE", "@" + sqlScript);
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute SQL script: " + sqlScript, e);
        }
    }

    /**
     * Execute SQL statement in the Oracle container
     * Useful for quick test data setup
     */
    protected void executeSql(String sql) {
        try {
            ORACLE_CONTAINER.execInContainer("sqlplus", "-S", "iym/iym@XE", "-c", sql);
        } catch (Exception e) {
            throw new RuntimeException("Failed to execute SQL: " + sql, e);
        }
    }

    /**
     * Executes parsed SQL statements against the Oracle TestContainer
     */
    protected static void executeParsedStatements(DataSource dataSource, List<String> statements, String fileName) throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            connection.setAutoCommit(false);

            log.info("📊 Executing {} statements from {}", statements.size(), fileName);

            try (Statement statement = connection.createStatement()) {
                int executedCount = 0;
                for (String sql : statements) {
                    String trimmedSql = sql.trim();
                    if (!trimmedSql.isEmpty()) {
                        try {
                            log.info("🔄 Executing statement #{}: {}", ++executedCount, trimmedSql);

                            boolean hasResultSet = statement.execute(trimmedSql);
                            log.info("✅ Statement #{} executed successfully - hasResultSet: {}", executedCount, hasResultSet);

                            // Check for warnings
                            SQLWarning warning = statement.getWarnings();
                            if (warning != null) {
                                log.warn("⚠️ Statement #{} completed with warnings: {}", executedCount, warning.getMessage());
                                statement.clearWarnings();
                            }

                        } catch (SQLException e) {
                            log.error("❌ Statement #{} failed in file {}: {} - Error: {}",
                                    executedCount, fileName,
                                    trimmedSql,
                                    e.getMessage());

                            // Check if this is a critical error
                            if (isCriticalError(e)) {
                                throw new SQLException("Critical SQL error in statement #" + executedCount +
                                        " of file " + fileName + ": " + e.getMessage(), e);
                            } else {
                                log.warn("⚠️ Non-critical error ignored: {}", e.getMessage());
                            }
                        }
                    }
                }

                connection.commit();
                log.info("🎉 {} statements processed successfully for: {}", executedCount, fileName);

            } catch (SQLException e) {
                connection.rollback();
                log.error("❌ SQL execution failed for: {} - {}", fileName, e.getMessage());
                throw e;
            }
        }
    }

    /**
     * Determines if a SQL error is critical and should fail the test
     * Updated to be more strict - most SQL errors should fail the test
     */
    private static boolean isCriticalError(SQLException e) {
        String message = e.getMessage().toLowerCase();
        int errorCode = e.getErrorCode();

        // Only ignore very specific non-critical errors
        if (message.contains("already exists") &&
                (errorCode == 955 || errorCode == 1430)) { // ORA-00955: name already used, ORA-01430: column being added already exists
            return false;
        }

        // All PL/SQL syntax errors are critical
        if (errorCode == 6550) { // ORA-06550: PL/SQL compilation error
            return true;
        }

        // All other SQL errors are considered critical for this test
        return true;
    }

    /**
     * Parses Oracle SQL content into individual executable statements
     * Uses SqlPlusToJdbcConverter for comprehensive parsing
     */
    protected static List<String> parseOracleSqlStatements(String sqlContent/*, String fileName*/) {
        return sqlPlusConverter.convertToJdbcStatements(sqlContent/*, fileName*/);
    }

    protected void executeAsJdbcStatements(Path sqlFile) throws SQLException, IOException {
        String sqlContent = Files.readString(sqlFile);
        log.info("📄 sqlContent of {}: {}", sqlFile.getFileName(), sqlContent);
        // Parse and save cleaned SQL first
        List<String> statements = parseOracleSqlStatements(sqlContent/*, sqlFile.getFileName().toString()*/);
        log.info("📝 Parsed {} statements from {}", statements.size(), sqlFile.getFileName());

        // Print fstatements for debugging
        for (int i = 0; i < statements.size(); i++) {
            String stmt = statements.get(i);
            log.info("Statement #{}: {}\n", i + 1, stmt);
        }

        log.info("✅ SQL file parsed successfully: {}", sqlFile.getFileName());
        log.info("🔍 Temizlenmiş SQL dosyası analiz için kaydedildi: makos/src/test/resources/cleaned-sql-analysis/");

        // Execute the parsed SQL statements
        log.info("🚀 Executing parsed SQL statements...");
        executeParsedStatements(dataSource, statements, sqlFile.getFileName().toString());

        log.info("✅ SQL file executed successfully: {}", sqlFile.getFileName());
    }
}
