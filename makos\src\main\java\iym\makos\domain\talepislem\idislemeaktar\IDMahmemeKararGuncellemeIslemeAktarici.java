package iym.makos.domain.talepislem.idislemeaktar;

import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.talep.MahkemeKararBilgiGuncellemeTalep;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.talep.MahkemeKararBilgiGuncelleTalepRepo;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class IDMahmemeKararGuncellemeIslemeAktarici extends IDMahkemeKararTalepIslemeAktariciBase {

    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private final MahkemeKararBilgiGuncelleTalepRepo mahkemeKararBilgiGuncelleTalepRepo;

    @Autowired
    public IDMahmemeKararGuncellemeIslemeAktarici(MahkemeKararBilgiGuncelleTalepRepo mahkemeKararBilgiGuncelleTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo

    ) {
        this.mahkemeKararBilgiGuncelleTalepRepo = mahkemeKararBilgiGuncelleTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
    }

    protected boolean updateRelatedTables(Long mahmemeKararTalepId) {
        boolean result = false;
        try {
            String durum = "ISLEMDE";
            CommonUtils.safeList(dMahkemeKararTalepRepo.findByMahkemeKararTalepId(mahmemeKararTalepId))
                    .forEach(dMahkemeKararTalep -> {
                        dMahkemeKararTalep.setDurum(durum);
                        dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                        Optional<MahkemeKararBilgiGuncellemeTalep> mahkemeKararBilgiGuncelleTalepOpt = mahkemeKararBilgiGuncelleTalepRepo.findByMahkemeKararDetayId(dMahkemeKararTalep.getMahkemeKararTalepId());
                        if(mahkemeKararBilgiGuncelleTalepOpt.isPresent()){
                            MahkemeKararBilgiGuncellemeTalep mahkemeKararBilgiGuncellemeTalep = mahkemeKararBilgiGuncelleTalepOpt.get();
                            mahkemeKararBilgiGuncellemeTalep.setDurumu(durum);
                            mahkemeKararBilgiGuncelleTalepRepo.save(mahkemeKararBilgiGuncellemeTalep);
                        }

                    });

            result = true;

        }catch (Exception ex){
            log.error("IDMahmemeKararGuncellemeIslemeAktarici process failed, mahkemeKararTalepId:{}",  mahmemeKararTalepId, ex);
        }
        return  result;
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME;
    }

}

