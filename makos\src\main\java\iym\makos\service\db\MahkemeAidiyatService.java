package iym.makos.service.db;

import iym.common.model.entity.iym.mk.MahkemeKararAidiyat;
import iym.common.service.db.mk.DbMahkemeKararAidiyatService;
import iym.makos.model.dto.db.MahkemeAidiyatDTO;
import iym.makos.mapper.MahkemeAidiyatMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Service for MahkemeAidiyatTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeAidiyatService {

    private final DbMahkemeKararAidiyatService dbMahkemeKararAidiyatService;
    private final MahkemeAidiyatMapper mahkemeAidiyatMapper;

    /**
     * Get mahkeme aidiyat talep records by mahkeme ID
     * @param mahkemeKararId Mahkeme ID
     * @return List of MahkemeAidiyatTalepDTO
     */
    public List<MahkemeAidiyatDTO> findByMahkemeId(Long mahkemeKararId) {
        List<MahkemeKararAidiyat> mahkemeKararAidiyatTalepList = dbMahkemeKararAidiyatService.findByMahkemeKararId(mahkemeKararId);
        return mahkemeAidiyatMapper.toDtoList(mahkemeKararAidiyatTalepList);
    }


    /**
     * Get mahkeme aidiyat talep by mahkeme ID and aidiyat kod
     * @param mahkemeKararId Mahkeme ID
     * @param aidiyatKod Aidiyat kod
     * @return MahkemeAidiyatTalepDTO
     * @throws ResponseStatusException if not found
     */
    public MahkemeAidiyatDTO findByMahkemeIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod) {
        return dbMahkemeKararAidiyatService.findByMahkemeKararIdAndAidiyatKod(mahkemeKararId, aidiyatKod)
                .map(mahkemeAidiyatMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat  bulunamadı"));
    }


    /**
     * Create new mahkeme aidiyat talep
     * @param mahkemeAidiyatDTO MahkemeAidiyatTalepDTO
     * @return Created MahkemeAidiyatTalepDTO
     */
    public MahkemeAidiyatDTO create(MahkemeAidiyatDTO mahkemeAidiyatDTO) {
        // Check if mahkeme aidiyat already exists
        if (mahkemeAidiyatDTO.getMahkemeKararId() != null && mahkemeAidiyatDTO.getAidiyatKod() != null) {
            Optional<MahkemeKararAidiyat> existingMahkemeAidiyatTalep = dbMahkemeKararAidiyatService.findByMahkemeKararIdAndAidiyatKod( mahkemeAidiyatDTO.getMahkemeKararId(), mahkemeAidiyatDTO.getAidiyatKod());
                    
            if (existingMahkemeAidiyatTalep.isPresent()) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, 
                    "Bu mahkeme aidiyat talebi zaten mevcut: Mahkeme ID: " +
                            mahkemeAidiyatDTO.getMahkemeKararId() + ", Aidiyat Kod: " +
                            mahkemeAidiyatDTO.getAidiyatKod());
            }
        }

        MahkemeKararAidiyat mahkemeKararAidiyat = mahkemeAidiyatMapper.toEntity(mahkemeAidiyatDTO);
        dbMahkemeKararAidiyatService.save(mahkemeKararAidiyat);
        log.info("Mahkeme aidiyat  oluşturuldu: {}", mahkemeKararAidiyat.getId());
        return mahkemeAidiyatMapper.toDto(mahkemeKararAidiyat);
    }

    /**
     * Update mahkeme aidiyat talep
     * @param id Mahkeme aidiyat talep ID
     * @param mahkemeAidiyatDTO MahkemeAidiyatTalepDTO
     * @return Updated MahkemeAidiyatTalepDTO
     */
    public MahkemeAidiyatDTO update(Long id, MahkemeAidiyatDTO mahkemeAidiyatDTO) {
        MahkemeKararAidiyat existingMahkemeKararAidiyat = dbMahkemeKararAidiyatService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat bulunamadı: " + id));

        // Check if mahkeme aidiyat is being changed and already exists
        if (mahkemeAidiyatDTO.getMahkemeKararId() != null &&
                mahkemeAidiyatDTO.getAidiyatKod() != null &&
            (!mahkemeAidiyatDTO.getMahkemeKararId().equals(existingMahkemeKararAidiyat.getMahkemeKararId()) ||
             !mahkemeAidiyatDTO.getAidiyatKod().equals(existingMahkemeKararAidiyat.getAidiyatKod()))) {
            
            Optional<MahkemeKararAidiyat> mahkemeAidiyatTalepWithSameDetails =
                dbMahkemeKararAidiyatService.findByMahkemeKararIdAndAidiyatKod(
                        mahkemeAidiyatDTO.getMahkemeKararId(),
                        mahkemeAidiyatDTO.getAidiyatKod());
                    
            if (mahkemeAidiyatTalepWithSameDetails.isPresent() && 
                !mahkemeAidiyatTalepWithSameDetails.get().getId().equals(id)) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, 
                    "Bu mahkeme aidiyat  zaten mevcut: Mahkeme ID: " +
                            mahkemeAidiyatDTO.getMahkemeKararId() + ", Aidiyat Kod: " +
                            mahkemeAidiyatDTO.getAidiyatKod());
            }
        }

        MahkemeKararAidiyat updatedMahkemeKararAidiyat = mahkemeAidiyatMapper.updateEntityFromDto(existingMahkemeKararAidiyat, mahkemeAidiyatDTO);
        dbMahkemeKararAidiyatService.update(updatedMahkemeKararAidiyat);
        log.info("Mahkeme aidiyat  güncellendi: {}", updatedMahkemeKararAidiyat.getId());
        return mahkemeAidiyatMapper.toDto(updatedMahkemeKararAidiyat);
    }

    /**
     * Delete mahkeme aidiyat talep
     * @param id Mahkeme aidiyat talep ID
     */
    public void delete(Long id) {
        MahkemeKararAidiyat mahkemeKararAidiyatTalep = dbMahkemeKararAidiyatService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat  bulunamadı: " + id));
        
        dbMahkemeKararAidiyatService.delete(mahkemeKararAidiyatTalep);
        log.info("Mahkeme aidiyat  silindi: {}", id);
    }
}
