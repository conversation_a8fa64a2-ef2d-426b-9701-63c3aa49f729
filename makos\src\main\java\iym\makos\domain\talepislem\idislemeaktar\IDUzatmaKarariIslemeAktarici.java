package iym.makos.domain.talepislem.idislemeaktar;

import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.HedeflerTalepRepo;
import iym.db.jpa.dao.talep.HedeflerDetayTalepRepo;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class IDUzatmaKarariIslemeAktarici extends IDMahkemeKararTalepIslemeAktariciBase {

    private final HedeflerDetayTalepRepo hedeflerDetayTalepRepo;
    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private final HedeflerTalepRepo hedeflerTalepRepo;

    @Autowired
    public IDUzatmaKarariIslemeAktarici(HedeflerDetayTalepRepo hedeflerDetayTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerTalepRepo hedeflerTalepRepo
    ) {
        this.hedeflerDetayTalepRepo = hedeflerDetayTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
        this.hedeflerTalepRepo = hedeflerTalepRepo;
    }

    protected boolean updateRelatedTables(Long mahkemeKararTalepId) {
        boolean result = false;
        String durum = "ISLEMDE";
        try {
            CommonUtils.safeList(hedeflerDetayTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId))
                    .forEach(hedeflerDetayTalep -> {
                        hedeflerDetayTalep.setDurumu(durum);
                        hedeflerDetayTalepRepo.save(hedeflerDetayTalep);

                        Long detayMahkemeKararId = hedeflerDetayTalep.getMahkemeKararDetayTalepId();

                        Optional<DetayMahkemeKararTalep> detayMahkemeKararTalepOpt = dMahkemeKararTalepRepo.findById(detayMahkemeKararId);
                        if (detayMahkemeKararTalepOpt.isPresent()) {
                            DetayMahkemeKararTalep detayMahkemeKararTalep = detayMahkemeKararTalepOpt.get();
                            detayMahkemeKararTalep.setDurum(durum);
                            dMahkemeKararTalepRepo.save(detayMahkemeKararTalep);
                        }
                    });


            CommonUtils.safeList(hedeflerTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId))
                    .forEach(hedeflerTalep -> {
                        hedeflerTalep.setDurumu(durum);
                        hedeflerTalepRepo.save(hedeflerTalep);
                    });
            result = true;

        } catch (Exception ex) {
            log.error("IDUzatmaKarariIslemeAktarici process failed, mahkemeKararTalepId:{}", mahkemeKararTalepId, ex);
        }
        return result;
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI;
    }

}

