package iym.makos.service.db;

import iym.common.model.entity.iym.EvrakGelenKurumlar;
import iym.common.service.db.DbEvrakGelenKurumlarService;
import iym.makos.model.dto.db.EvrakGelenKurumlarDTO;
import iym.makos.mapper.EvrakGelenKurumlarMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Service for EvrakGelenKurumlar operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EvrakGelenKurumlarService {

    private final DbEvrakGelenKurumlarService dbEvrakGelenKurumlarService;
    private final EvrakGelenKurumlarMapper evrakGelenKurumlarMapper;

    /**
     * Get all EvrakGelenKurumlar records
     * @return List of EvrakGelenKurumlarDTO
     */
    public List<EvrakGelenKurumlarDTO> findAll() {
        List<EvrakGelenKurumlar> evrakGelenKurumlarList = dbEvrakGelenKurumlarService.findAll();
        return evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList);
    }

    /**
     * Get all EvrakGelenKurumlar records ordered by idx
     * @return List of EvrakGelenKurumlarDTO
     */
    public List<EvrakGelenKurumlarDTO> findAllOrdered() {
        List<EvrakGelenKurumlar> evrakGelenKurumlarList = dbEvrakGelenKurumlarService.findAllByOrderByIdxAsc();
        return evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList);
    }

    /**
     * Get all EvrakGelenKurumlar records with pagination
     * @param pageable Pagination information
     * @return Page of EvrakGelenKurumlarDTO
     */
    public Page<EvrakGelenKurumlarDTO> findAll(Pageable pageable) {
        Page<EvrakGelenKurumlar> evrakGelenKurumlarPage = dbEvrakGelenKurumlarService.findAll(pageable);
        List<EvrakGelenKurumlarDTO> dtoList = evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarPage.getContent());
        return new PageImpl<>(dtoList, pageable, evrakGelenKurumlarPage.getTotalElements());
    }

    /**
     * Get EvrakGelenKurumlar by id
     * @param id EvrakGelenKurumlar id
     * @return EvrakGelenKurumlarDTO
     */
    public EvrakGelenKurumlarDTO findById(Long id) {
        EvrakGelenKurumlar evrakGelenKurumlar = dbEvrakGelenKurumlarService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak gelen kurum bulunamadı: " + id));
        return evrakGelenKurumlarMapper.toDto(evrakGelenKurumlar);
    }

    /**
     * Get EvrakGelenKurumlar by kurumKod
     * @param kurumKod Kurum kodu
     * @return EvrakGelenKurumlarDTO
     */
    public EvrakGelenKurumlarDTO findByKurumKod(String kurumKod) {
        EvrakGelenKurumlar evrakGelenKurumlar = dbEvrakGelenKurumlarService.findByKurumKod(kurumKod)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak gelen kurum bulunamadı: " + kurumKod));
        return evrakGelenKurumlarMapper.toDto(evrakGelenKurumlar);
    }

    /**
     * Get EvrakGelenKurumlar by kurumAdi
     * @param kurumAdi Kurum adı
     * @return List of EvrakGelenKurumlarDTO
     */
    public List<EvrakGelenKurumlarDTO> findByKurumAdi(String kurumAdi) {
        List<EvrakGelenKurumlar> evrakGelenKurumlarList = dbEvrakGelenKurumlarService.findByKurumAdi(kurumAdi);
        return evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList);
    }

    /**
     * Get EvrakGelenKurumlar by kurum
     * @param kurum Kurum
     * @return List of EvrakGelenKurumlarDTO
     */
    public List<EvrakGelenKurumlarDTO> findByKurum(String kurum) {
        List<EvrakGelenKurumlar> evrakGelenKurumlarList = dbEvrakGelenKurumlarService.findByKurum(kurum);
        return evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList);
    }

    /**
     * Get EvrakGelenKurumlar by idx
     * @param idx Sıra numarası
     * @return List of EvrakGelenKurumlarDTO
     */
    public List<EvrakGelenKurumlarDTO> findByIdx(Long idx) {
        List<EvrakGelenKurumlar> evrakGelenKurumlarList = dbEvrakGelenKurumlarService.findByIdx(idx);
        return evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList);
    }

    /**
     * Search EvrakGelenKurumlar by kurumAdi
     * @param kurumAdi Kurum adı
     * @return List of EvrakGelenKurumlarDTO
     */
    public List<EvrakGelenKurumlarDTO> findByKurumAdiContainingIgnoreCase(String kurumAdi) {
        List<EvrakGelenKurumlar> evrakGelenKurumlarList = dbEvrakGelenKurumlarService.findByKurumAdiContainingIgnoreCase(kurumAdi);
        return evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList);
    }

    /**
     * Search EvrakGelenKurumlar by kurum
     * @param kurum Kurum
     * @return List of EvrakGelenKurumlarDTO
     */
    public List<EvrakGelenKurumlarDTO> findByKurumContainingIgnoreCase(String kurum) {
        List<EvrakGelenKurumlar> evrakGelenKurumlarList = dbEvrakGelenKurumlarService.findByKurumContainingIgnoreCase(kurum);
        return evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList);
    }

    /**
     * Search EvrakGelenKurumlar by kurumKod
     * @param kurumKod Kurum kodu
     * @return List of EvrakGelenKurumlarDTO
     */
    public List<EvrakGelenKurumlarDTO> findByKurumKodContaining(String kurumKod) {
        List<EvrakGelenKurumlar> evrakGelenKurumlarList = dbEvrakGelenKurumlarService.findByKurumKodContaining(kurumKod);
        return evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList);
    }

    /**
     * Check if EvrakGelenKurumlar exists by kurumKod
     * @param kurumKod Kurum kodu
     * @return true if exists, false otherwise
     */
    public boolean existsByKurumKod(String kurumKod) {
        return dbEvrakGelenKurumlarService.existsByKurumKod(kurumKod);
    }

    /**
     * Check if EvrakGelenKurumlar exists by kurumAdi
     * @param kurumAdi Kurum adı
     * @return true if exists, false otherwise
     */
    public boolean existsByKurumAdi(String kurumAdi) {
        return dbEvrakGelenKurumlarService.existsByKurumAdi(kurumAdi);
    }

    /**
     * Create new EvrakGelenKurumlar
     * @param evrakGelenKurumlarDTO EvrakGelenKurumlarDTO
     * @return Created EvrakGelenKurumlarDTO
     */
    public EvrakGelenKurumlarDTO create(EvrakGelenKurumlarDTO evrakGelenKurumlarDTO) {
        // Check if kurumKod already exists
        if (dbEvrakGelenKurumlarService.existsByKurumKod(evrakGelenKurumlarDTO.getKurumKod())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Kurum kodu zaten mevcut: " + evrakGelenKurumlarDTO.getKurumKod());
        }

        // Generate ID if not provided
        if (evrakGelenKurumlarDTO.getId() == null) {
            Optional<Long> maxId = dbEvrakGelenKurumlarService.findAll().stream()
                    .map(EvrakGelenKurumlar::getId)
                    .max(Long::compareTo);
            evrakGelenKurumlarDTO.setId(maxId.orElse(0L) + 1);
        }

        // Generate idx if not provided
        if (evrakGelenKurumlarDTO.getIdx() == null) {
            Optional<Long> maxIdx = dbEvrakGelenKurumlarService.findAll().stream()
                    .map(EvrakGelenKurumlar::getIdx)
                    .max(Long::compareTo);
            evrakGelenKurumlarDTO.setIdx(maxIdx.orElse(0L) + 1);
        }

        EvrakGelenKurumlar evrakGelenKurumlar = evrakGelenKurumlarMapper.toEntity(evrakGelenKurumlarDTO);
        dbEvrakGelenKurumlarService.save(evrakGelenKurumlar);
        log.info("Evrak gelen kurum oluşturuldu: {}", evrakGelenKurumlar.getId());
        return evrakGelenKurumlarMapper.toDto(evrakGelenKurumlar);
    }

    /**
     * Update existing EvrakGelenKurumlar
     * @param id EvrakGelenKurumlar id
     * @param evrakGelenKurumlarDTO EvrakGelenKurumlarDTO
     * @return Updated EvrakGelenKurumlarDTO
     */
    public EvrakGelenKurumlarDTO update(Long id, EvrakGelenKurumlarDTO evrakGelenKurumlarDTO) {
        EvrakGelenKurumlar existingEvrakGelenKurumlar = dbEvrakGelenKurumlarService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak gelen kurum bulunamadı: " + id));

        // Check if kurumKod already exists for another entity
        if (!existingEvrakGelenKurumlar.getKurumKod().equals(evrakGelenKurumlarDTO.getKurumKod()) &&
                dbEvrakGelenKurumlarService.existsByKurumKod(evrakGelenKurumlarDTO.getKurumKod())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Kurum kodu zaten mevcut: " + evrakGelenKurumlarDTO.getKurumKod());
        }

        // Ensure ID is not changed
        evrakGelenKurumlarDTO.setId(id);

        EvrakGelenKurumlar updatedEvrakGelenKurumlar = evrakGelenKurumlarMapper.updateEntityFromDto(existingEvrakGelenKurumlar, evrakGelenKurumlarDTO);
        dbEvrakGelenKurumlarService.update(updatedEvrakGelenKurumlar);
        log.info("Evrak gelen kurum güncellendi: {}", updatedEvrakGelenKurumlar.getId());
        return evrakGelenKurumlarMapper.toDto(updatedEvrakGelenKurumlar);
    }

    /**
     * Delete EvrakGelenKurumlar
     * @param id EvrakGelenKurumlar id
     */
    public void delete(Long id) {
        EvrakGelenKurumlar evrakGelenKurumlar = dbEvrakGelenKurumlarService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak gelen kurum bulunamadı: " + id));
        dbEvrakGelenKurumlarService.delete(evrakGelenKurumlar);
        log.info("Evrak gelen kurum silindi: {}", id);
    }
}
