package iym.makos.mapper;

import iym.common.model.entity.iym.talep.HedeflerTalep;
import org.springframework.stereotype.Component;
import iym.makos.model.dto.db.HedeflerTalepDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for HedeflerTalep entity and DTO
 */
@Component
public class HedeflerTalepMapper {

    /**
     * Convert entity to DTO
     * @param entity HedeflerTalep entity
     * @return HedeflerTalepDTO
     */
    public HedeflerTalepDTO toDto(HedeflerTalep entity) {
        if (entity == null) {
            return null;
        }

        return HedeflerTalepDTO.builder()
                .id(entity.getId())
                .birimKod(entity.getBirimKod())
                .kullaniciId(entity.getKullaniciId())
                .tekMasaKulId(entity.getTekMasaKulId())
                .hedefNo(entity.getHedefNo())
                .hedefAdi(entity.getHedefAdi())
                .hedefSoyadi(entity.getHedefSoyadi())
                .baslamaTarihi(entity.getBaslamaTarihi())
                .suresi(entity.getSuresi())
                .sureTipi(entity.getSureTipi())
                .uzatmaSayisi(entity.getUzatmaSayisi())
                .durumu(entity.getDurumu())
                .aciklama(entity.getAciklama())
                .mahkemeKararId(entity.getMahkemeKararTalepId())
                .hedefAidiyatId(entity.getHedefAidiyatId())
                .grupKod(entity.getGrupKod())
                .aidiyatKod(entity.getAidiyatKod())
                .uniqKod(entity.getUniqKod())
                .kayitTarihi(entity.getKayitTarihi())
                .tanimlamaTarihi(entity.getTanimlamaTarihi())
                .kapatmaKararId(entity.getKapatmaKararId())
                .kapatmaTarihi(entity.getKapatmaTarihi())
                .imha(entity.getImha())
                .imhaTarihi(entity.getImhaTarihi())
                .uzatmaId(entity.getUzatmaId())
                .acilmi(entity.getAcilmi())
                .hedef118Adi(entity.getHedef118Adi())
                .hedef118Soyadi(entity.getHedef118Soyadi())
                .hedef118Adres(entity.getHedef118Adres())
                .hedefTipi(entity.getHedefTipi())
                .canakNo(entity.getCanakNo())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto HedeflerTalepDTO
     * @return HedeflerTalep entity
     */
    public HedeflerTalep toEntity(HedeflerTalepDTO dto) {
        if (dto == null) {
            return null;
        }

        return HedeflerTalep.builder()
                .id(dto.getId())
                .birimKod(dto.getBirimKod())
                .kullaniciId(dto.getKullaniciId())
                .tekMasaKulId(dto.getTekMasaKulId())
                .hedefNo(dto.getHedefNo())
                .hedefAdi(dto.getHedefAdi())
                .hedefSoyadi(dto.getHedefSoyadi())
                .baslamaTarihi(dto.getBaslamaTarihi())
                .suresi(dto.getSuresi())
                .sureTipi(dto.getSureTipi())
                .uzatmaSayisi(dto.getUzatmaSayisi())
                .durumu(dto.getDurumu())
                .aciklama(dto.getAciklama())
                .mahkemeKararTalepId(dto.getMahkemeKararId())
                .hedefAidiyatId(dto.getHedefAidiyatId())
                .grupKod(dto.getGrupKod())
                .aidiyatKod(dto.getAidiyatKod())
                .uniqKod(dto.getUniqKod())
                .kayitTarihi(dto.getKayitTarihi())
                .tanimlamaTarihi(dto.getTanimlamaTarihi())
                .kapatmaKararId(dto.getKapatmaKararId())
                .kapatmaTarihi(dto.getKapatmaTarihi())
                .imha(dto.getImha())
                .imhaTarihi(dto.getImhaTarihi())
                .uzatmaId(dto.getUzatmaId())
                .acilmi(dto.getAcilmi())
                .hedef118Adi(dto.getHedef118Adi())
                .hedef118Soyadi(dto.getHedef118Soyadi())
                .hedef118Adres(dto.getHedef118Adres())
                .hedefTipi(dto.getHedefTipi())
                .canakNo(dto.getCanakNo())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity HedeflerTalep entity to update
     * @param dto HedeflerTalepDTO with new values
     * @return Updated HedeflerTalep entity
     */
    public HedeflerTalep updateEntityFromDto(HedeflerTalep entity, HedeflerTalepDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setBirimKod(dto.getBirimKod());
        entity.setKullaniciId(dto.getKullaniciId());
        entity.setTekMasaKulId(dto.getTekMasaKulId());
        entity.setHedefNo(dto.getHedefNo());
        entity.setHedefAdi(dto.getHedefAdi());
        entity.setHedefSoyadi(dto.getHedefSoyadi());
        entity.setBaslamaTarihi(dto.getBaslamaTarihi());
        entity.setSuresi(dto.getSuresi());
        entity.setSureTipi(dto.getSureTipi());
        entity.setUzatmaSayisi(dto.getUzatmaSayisi());
        entity.setDurumu(dto.getDurumu());
        entity.setAciklama(dto.getAciklama());
        entity.setMahkemeKararTalepId(dto.getMahkemeKararId());
        entity.setHedefAidiyatId(dto.getHedefAidiyatId());
        entity.setGrupKod(dto.getGrupKod());
        entity.setAidiyatKod(dto.getAidiyatKod());
        entity.setUniqKod(dto.getUniqKod());
        entity.setKayitTarihi(dto.getKayitTarihi());
        entity.setTanimlamaTarihi(dto.getTanimlamaTarihi());
        entity.setKapatmaKararId(dto.getKapatmaKararId());
        entity.setKapatmaTarihi(dto.getKapatmaTarihi());
        entity.setImha(dto.getImha());
        entity.setImhaTarihi(dto.getImhaTarihi());
        entity.setUzatmaId(dto.getUzatmaId());
        entity.setAcilmi(dto.getAcilmi());
        entity.setHedef118Adi(dto.getHedef118Adi());
        entity.setHedef118Soyadi(dto.getHedef118Soyadi());
        entity.setHedef118Adres(dto.getHedef118Adres());
        entity.setHedefTipi(dto.getHedefTipi());
        entity.setCanakNo(dto.getCanakNo());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of HedeflerTalep entities
     * @return List of HedeflerTalepDTO
     */
    public List<HedeflerTalepDTO> toDtoList(List<HedeflerTalep> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of HedeflerTalepDTO
     * @return List of HedeflerTalep entities
     */
    public List<HedeflerTalep> toEntityList(List<HedeflerTalepDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
