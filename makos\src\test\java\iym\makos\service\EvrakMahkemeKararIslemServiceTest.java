package iym.makos.service;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.EvrakMahkemeKararIslem;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbEvrakMahkemeKararIslemService;
import iym.makos.mapper.EvrakMahkemeKararIslemMapper;
import iym.makos.service.db.EvrakMahkemeKararIslemService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.model.dto.db.EvrakMahkemeKararIslemDTO;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EvrakMahkemeKararIslemServiceTest {

    @Mock
    private DbEvrakMahkemeKararIslemService dbEvrakMahkemeKararIslemService;

    @Mock
    private DbEvrakKayitService dbEvrakKayitService;

    @Mock
    private EvrakMahkemeKararIslemMapper evrakMahkemeKararIslemMapper;

    @InjectMocks
    private EvrakMahkemeKararIslemService evrakMahkemeKararIslemService;

    private EvrakMahkemeKararIslem evrakMahkemeKararIslem;
    private EvrakMahkemeKararIslemDTO evrakMahkemeKararIslemDTO;
    private EvrakKayit evrakKayit;
    private List<EvrakMahkemeKararIslem> evrakMahkemeKararIslemList;
    private List<EvrakMahkemeKararIslemDTO> evrakMahkemeKararIslemDTOList;

    @BeforeEach
    void setUp() {
        evrakKayit = EvrakKayit.builder()
                .id(1L)
                .evrakGeldigiKurumKodu("01")
                .build();

        evrakMahkemeKararIslem = EvrakMahkemeKararIslem.builder()
                .evrakId(1L)
                .kurum("01")
                .seviye("0")
                .build();

        EvrakMahkemeKararIslem evrakMahkemeKararIslem2 = EvrakMahkemeKararIslem.builder()
                .evrakId(2L)
                .kurum("02")
                .seviye("1")
                .build();

        evrakMahkemeKararIslemDTO = EvrakMahkemeKararIslemDTO.builder()
                .evrakId(1L)
                .kurum("01")
                .seviye("0")
                .build();

        EvrakMahkemeKararIslemDTO evrakMahkemeKararIslemDTO2 = EvrakMahkemeKararIslemDTO.builder()
                .evrakId(2L)
                .kurum("02")
                .seviye("1")
                .build();

        evrakMahkemeKararIslemList = Arrays.asList(evrakMahkemeKararIslem, evrakMahkemeKararIslem2);
        evrakMahkemeKararIslemDTOList = Arrays.asList(evrakMahkemeKararIslemDTO, evrakMahkemeKararIslemDTO2);
    }

    @Test
    void findAll_shouldReturnAllEvrakMahkemeKararIslem() {
        // Given
        when(dbEvrakMahkemeKararIslemService.findAll()).thenReturn(evrakMahkemeKararIslemList);
        when(evrakMahkemeKararIslemMapper.toDtoList(evrakMahkemeKararIslemList)).thenReturn(evrakMahkemeKararIslemDTOList);

        // When
        List<EvrakMahkemeKararIslemDTO> result = evrakMahkemeKararIslemService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(evrakMahkemeKararIslemDTOList);
        verify(dbEvrakMahkemeKararIslemService).findAll();
        verify(evrakMahkemeKararIslemMapper).toDtoList(evrakMahkemeKararIslemList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfEvrakMahkemeKararIslem() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<EvrakMahkemeKararIslem> evrakMahkemeKararIslemPage = new PageImpl<>(evrakMahkemeKararIslemList, pageable, evrakMahkemeKararIslemList.size());
        
        when(dbEvrakMahkemeKararIslemService.findAll(pageable)).thenReturn(evrakMahkemeKararIslemPage);
        when(evrakMahkemeKararIslemMapper.toDtoList(evrakMahkemeKararIslemList)).thenReturn(evrakMahkemeKararIslemDTOList);

        // When
        Page<EvrakMahkemeKararIslemDTO> result = evrakMahkemeKararIslemService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(evrakMahkemeKararIslemDTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbEvrakMahkemeKararIslemService).findAll(pageable);
        verify(evrakMahkemeKararIslemMapper).toDtoList(evrakMahkemeKararIslemList);
    }

    @Test
    void findById_shouldReturnEvrakMahkemeKararIslem_whenExists() {
        // Given
        when(dbEvrakMahkemeKararIslemService.findById(1L)).thenReturn(Optional.of(evrakMahkemeKararIslem));
        when(evrakMahkemeKararIslemMapper.toDto(evrakMahkemeKararIslem)).thenReturn(evrakMahkemeKararIslemDTO);

        // When
        EvrakMahkemeKararIslemDTO result = evrakMahkemeKararIslemService.findById(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(evrakMahkemeKararIslemDTO);
        verify(dbEvrakMahkemeKararIslemService).findById(1L);
        verify(evrakMahkemeKararIslemMapper).toDto(evrakMahkemeKararIslem);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        when(dbEvrakMahkemeKararIslemService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakMahkemeKararIslemService.findById(1L));
        verify(dbEvrakMahkemeKararIslemService).findById(1L);
        verify(evrakMahkemeKararIslemMapper, never()).toDto(any());
    }

    @Test
    void findByKurum_shouldReturnEvrakMahkemeKararIslemList() {
        // Given
        when(dbEvrakMahkemeKararIslemService.findByKurum("01")).thenReturn(Arrays.asList(evrakMahkemeKararIslem));
        when(evrakMahkemeKararIslemMapper.toDtoList(Arrays.asList(evrakMahkemeKararIslem))).thenReturn(Arrays.asList(evrakMahkemeKararIslemDTO));

        // When
        List<EvrakMahkemeKararIslemDTO> result = evrakMahkemeKararIslemService.findByKurum("01");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(evrakMahkemeKararIslemDTO);
        verify(dbEvrakMahkemeKararIslemService).findByKurum("01");
        verify(evrakMahkemeKararIslemMapper).toDtoList(Arrays.asList(evrakMahkemeKararIslem));
    }

    @Test
    void create_shouldCreateEvrakMahkemeKararIslem() {
        // Given
        when(dbEvrakKayitService.findById(1L)).thenReturn(Optional.of(evrakKayit));
        when(dbEvrakMahkemeKararIslemService.findById(1L)).thenReturn(Optional.empty());
        when(evrakMahkemeKararIslemMapper.toEntity(evrakMahkemeKararIslemDTO)).thenReturn(evrakMahkemeKararIslem);
        when(evrakMahkemeKararIslemMapper.toDto(evrakMahkemeKararIslem)).thenReturn(evrakMahkemeKararIslemDTO);

        // When
        EvrakMahkemeKararIslemDTO result = evrakMahkemeKararIslemService.create(evrakMahkemeKararIslemDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(evrakMahkemeKararIslemDTO);
        verify(dbEvrakKayitService).findById(1L);
        verify(dbEvrakMahkemeKararIslemService).findById(1L);
        verify(evrakMahkemeKararIslemMapper).toEntity(evrakMahkemeKararIslemDTO);
        verify(dbEvrakMahkemeKararIslemService).save(evrakMahkemeKararIslem);
        verify(evrakMahkemeKararIslemMapper).toDto(evrakMahkemeKararIslem);
    }

    @Test
    void create_shouldThrowException_whenEvrakNotExists() {
        // Given
        when(dbEvrakKayitService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakMahkemeKararIslemService.create(evrakMahkemeKararIslemDTO));
        verify(dbEvrakKayitService).findById(1L);
        verify(dbEvrakMahkemeKararIslemService, never()).findById(anyLong());
        verify(evrakMahkemeKararIslemMapper, never()).toEntity(any());
        verify(dbEvrakMahkemeKararIslemService, never()).save(any());
        verify(evrakMahkemeKararIslemMapper, never()).toDto(any());
    }

    @Test
    void create_shouldThrowException_whenEvrakMahkemeKararIslemAlreadyExists() {
        // Given
        when(dbEvrakKayitService.findById(1L)).thenReturn(Optional.of(evrakKayit));
        when(dbEvrakMahkemeKararIslemService.findById(1L)).thenReturn(Optional.of(evrakMahkemeKararIslem));

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakMahkemeKararIslemService.create(evrakMahkemeKararIslemDTO));
        verify(dbEvrakKayitService).findById(1L);
        verify(dbEvrakMahkemeKararIslemService).findById(1L);
        verify(evrakMahkemeKararIslemMapper, never()).toEntity(any());
        verify(dbEvrakMahkemeKararIslemService, never()).save(any());
        verify(evrakMahkemeKararIslemMapper, never()).toDto(any());
    }

    @Test
    void update_shouldUpdateEvrakMahkemeKararIslem() {
        // Given
        when(dbEvrakMahkemeKararIslemService.findById(1L)).thenReturn(Optional.of(evrakMahkemeKararIslem));
        when(evrakMahkemeKararIslemMapper.updateEntityFromDto(evrakMahkemeKararIslem, evrakMahkemeKararIslemDTO)).thenReturn(evrakMahkemeKararIslem);
        when(evrakMahkemeKararIslemMapper.toDto(evrakMahkemeKararIslem)).thenReturn(evrakMahkemeKararIslemDTO);

        // When
        EvrakMahkemeKararIslemDTO result = evrakMahkemeKararIslemService.update(1L, evrakMahkemeKararIslemDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(evrakMahkemeKararIslemDTO);
        verify(dbEvrakMahkemeKararIslemService).findById(1L);
        verify(evrakMahkemeKararIslemMapper).updateEntityFromDto(evrakMahkemeKararIslem, evrakMahkemeKararIslemDTO);
        verify(dbEvrakMahkemeKararIslemService).update(evrakMahkemeKararIslem);
        verify(evrakMahkemeKararIslemMapper).toDto(evrakMahkemeKararIslem);
    }

    @Test
    void update_shouldThrowException_whenEvrakIdMismatch() {
        // Given
        EvrakMahkemeKararIslemDTO mismatchDTO = EvrakMahkemeKararIslemDTO.builder()
                .evrakId(2L)
                .kurum("01")
                .seviye("0")
                .build();

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakMahkemeKararIslemService.update(1L, mismatchDTO));
        verify(dbEvrakMahkemeKararIslemService, never()).findById(anyLong());
        verify(evrakMahkemeKararIslemMapper, never()).updateEntityFromDto(any(), any());
        verify(dbEvrakMahkemeKararIslemService, never()).update(any());
        verify(evrakMahkemeKararIslemMapper, never()).toDto(any());
    }

    @Test
    void update_shouldThrowException_whenEvrakMahkemeKararIslemNotExists() {
        // Given
        when(dbEvrakMahkemeKararIslemService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakMahkemeKararIslemService.update(1L, evrakMahkemeKararIslemDTO));
        verify(dbEvrakMahkemeKararIslemService).findById(1L);
        verify(evrakMahkemeKararIslemMapper, never()).updateEntityFromDto(any(), any());
        verify(dbEvrakMahkemeKararIslemService, never()).update(any());
        verify(evrakMahkemeKararIslemMapper, never()).toDto(any());
    }

    @Test
    void delete_shouldDeleteEvrakMahkemeKararIslem() {
        // Given
        when(dbEvrakMahkemeKararIslemService.findById(1L)).thenReturn(Optional.of(evrakMahkemeKararIslem));

        // When
        evrakMahkemeKararIslemService.delete(1L);

        // Then
        verify(dbEvrakMahkemeKararIslemService).findById(1L);
        verify(dbEvrakMahkemeKararIslemService).delete(evrakMahkemeKararIslem);
    }

    @Test
    void delete_shouldThrowException_whenEvrakMahkemeKararIslemNotExists() {
        // Given
        when(dbEvrakMahkemeKararIslemService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakMahkemeKararIslemService.delete(1L));
        verify(dbEvrakMahkemeKararIslemService).findById(1L);
        verify(dbEvrakMahkemeKararIslemService, never()).delete(any());
    }
}
