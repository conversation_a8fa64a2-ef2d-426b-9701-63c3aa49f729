package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

/**
 * DTO for Iller entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Schema(description = "Mahkeme Bilgi")
public class MahkemeDTO {

    public String mahkemeKodu;

    public String mahkemeAdi;

    private String ilIlceKodu;

    private String mahkemeTuruKodu;

    private String mahkemeSayi;

    private Date eklemeTarihi;

    private Long ekleyenKullaniciId;

    private String silindi;

}
