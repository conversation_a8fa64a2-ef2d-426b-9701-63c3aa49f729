package iym.makos.mapper;

import iym.common.model.entity.iym.EvrakKayit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.model.dto.db.EvrakKayitDTO;


import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class EvrakKayitMapperTest {

    private EvrakKayitMapper evrakKayitMapper;
    private EvrakKayit evrakKayit;
    private EvrakKayitDTO evrakKayitDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        evrakKayitMapper = new EvrakKayitMapper();
        testDate = new Date();

        evrakKayit = EvrakKayit.builder()
                .id(1L)
                .evrakSiraNo("TEST-001")
                .evrakNo("TEST-EVRAK-001")
                .girisTarih(testDate)
                .evrakTarihi(testDate)
                .evrakGeldigiKurumKodu("02")
                .kayKullaniciId(1L)
                .evrakTipi("ILETISIMIN_DENETLENMESI")
                .havaleBirim("02")
                .aciklama("Test açıklama")
                .geldigiIlIlceKodu("0600")
                .evrakKonusu("Test konu")
                .arsivDosyaNo("ARŞ-001")
                .durumu("AKTIF")
                .evrakYonu("GELEN")
                .onayTarihi(testDate)
                .acilmi("H")
                .sorusturmaNo("SOR-001")
                .mahkemeKararNo("MK-001")
                .build();

        evrakKayitDTO = EvrakKayitDTO.builder()
                .id(1L)
                .evrakSiraNo("TEST-001")
                .evrakNo("TEST-EVRAK-001")
                .girisTarih(testDate)
                .evrakTarihi(testDate)
                .evrakGeldigiKurum("02")
                .kayKullanici(1L)
                .evrakTipi("ILETISIMIN_DENETLENMESI")
                .havaleBirim("02")
                .aciklama("Test açıklama")
                .gelIl("0600")
                .evrakKonusu("Test konu")
                .arsivDosyaNo("ARŞ-001")
                .durumu("AKTIF")
                .evrakYonu("GELEN")
                .onayTarihi(testDate)
                .acilmi("H")
                .sorusturmaNo("SOR-001")
                .mahkemeKararNo("MK-001")
                .build();
    }

    @Test
    void toDto() {
        // When
        EvrakKayitDTO result = evrakKayitMapper.toDto(evrakKayit);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(evrakKayit.getId());
        assertThat(result.getEvrakSiraNo()).isEqualTo(evrakKayit.getEvrakSiraNo());
        assertThat(result.getEvrakNo()).isEqualTo(evrakKayit.getEvrakNo());
        assertThat(result.getGirisTarih()).isEqualTo(evrakKayit.getGirisTarih());
        assertThat(result.getEvrakTarihi()).isEqualTo(evrakKayit.getEvrakTarihi());
        assertThat(result.getEvrakGeldigiKurum()).isEqualTo(evrakKayit.getEvrakGeldigiKurumKodu());
        assertThat(result.getKayKullanici()).isEqualTo(evrakKayit.getKayKullaniciId());
        assertThat(result.getEvrakTipi()).isEqualTo(evrakKayit.getEvrakTipi());
        assertThat(result.getHavaleBirim()).isEqualTo(evrakKayit.getHavaleBirim());
        assertThat(result.getAciklama()).isEqualTo(evrakKayit.getAciklama());
        assertThat(result.getGelIl()).isEqualTo(evrakKayit.getGeldigiIlIlceKodu());
        assertThat(result.getEvrakKonusu()).isEqualTo(evrakKayit.getEvrakKonusu());
        assertThat(result.getArsivDosyaNo()).isEqualTo(evrakKayit.getArsivDosyaNo());
        assertThat(result.getDurumu()).isEqualTo(evrakKayit.getDurumu());
        assertThat(result.getEvrakYonu()).isEqualTo(evrakKayit.getEvrakYonu());
        assertThat(result.getOnayTarihi()).isEqualTo(evrakKayit.getOnayTarihi());
        assertThat(result.getAcilmi()).isEqualTo(evrakKayit.getAcilmi());
        assertThat(result.getSorusturmaNo()).isEqualTo(evrakKayit.getSorusturmaNo());
        assertThat(result.getMahkemeKararNo()).isEqualTo(evrakKayit.getMahkemeKararNo());
    }

    @Test
    void toDtoWithNullEntity() {
        // When
        //EvrakKayitDTO result = evrakKayitMapper.toDto(null);

        // Then
        //assertThat(result).isNull();
    }

    @Test
    void toEntity() {
        // When
        EvrakKayit result = evrakKayitMapper.toEntity(evrakKayitDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(evrakKayitDTO.getId());
        assertThat(result.getEvrakSiraNo()).isEqualTo(evrakKayitDTO.getEvrakSiraNo());
        assertThat(result.getEvrakNo()).isEqualTo(evrakKayitDTO.getEvrakNo());
        assertThat(result.getGirisTarih()).isEqualTo(evrakKayitDTO.getGirisTarih());
        assertThat(result.getEvrakTarihi()).isEqualTo(evrakKayitDTO.getEvrakTarihi());
        assertThat(result.getEvrakGeldigiKurumKodu()).isEqualTo(evrakKayitDTO.getEvrakGeldigiKurum());
        assertThat(result.getKayKullaniciId()).isEqualTo(evrakKayitDTO.getKayKullanici());
        assertThat(result.getEvrakTipi()).isEqualTo(evrakKayitDTO.getEvrakTipi());
        assertThat(result.getHavaleBirim()).isEqualTo(evrakKayitDTO.getHavaleBirim());
        assertThat(result.getAciklama()).isEqualTo(evrakKayitDTO.getAciklama());
        assertThat(result.getGeldigiIlIlceKodu()).isEqualTo(evrakKayitDTO.getGelIl());
        assertThat(result.getEvrakKonusu()).isEqualTo(evrakKayitDTO.getEvrakKonusu());
        assertThat(result.getArsivDosyaNo()).isEqualTo(evrakKayitDTO.getArsivDosyaNo());
        assertThat(result.getDurumu()).isEqualTo(evrakKayitDTO.getDurumu());
        assertThat(result.getEvrakYonu()).isEqualTo(evrakKayitDTO.getEvrakYonu());
        assertThat(result.getOnayTarihi()).isEqualTo(evrakKayitDTO.getOnayTarihi());
        assertThat(result.getAcilmi()).isEqualTo(evrakKayitDTO.getAcilmi());
        assertThat(result.getSorusturmaNo()).isEqualTo(evrakKayitDTO.getSorusturmaNo());
        assertThat(result.getMahkemeKararNo()).isEqualTo(evrakKayitDTO.getMahkemeKararNo());
    }

    @Test
    void toEntityWithNullDto() {
        // When
        EvrakKayit result = evrakKayitMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto() {
        // Given
        EvrakKayit existingEntity = EvrakKayit.builder()
                .id(1L)
                .evrakSiraNo("OLD-001")
                .evrakNo("OLD-EVRAK-001")
                .build();

        // When
        EvrakKayit result = evrakKayitMapper.updateEntityFromDto(existingEntity, evrakKayitDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L); // ID should not change
        assertThat(result.getEvrakSiraNo()).isEqualTo(evrakKayitDTO.getEvrakSiraNo());
        assertThat(result.getEvrakNo()).isEqualTo(evrakKayitDTO.getEvrakNo());
        assertThat(result.getGirisTarih()).isEqualTo(evrakKayitDTO.getGirisTarih());
        assertThat(result.getEvrakTarihi()).isEqualTo(evrakKayitDTO.getEvrakTarihi());
    }

    @Test
    void updateEntityFromDtoWithNullDto() {
        // Given
        EvrakKayit existingEntity = EvrakKayit.builder()
                .id(1L)
                .evrakSiraNo("OLD-001")
                .evrakNo("OLD-EVRAK-001")
                .build();

        // When
        EvrakKayit result = evrakKayitMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity); // Should return the original entity unchanged
    }

    @Test
    void toDtoList() {
        // Given
        List<EvrakKayit> entities = Arrays.asList(
            evrakKayit,
            EvrakKayit.builder().id(2L).evrakSiraNo("TEST-002").build()
        );

        // When
        List<EvrakKayitDTO> result = evrakKayitMapper.toDtoList(entities);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(1L);
        assertThat(result.get(1).getId()).isEqualTo(2L);
    }

    @Test
    void toDtoListWithNullList() {
        // When
        List<EvrakKayitDTO> result = evrakKayitMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }
}
