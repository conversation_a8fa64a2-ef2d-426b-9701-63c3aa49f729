package iym.makos.model.dto.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for Iller entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Mahkeme Karar  bilgilerini içerir")
public class MahkemeKararDTO {

    @Schema(description = "Mahkeme karar ID")
    private Long id;

    @Schema(description = "İlişkili evrak ID")
    @NotNull(message = "Evrak ID boş olamaz")
    private Long evrakId;

    @Schema(description = "Kaydeden kullanıcı ID")
    @NotNull(message = "Kullanıcı ID boş olamaz")
    private Long kullaniciId;

    @Schema(description = "Kayıt tarihi", example = "2023-05-15T10:30:00")
    @NotNull(message = "Kayıt tarihi boş olamaz")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date kayitTarihi;

    @Schema(description = "Durum", example = "AKTIF")
    @Size(max = 20, message = "Durum 20 karakterden fazla olamaz")
    private String durum;

    @Schema(description = "Hukuk birim", example = "SULH CEZA")
    @Size(max = 50, message = "Hukuk birim 50 karakterden fazla olamaz")
    private String hukukBirim;

    @Schema(description = "Karar tipi", example = "300")
    @Size(max = 20, message = "Karar tipi 20 karakterden fazla olamaz")
    private String kararTip;

    @Schema(description = "Mahkeme karar başlangıç tarihi", example = "2023-05-10T14:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date mahKararBasTar;

    @Schema(description = "Mahkeme karar bitiş tarihi", example = "2023-06-10T14:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date mahKararBitisTar;

    @Schema(description = "Mahkeme adı", example = "ANKARA 1. SULH CEZA HAKİMLİĞİ")
    @Size(max = 250, message = "Mahkeme adı 250 karakterden fazla olamaz")
    private String mahkemeAdi;

    @Schema(description = "Mahkeme karar numarası", example = "2023/123")
    @Size(max = 50, message = "Mahkeme karar numarası 50 karakterden fazla olamaz")
    private String mahkemeKararNo;

    @Schema(description = "Mahkeme ili kodu", example = "0600")
    @NotNull(message = "Mahkeme ili boş olamaz")
    @Size(max = 4, message = "Mahkeme ili 4 karakterden fazla olamaz")
    private String mahkemeIlIlceKodu;

    @Schema(description = "Açıklama")
    @Size(max = 500, message = "Açıklama 500 karakterden fazla olamaz")
    private String aciklama;

    @Schema(description = "Hakim sicil numarası", example = "12345")
    @Size(max = 20, message = "Hakim sicil numarası 20 karakterden fazla olamaz")
    private String hakimSicilNo;

    @Schema(description = "Soruşturma numarası", example = "2023/456")
    @Size(max = 50, message = "Soruşturma numarası 50 karakterden fazla olamaz")
    private String sorusturmaNo;

    @Schema(description = "Mahkeme kodu", example = "SC01")
    @Size(max = 10, message = "Mahkeme kodu 10 karakterden fazla olamaz")
    private String mahkemeKodu;


}
