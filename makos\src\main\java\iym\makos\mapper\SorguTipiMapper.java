package iym.makos.mapper;

import iym.common.model.entity.iym.SorguTipleri;
import iym.common.model.entity.iym.SucTipi;
import iym.makos.model.dto.db.SorguTipiDTO;
import iym.makos.model.dto.db.SucTipiDTO;
import org.springframework.stereotype.Component;

/**
 * Mapper for MahkemeBilgiMapper entity and DTO
 */
@Component
public class SorguTipiMapper {


    public SorguTipiDTO toDto(SorguTipleri entity) {
        if (entity == null) {
            return null;
        }

        return SorguTipiDTO.builder()
                .sorguTipi(entity.getSorguTipi())
                .aciklama(entity.getAciklama())
                .build();
    }


    public SorguTipleri toEntity(SorguTipiDTO dto) {
        if (dto == null) {
            return null;
        }

        return SorguTipleri.builder()
                .sorguTipi(dto.getSorguTipi())
                .aciklama(dto.getAciklama())
                .build();
    }


}
