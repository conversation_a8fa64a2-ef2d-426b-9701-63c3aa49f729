-- Oracle TestContainer Minimal JDBC-Compatible Initialization
-- This script contains only basic SQL commands that work with JDBC
-- No SQL*Plus commands, no PL/SQL blocks with DBMS_OUTPUT

-- Create a simple table to verify container is working
CREATE TABLE CONTAINER_INIT_CHECK (
    ID NUMBER(1) PRIMARY KEY,
    STATUS VARCHAR2(20) DEFAULT 'READY',
    INIT_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert a test record
INSERT INTO CONTAINER_INIT_CHECK (ID, STATUS) VALUES (1, 'READY');

-- Commit the transaction
COMMIT;

-- Bu script Testcontainers tarafından SYSTEM kullanıcısı ile çalıştırılır.
-- Bu nedenle DBA yetkisi gerektiren işlemler burada yapılmalıdır.

-- IYM kullanıcısı zaten .withUsername("iym") ile oluşturuluyor.
-- Biz burada sadece tablespace ve yetkileri vereceğiz.

CREATE TABLESPACE iym_data
DATAFILE 'iym_data.dbf' SIZE 100M
AUTOEXTEND ON NEXT 50M MAXSIZE UNLIMITED;

-- Kullanıcıya oluşturulan tablespace'i varsayılan olarak ata ve kota ver
ALTER USER iym DEFAULT TABLESPACE iym_data QUOTA UNLIMITED ON iym_data;

-- Gerekli tüm yetkileri ver
GRANT CONNECT, RESOURCE, DBA TO iym;
GRANT CREATE SESSION TO iym;
GRANT CREATE TABLE TO iym;
GRANT CREATE VIEW TO iym;
GRANT CREATE SEQUENCE TO iym;
GRANT CREATE PROCEDURE TO iym;
GRANT CREATE TRIGGER TO iym;
GRANT CREATE TYPE TO iym;
GRANT CREATE SYNONYM TO iym;
GRANT UNLIMITED TABLESPACE TO iym;