-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;


-- Create TESPIT_TURLERI table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'SORGU_TIPLERI';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE '
        CREATE TABLE iym.SORGU_TIPLERI (
             SORGU_TIPI NUMBER NOT NULL,
             SORGU_ACIKLAMA VARCHAR2(1000) NOT NULL,
             CONSTRAINT SORGU_TIPLERI_PRK PRIMARY KEY (SORGU_TIPI)ENABLE
        )
    ';

  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.SORGU_TIPLERI;
  IF row_count = 0 THEN
    DECLARE
      suctipi_count NUMBER;
    BEGIN

    INSERT INTO SORGU_TIPLERI (SORGU_TIPI,SORGU_ACIKLAMA) VALUES (100,'TELEFON GÖRÜŞME SORGUSU') ;
    INSERT INTO SORGU_TIPLERI (SORGU_TIPI,SORGU_ACIKLAMA) VALUES (200,'IMEI GÖRÜŞME SORGUSU') ;
    INSERT INTO SORGU_TIPLERI (SORGU_TIPI,SORGU_ACIKLAMA) VALUES (300,'BAZ GÖRÜŞME SORGUSU') ;
    INSERT INTO SORGU_TIPLERI (SORGU_TIPI,SORGU_ACIKLAMA) VALUES (400,'NUMARA KULLANAN IMEI SORGUSU') ;
    INSERT INTO SORGU_TIPLERI (SORGU_TIPI,SORGU_ACIKLAMA) VALUES (500,'IMEI KULLANAN NUMARA SORGUSU') ;
    INSERT INTO SORGU_TIPLERI (SORGU_TIPI,SORGU_ACIKLAMA) VALUES (600,'TKART SORGUSU') ;
    INSERT INTO SORGU_TIPLERI (SORGU_TIPI,SORGU_ACIKLAMA) VALUES (700,'ABONE SORGUSU') ;

    END;
  END IF;
END;
/

COMMIT;
