package iym.makos.model.dto.auth;

import iym.common.validation.ValidationResult;
import iym.makos.model.MakosRequest;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class ChangePasswordRequest implements MakosRequest {
    
    @NotBlank(message = "Current password is required")
    private String currentPassword;
    
    @NotBlank(message = "New password is required")
    private String newPassword;
    
    @NotBlank(message = "Confirm password is required")
    private String confirmPassword;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if ChangePasswordRequest is valid");
        
        ValidationResult validationResult = new ValidationResult(true);
        
        try {
            if (currentPassword == null || currentPassword.trim().isEmpty()) {
                validationResult.addFailedReason("Current password cannot be null or empty");
            }
            if (newPassword == null || newPassword.trim().isEmpty()) {
                validationResult.addFailedReason("New password cannot be null or empty");
            }
            if (confirmPassword == null || confirmPassword.trim().isEmpty()) {
                validationResult.addFailedReason("Confirm password cannot be null or empty");
            }
            if (newPassword != null && confirmPassword != null && !newPassword.equals(confirmPassword)) {
                validationResult.addFailedReason("New password and confirm password must match");
            }
        } catch (Exception e) {
            log.error("Validation failed", e);
            validationResult.addFailedReason("Validation error: " + e.getMessage());
        }
        
        return validationResult;
    }
} 