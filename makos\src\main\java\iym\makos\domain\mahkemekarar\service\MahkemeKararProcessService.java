package iym.makos.domain.mahkemekarar.service;

import iym.makos.config.security.UserDetailsImpl;
import iym.makos.domain.mahkemekarar.processor.IMakosRequestProcessor;
import iym.makos.domain.mahkemekarar.processor.ProcessorFactory;
import iym.makos.model.dto.mahkemekarar.MahkemeKararRequest;
import iym.makos.model.dto.mahkemekarar.MahkemeKararResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MahkemeKararProcessService {
    private final ProcessorFactory processorFactory;

    public MahkemeKararProcessService(@Autowired ProcessorFactory processorFactory) {
        this.processorFactory = processorFactory;
    }

    @SuppressWarnings("unchecked")
    public <T extends MahkemeKararRequest, R extends MahkemeKararResponse> R process(T request, Class<R> responseType, UserDetailsImpl userDetails) {
        IMakosRequestProcessor<T, R> processor = processorFactory.getProcessor(request, responseType);
        if (processor != null)
            return processor.process(request, userDetails);
        throw new RuntimeException("No processor found for <" + request.getClass().getSimpleName() + ", " + responseType.getSimpleName() + ">");
    }

}
