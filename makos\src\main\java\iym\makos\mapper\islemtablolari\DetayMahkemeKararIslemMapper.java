package iym.makos.mapper.islemtablolari;

import iym.common.model.entity.iym.mk.DetayMahkemeKararIslem;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Mapper for MahkemeKararTalep entity and DTO
 */
@Component
public class DetayMahkemeKararIslemMapper {

    public DetayMahkemeKararIslem fromDetayMahkemeKararTalep(DetayMahkemeKararTalep entity){
        if (entity == null) {
            return null;
        }

        return DetayMahkemeKararIslem.builder()
                //.id(entity.getId())
                .kararTipDetay(entity.getKararTipDetay())
                .mahkemeKararTalepId(entity.getMahkemeKararTalepId())
                .mahkemeKararNo(entity.getMahkemeKararNoDetay())
                .iliskiliMahkemeKararId(entity.getIliskiliMahkemeKararId())
                .mahkemeAdiDetay(entity.getMahkemeAdiDetay())
                .mahkemeKodu(entity.getMahkemeKoduDetay())
                .sorusturmaNo(entity.getSorusturmaNoDetay())
                .mahkemeIlIlceKodu(entity.getMahkemeIlIlceKoduDetay())
                .kararTipDetay(entity.getKararTipDetay())
                .durum(entity.getDurum())
                .kayitTarihi(new Date())
                .evrakId(entity.getEvrakId())
                .aciklama(entity.getAciklama())
                .build();
    }




}
