package iym.makos.mapper;

import iym.common.model.entity.iym.sorgu.MahkemeKararSorguInfo;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mk.MahkemeKararSorguView;
import org.springframework.stereotype.Component;

/**
 * Mapper for MahkemeKararTalep entity and DTO
 */
@Component
public class MahkemeKararSorguMapper {

    public MahkemeKararSorguView toMahkemeKararSorguView(MahkemeKararSorguInfo entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeKararSorguView.builder()
                .mahkemeKararTalepId(entity.getId())
                .evrakId(entity.getEvrakId())
                .kaydedenKullaniciId(entity.getKaydedenKullaniciId())
                .kararKayitTarihi(entity.getKayitTarihi())
                .durumu(entity.getDurum())
                .mahkemeKararNo(entity.getMahkemeKararNo())
                .sorusturmaNo(entity.getSorusturmaNo())
                //.mahkemeIlIlceKodu(entity.geti)
                .aciklama(entity.getAciklama())
                .build();
    }


}
