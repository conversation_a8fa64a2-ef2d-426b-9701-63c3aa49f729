package iym.makos.controller;

import iym.common.enums.ResponseCode;
import iym.common.model.api.ApiResponse;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mk.id.IDEvrakDurumSorgulamaRequest;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mk.id.IDEvrakDurumSorgulamaResponse;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mk.IdEvrakDurumSorguView;
import iym.makos.service.db.MahkemeKararService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Audit Controller for MAKOS Module
 * Provides endpoints for viewing audit logs
 */
@RestController
@RequestMapping("/evrak")
@Slf4j
@Validated
public class EvrakController {

    @Autowired
    private MahkemeKararService mahkemeKararService;

    @PostMapping("/mahkemeKararSorgu")
    public ResponseEntity<IDEvrakDurumSorgulamaResponse> evrakDurumSorgu(
            @Valid @RequestBody IDEvrakDurumSorgulamaRequest sorguParam, Authentication authentication) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            List<IdEvrakDurumSorguView> sonucListesi = null;//not implemented yet

            return ResponseEntity.ok(IDEvrakDurumSorgulamaResponse.builder()
                    .evrakListesi(sonucListesi)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Sorgu Başarılı")
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("mahkemeKararSorgu process failed, requestId:{}", sorguParam.hashCode(), ex);
            IDEvrakDurumSorgulamaResponse response = IDEvrakDurumSorgulamaResponse.builder()
                    .evrakListesi(null)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Sorgu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }

}
