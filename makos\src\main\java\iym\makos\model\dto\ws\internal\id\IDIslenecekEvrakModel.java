package iym.makos.model.dto.ws.internal.id;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description =  "İşlenecek Denetleme Evrak Bilgisi")
public class IDIslenecekEvrakModel {

    @Schema(description = "Evrak Id")
    private Long evrakId;

    @Schema(description = "Evrak Sıra No")
    private String evrakSiraNo;

    @Schema(description = "Kurum Evrak No")
    private String evrakNo;

    @Schema(description = "Evrak Giriş Tarihi")
    private Date evrakGirişTarihi;

    @Schema(description = "Kurum Evrak Tarihi")
    private Date evrakTarihi;

    @Schema(description = "Evrak İl/İlçe Kodu")
    private String evrakIlIlceKodu;

    @Schema(description = "Evrak İl/İlçe Adı")
    private String evraklIlceAdi;

    @Schema(description = "Evrak Kurum Kodu")
    private String evrakKurumKodu;

    @Schema(description = "Evrak Kurum Adı")
    private String evrakKurumAdı;

    @Schema(description = "Kurum Evrak No")
    private boolean acil;

    @Schema(description = "Açıklama")
    private String aciklama;

    @Schema(description = "Mahkeme Karar Talep Id")
    private Long mahkemeKararTalepId;


    @Schema(description = "Atayan Kullanıcı Id")
    private Long atayanKullaniciId;

    @Schema(description = "Atayan KullanıcıAdi")
    private String atayanAdiSoyadi;


    @Schema(description = "Atanan Kullanıcı Id")
    private Long atananKullaniciId;

    @Schema(description = "Atanan KullanıcıAdi")
    private String atananAdiSoyadi;


    @Schema(description = "Soruşturma No")
    private String sorusturmaNo;

    @Schema(description = "Mahkeme Karar No")
    private String mahkemeKararNo;

    @Schema(description = "Mahkeme Kodu")
    private String mahkemeKodu;

    @Schema(description = "Mahkeme Adı")
    private String mahkemeAdi;

}

