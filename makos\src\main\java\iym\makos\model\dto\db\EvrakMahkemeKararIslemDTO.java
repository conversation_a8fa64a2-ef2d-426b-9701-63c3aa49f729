package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for EvrakMahkemeKararIslem entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Evrak Mahkeme Karar İşlem bilgilerini içerir")
public class EvrakMahkemeKararIslemDTO {

    @Schema(description = "Evrak ID", example = "1")
    @NotNull(message = "Evrak ID boş olamaz")
    private Long evrakId;

    @Schema(description = "Kurum kodu", example = "01")
    @Size(max = 10, message = "Kurum kodu 10 karakterden fazla olamaz")
    private String kurum;

    @Schema(description = "Seviye", example = "0")
    @Size(max = 1, message = "Seviye 1 karakterden fazla olamaz")
    private String seviye;
}
