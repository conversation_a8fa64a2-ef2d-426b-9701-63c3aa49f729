package iym.makos.integration;

import iym.common.testcontainer.AbstractOracleTestContainer;
import iym.common.testcontainer.OracleTestContainerConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for all production SQL files in docker/oracle/init directory.
 * <p>
 * This test class validates that all SQL files used by Makos module can be executed
 * successfully against Oracle TestContainer without errors.
 * <p>
 * Features:
 * - Tests all .sql files recursively from docker/oracle/init
 * - Uses Oracle TestContainer with same image as production
 * - Parametrized tests for each SQL file
 * - Detailed error reporting for failed SQL files
 * - Excludes healthcheck.sql (not a schema file)
 * <p>
 * Purpose:
 * - Detect SQL syntax errors early in Makos database schema
 * - Validate schema compatibility with Oracle
 * - Ensure all production scripts execute successfully
 * - Prevent deployment issues due to SQL errors
 *
 * <AUTHOR> Team
 */
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@DataJpaTest
@Import(OracleTestContainerConfiguration.class)
@Testcontainers
@ActiveProfiles("testcontainers-oracle")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS) // 🔧 REQUIRED: For non-static @BeforeAll methods
@DisplayName("Makos - Oracle Production SQL Files Integration Tests")
@Slf4j
public class OracleProductionSqlFilesIntegrationTest extends AbstractOracleTestContainer {

    private static List<Path> sqlFiles;


    @BeforeAll
    static void setupSqlFiles() throws IOException {
        log.info("🔍 Scanning for production SQL files in: {}", SQL_FILES_BASE_PATH);

        // Get current working directory
        Path currentDir = Paths.get(System.getProperty("user.dir"));
        log.debug("Current working directory: {}", currentDir);

        Path sqlDirectory;

        // Check if we're in makos directory, then go up to project root
        if (currentDir.getFileName().toString().equals("makos")) {
            sqlDirectory = currentDir.getParent().resolve(SQL_FILES_BASE_PATH);
        } else {
            // We're already in project root
            sqlDirectory = currentDir.resolve(SQL_FILES_BASE_PATH);
        }

        log.debug("Looking for SQL directory at: {}", sqlDirectory);

        if (!Files.exists(sqlDirectory)) {
            throw new IllegalStateException("SQL directory not found: " + sqlDirectory + ". Current dir: " + currentDir + ". Expected path: " + SQL_FILES_BASE_PATH);
        }

        // Find all .sql files recursively, excluding healthcheck.sql
        try (Stream<Path> paths = Files.walk(sqlDirectory)) {
            sqlFiles = paths
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().toLowerCase().endsWith(".sql"))
                    .filter(path -> !path.getFileName().toString().equals("healthcheck.sql"))
                    .sorted()
                    .toList();
        }

        log.info("📋 Found {} production SQL files to test", sqlFiles.size());
        sqlFiles.forEach(file -> log.debug("  📄 {}", file));

        if (sqlFiles.isEmpty()) {
            throw new IllegalStateException("No SQL files found in: " + sqlDirectory);
        }
    }

//    @BeforeAll
//    static void setupDataSource() {
//        // Get DataSource from the test container
//        dataSource = createTestDataSource();
//        log.info("✅ Oracle TestContainer DataSource configured for Makos");
//    }

    /**
     * Provides SQL file paths for parametrized tests
     */
    static Stream<Path> provideSqlFiles() {
        return sqlFiles.stream();
    }

    @Test
    @DisplayName("🔍 Should find production SQL files for Makos")
    void shouldFindProductionSqlFiles() {
        assertNotNull(sqlFiles, "SQL files list should not be null");
        assertFalse(sqlFiles.isEmpty(), "Should find at least one SQL file");

        log.info("✅ Found {} SQL files for Makos testing", sqlFiles.size());

        // Verify some expected files exist
        boolean hasCreateSchema = sqlFiles.stream().anyMatch(path -> path.getFileName().toString().contains("create_schema"));
        assertTrue(hasCreateSchema, "Should find create_schema.sql file");

        // Verify makos-related files exist
        boolean hasMakosRelatedFiles = sqlFiles.stream().anyMatch(path -> path.getFileName().toString().toLowerCase().contains("kullanici") || path.getFileName().toString().toLowerCase().contains("evrak") || path.getFileName().toString().toLowerCase().contains("mahkeme"));
        assertTrue(hasMakosRelatedFiles, "Should find Makos-related SQL files");
    }

    @ParameterizedTest(name = "📄 {0}")
    @MethodSource("provideSqlFiles")
    @DisplayName("🧪 Should execute SQL file successfully")
    void shouldExecuteSqlFileSuccessfully(Path sqlFile) throws SQLException {
        log.info("🧪 Testing SQL file: {}", sqlFile.getFileName());

        try {
            String sqlContent = Files.readString(sqlFile);
            assertNotNull(sqlContent, "SQL content should not be null");
            assertFalse(sqlContent.trim().isEmpty(), "SQL content should not be empty");

            executeAsJdbcStatements(sqlFile);

        } catch (IOException e) {
            fail("Failed to read SQL file: " + sqlFile + " - " + e.getMessage());
        } catch (SQLException e) {
            fail("SQL execution failed for file: " + sqlFile.getFileName() + "\nSQL Error: " + e.getMessage() + "\nError Code: " + e.getErrorCode() + "\nSQL State: " + e.getSQLState());
            throw e;
        } catch (Exception e) {
            fail("Unexpected error executing SQL file: " + sqlFile.getFileName() + " - " + e.getMessage());
        }
    }

//    /**
//     * Creates a test DataSource from the Oracle TestContainer
//     */
//    private static DataSource createTestDataSource() {
//        return org.springframework.boot.jdbc.DataSourceBuilder.create().url(ORACLE_CONTAINER.getJdbcUrl()).username(ORACLE_CONTAINER.getUsername()).password(ORACLE_CONTAINER.getPassword()).driverClassName("oracle.jdbc.OracleDriver").build();
//    }

    @Test
    @DisplayName("🔗 Should connect to Oracle TestContainer")
    void shouldConnectToOracleTestContainer() throws SQLException {
        log.info("🚀 [OracleProductionSqlFilesIntegrationTest] Starting connection test...");

        // 🔧 DEBUGGING: Log connection pool status before test
        logConnectionPoolStatus("Before OracleProductionSqlFilesIntegrationTest");

        assertNotNull(dataSource, "DataSource should not be null");

        try (Connection connection = dataSource.getConnection()) {
            assertNotNull(connection, "Connection should not be null");
            assertFalse(connection.isClosed(), "Connection should be open");

            // 🔧 DEBUGGING: Log connection details
            log.info("🔍 [CONNECTION DEBUG] Connection Class: {}", connection.getClass().getName());
            log.info("🔍 [CONNECTION DEBUG] Connection String: {}", connection.toString());

            // Test basic query and get Oracle connection details
            try (Statement statement = connection.createStatement()) {
                // Get Oracle connection ID
                var connIdRs = statement.executeQuery("SELECT SYS_CONTEXT('USERENV', 'SID') as SID, " +
                        "SYS_CONTEXT('USERENV', 'SESSIONID') as SESSION_ID, " +
                        "SYS_CONTEXT('USERENV', 'INSTANCE') as INSTANCE FROM DUAL");
                if (connIdRs.next()) {
                    log.info("🔍 [ORACLE DEBUG] Session ID (SID): {}", connIdRs.getString("SID"));
                    log.info("🔍 [ORACLE DEBUG] Session ID (SESSIONID): {}", connIdRs.getString("SESSION_ID"));
                    log.info("🔍 [ORACLE DEBUG] Instance: {}", connIdRs.getString("INSTANCE"));
                }

                // Basic test query
                var resultSet = statement.executeQuery("SELECT 1 FROM DUAL");
                assertTrue(resultSet.next(), "Should get result from DUAL");
                assertEquals(1, resultSet.getInt(1), "Should return 1");
            }
        } catch (Exception e) {
            log.error("❌ [OracleProductionSqlFilesIntegrationTest] Connection test failed: {}", e.getMessage(), e);
            throw e;
        }

        // 🔧 DEBUGGING: Log connection pool status after test
        logConnectionPoolStatus("After OracleProductionSqlFilesIntegrationTest");

        log.info("✅ Oracle TestContainer connection verified for Makos");
    }

    /**
     * Cleanup method to ensure proper test lifecycle management
     */
    @AfterAll
    static void cleanup() {
        try {
            // Give some time for any pending operations to complete
            Thread.sleep(1000);
            log.info("🧹 Test cleanup completed - container will be stopped by Testcontainers");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Cleanup interrupted: {}", e.getMessage());
        }
    }


}
