-- Oracle TestContainer Minimal JDBC-Compatible Initialization
-- This script contains only basic SQL commands that work with JDBC
-- No SQL*Plus commands, no PL/SQL blocks with DBMS_OUTPUT

-- Create a simple table to verify container is working
CREATE TABLE CONTAINER_INIT_CHECK (
    ID NUMBER(1) PRIMARY KEY,
    STATUS VARCHAR2(20) DEFAULT 'READY',
    INIT_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert a test record
INSERT INTO CONTAINER_INIT_CHECK (ID, STATUS) VALUES (1, 'READY');

-- Commit the transaction
COMMIT;
