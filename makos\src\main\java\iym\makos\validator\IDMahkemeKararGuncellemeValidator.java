package iym.makos.validator;

import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.validation.ValidationResult;
import iym.makos.model.dto.mahkemekarar.id.IDMahkemeKararGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.model.api.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class IDMahkemeKararGuncellemeValidator extends MahkemeKararRequestValidatorBase<IDMahkemeKararGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeBilgiService dbMahkemeBilgiService;

    @Autowired
    public IDMahkemeKararGuncellemeValidator(DbMahkemeKararService dbMahkemeKararService, DbMahkemeBilgiService dbMahkemeBilgiService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeBilgiService = dbMahkemeBilgiService;
    }

    @Override
    protected ValidationResult doValidate(IDMahkemeKararGuncellemeRequest request) {

        try {
            ValidationResult validationResult = new ValidationResult(true);

            for (MahkemeKararGuncellemeDetay mahkemeKararBilgisiGuncellemeDetay : request.getMahkemeKararGuncellemeDetayListesi()) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = mahkemeKararBilgisiGuncellemeDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("İlişkil mahkeme karar boş olamaz");
                } else {
                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu(), iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {

                        MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();
                        String yeniMahkemeKodu = "";
                        String yeniSorusturmaNo = "";
                        String yeniMahkemeKararNo = "";

                        for(MahkemeKararGuncellemeBilgi  mahkemeKararGuncellemeBilgi : mahkemeKararBilgisiGuncellemeDetay.getMahkemeKararGuncellemeBilgiListesi()){
                            if(mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlan() == MahkemeKararGuncellemeAlan.MAHKEME_KODU){
                                yeniMahkemeKodu = mahkemeKararGuncellemeBilgi.getYeniDegeri();
                                if(iliskiliMahkemeKarar.getMahkemeKodu().equals(yeniMahkemeKodu)){
                                    validationResult.addFailedReason("Mahkeme kodu eski mahkeme kod ile aynıdır.");
                                }
                                else{
                                    //Değiştirilmeye çalılşılan mahkeme kodu ile ilgili mahkeme var mı?
                                    Optional<MahkemeBilgi> mahkemeBilgiOpt = dbMahkemeBilgiService.findByMahkemeKodu(yeniMahkemeKodu);
                                    if (mahkemeBilgiOpt.isEmpty()) {
                                        validationResult.addFailedReason(yeniMahkemeKodu + " numaralı mahkeme bilgisi bulunamadı.");
                                    }
                                }

                            }
                            else if(mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlan() == MahkemeKararGuncellemeAlan.MAHKEMEKARAR_NO){
                                yeniMahkemeKararNo = mahkemeKararGuncellemeBilgi.getYeniDegeri();
                                if(iliskiliMahkemeKarar.getMahkemeKararNo().equals(yeniMahkemeKararNo)){
                                    validationResult.addFailedReason("Mahkeme karar numarası eski mahkeme karar numarası  ile aynıdır.");
                                }
                            }
                            else if(mahkemeKararGuncellemeBilgi.getMahkemeKararGuncellemeAlan() == MahkemeKararGuncellemeAlan.SORUSTURMA_NO){
                                yeniSorusturmaNo = mahkemeKararGuncellemeBilgi.getYeniDegeri();
                                if(iliskiliMahkemeKarar.getSorusturmaNo().equals(yeniSorusturmaNo)){
                                    validationResult.addFailedReason("Soruşturma numarası eski soruşturma numarası  ile aynıdır.");
                                }
                            }
                        }
                        /*
                        if (CommonUtils.isNullOrEmpty(yeniMahkemeKodu) && CommonUtils.isNullOrEmpty(yeniSorusturmaNo) && CommonUtils.isNullOrEmpty(yeniMahkemeKararNo)) {
                            validationResult.addFailedReason("Mahkeme karar bilgisi güncelleme yapmak için en az bir tane alan dolu olmalıdır.");
                        }*/

                    }
                }
            }

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME;
    }

}

