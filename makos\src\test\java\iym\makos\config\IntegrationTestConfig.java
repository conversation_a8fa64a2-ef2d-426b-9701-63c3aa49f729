package iym.makos.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * Test configuration for integration tests
 * This configuration allows full database auto-configuration with H2 in-memory database
 * Used for @SpringBootTest integration tests that need database connectivity
 */
@Configuration
@Profile("integration-test")
public class IntegrationTestConfig {
    // This class is intentionally empty
    // Its purpose is to enable full Spring Boot auto-configuration for integration tests
    // Database configuration is handled through application.properties
}
