-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAH_AID_DET_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAH_AID_DET_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAH_AID_DET_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

--TODO : byte -> null, pkkey
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_AIDIYAT_DETAY';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_AIDIYAT_DETAY (
      ID NUMBER NOT NULL
    , ILISKILI_MAHKEME_KARAR_ID NUMBER
    , <PERSON>HKE<PERSON>_KARAR_ID NUMBER
    , <PERSON>H<PERSON><PERSON>_AIDIYAT_KODU_EKLE VARCHAR2(25)
    , <PERSON>HKEME_AIDIYAT_KODU_CIKAR VARCHAR2(25)
    , TARIH DATE NOT NULL
    , DURUM VARCHAR2(25)
    , MAHKEME_KARAR_DETAY_ID NUMBER
     , CONSTRAINT MAHKEME_AIDIYAT_DETAY_PK PRIMARY KEY (ID) ENABLE
    )';
  END IF;
END;
/


COMMIT;
