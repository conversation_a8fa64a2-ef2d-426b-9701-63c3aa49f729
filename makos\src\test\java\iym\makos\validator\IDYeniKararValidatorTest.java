package iym.makos.validator;

import iym.common.enums.MahkemeKararTip;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbSucTipiService;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.model.dto.mahkemekarar.id.IDYeniKararRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Optional;

import static iym.makos.domain.testdata.TestDataBuilder.createValidEvrakDetay;
import static iym.makos.domain.testdata.TestDataBuilder.createValidIDYeniKararRequest;
import static iym.makos.domain.testdata.TestDataBuilder.createValidMahkemeKararBilgisi;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDYeniKararValidator.
 *
 * Tests the validator implementation for ID yeni karar requests.
 * Verifies business rule validations including OHAL date restrictions.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("IDYeniKararValidator Unit Tests")
class IDYeniKararValidatorTest extends BaseDomainUnitTest {
    
    @Mock
    private DbMahkemeKararService mockDbMahkemeKararService;
    
    @Mock
    private DbHedeflerService mockDbHedeflerService;
    
    @Mock
    private DbSucTipiService mockDbSucTipiService;
    
    @Mock
    private MahkemeKararRequestCommonValidator mockCommonValidator;
    
    @InjectMocks
    private IDYeniKararValidator validator;
    
    private IDYeniKararRequest testRequest;
    
    @BeforeEach
    void setUp() {
        testRequest = createValidIDYeniKararRequest();

        // Setup default mocks
        setupValidMocks();
    }

    private void setupValidMocks() {
        // Mock common validator to return valid result for any request
        when(mockCommonValidator.validate(any(IDYeniKararRequest.class)))
                .thenReturn(new ValidationResult(true));

        // Mock suç tipi service to return valid suç tipi
        when(mockDbSucTipiService.findBySucTipiKodu(any()))
                .thenReturn(Optional.of(new iym.common.model.entity.iym.SucTipi()));

        // Set the common validator mock
        validator.setMahkemeKararRequestCommonValidator(mockCommonValidator);
    }
    
    @Test
    @DisplayName("Should pass validation when request is valid")
    void shouldPassValidation_whenRequestIsValid() {
        // Given - Valid request with normal karar tipi
        testRequest.getMahkemeKararBilgisi().setMahkemeKararTipi(MahkemeKararTip.ADLI_HAKIM_KARARI);

        // When
        ValidationResult result = validator.validate(testRequest);

        // Then
        assertValidationSuccess(result);
    }
    
    @Test
    @DisplayName("Should fail validation when OHAL date violated for Adli KHK Yazili Emir")
    void shouldFailValidation_whenOhalDateViolatedForAdliKhkYaziliEmir() {
        // Given - Request with Adli KHK Yazili Emir after OHAL date
        testRequest.getMahkemeKararBilgisi().setMahkemeKararTipi(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR);
        
        // When
        ValidationResult result = validator.validate(testRequest);
        
        // Then
        assertValidationFailure(result, "19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
    }
    
    @Test
    @DisplayName("Should pass validation when Adli KHK Yazili Emir before OHAL date")
    void shouldPassValidation_whenAdliKhkYaziliEmirBeforeOhalDate() {
        // Given - Request with Adli KHK Yazili Emir before OHAL date (this is a theoretical test)
        testRequest.getMahkemeKararBilgisi().setMahkemeKararTipi(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR);
        
        // Note: Since current date is after OHAL date, this test demonstrates the logic
        // In real scenario, we would need to mock the current date or test with historical data
        
        // When
        ValidationResult result = validator.validate(testRequest);
        
        // Then - Should fail because current date is after OHAL date
        assertValidationFailure(result, "19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
    }
    
    @Test
    @DisplayName("Should handle common validator failure")
    void shouldHandleCommonValidatorFailure() {
        // Given - Common validator returns failure
        ValidationResult commonFailure = new ValidationResult("Evrak detayları eksik");
        when(mockCommonValidator.validate(any(IDYeniKararRequest.class))).thenReturn(commonFailure);

        // When
        ValidationResult result = validator.validate(testRequest);

        // Then
        assertValidationFailure(result, "Evrak detayları eksik");
    }
    
    @Test
    @DisplayName("Should handle request validation failure")
    void shouldHandleRequestValidationFailure() {
        // Given - Request with invalid suç tipi kodu
        testRequest = createValidIDYeniKararRequest();

        // Create a new mutable list and add invalid suç tipi
        ArrayList<String> sucTipiKodlari = new ArrayList<>(testRequest.getMahkemeSucTipiKodlari());
        sucTipiKodlari.add("INVALID_SUC_TIPI");
        testRequest.setMahkemeSucTipiKodlari(sucTipiKodlari);

        // Mock suç tipi service to return empty for invalid code
        when(mockDbSucTipiService.findBySucTipiKodu("INVALID_SUC_TIPI"))
                .thenReturn(Optional.empty());

        // When
        ValidationResult result = validator.validate(testRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("'INVALID_SUC_TIPI' kodu ile eşleşen bir suç tipi mevcut değil.");
    }
    
    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() {
        // When
        Class<IDYeniKararRequest> requestType = validator.getRelatedRequestType();

        // Then
        org.assertj.core.api.Assertions.assertThat(requestType).isEqualTo(IDYeniKararRequest.class);
    }
    
    @Test
    @DisplayName("Should handle validation exception gracefully")
    void shouldHandleValidationException_gracefully() {
        // Given - Mock common validator to throw exception
        when(mockCommonValidator.validate(any(IDYeniKararRequest.class))).thenThrow(new RuntimeException("Database error"));

        // When
        ValidationResult result = validator.validate(testRequest);

        // Then
        assertValidationFailure(result, "Validation failed. Internal error");
    }
    
    @Test
    @DisplayName("Should validate different mahkeme karar tipi values correctly")
    void shouldValidateDifferentMahkemeKararTipi_correctly() {
        // Test various karar tipi values that should pass validation
        MahkemeKararTip[] validTipler = {
            MahkemeKararTip.ADLI_HAKIM_KARARI,
            MahkemeKararTip.ONLEYICI_HAKIM_KARARI,
            MahkemeKararTip.ADLI_YAZILI_EMIR
        };

        for (MahkemeKararTip tip : validTipler) {
            // Given
            testRequest.getMahkemeKararBilgisi().setMahkemeKararTipi(tip);

            // When
            ValidationResult result = validator.validate(testRequest);

            // Then
            assertValidationSuccess(result);
        }
    }
    

}
