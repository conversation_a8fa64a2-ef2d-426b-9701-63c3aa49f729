package iym.makos.mapper;

import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.SucTipi;
import iym.makos.model.dto.db.MahkemeDTO;
import iym.makos.model.dto.db.MahkemeKoduDTO;
import iym.makos.model.dto.db.SucTipiDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for MahkemeBilgiMapper entity and DTO
 */
@Component
public class MahkemeBilgiMapper {


    public MahkemeDTO toDto(MahkemeBilgi entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeDTO.builder()
                .mahkemeKodu(entity.getMahkemeKodu())
                .ilIlceKodu(entity.getIlIlceKodu())
                .mahkemeTuruKodu(entity.getMahkemeTuruKodu())
                .mahkemeSayi(entity.getMahkemeSayi())
                .mahkemeAdi(entity.getMahkemeAdi())
                .eklemeTarihi(entity.getEklemeTarihi())
                .ekleyenKullaniciId(entity.getEkleyenKullaniciId())
                .silindi(entity.getSilindi())
                .build();

    }

    public MahkemeKoduDTO toMahkemeKoduDTO(MahkemeBilgi entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeKoduDTO.builder()
                .mahkemeKodu(entity.getMahkemeKodu())
                .mahkemeAdi(entity.getMahkemeAdi())
                .build();
    }


    public MahkemeBilgi toEntity(MahkemeDTO dto) {
        if (dto == null) {
            return null;
        }

        return MahkemeBilgi.builder()
                .mahkemeKodu(dto.getMahkemeKodu())
                .ilIlceKodu(dto.getIlIlceKodu())
                .mahkemeTuruKodu(dto.getMahkemeTuruKodu())
                .mahkemeSayi(dto.getMahkemeSayi())
                .mahkemeAdi(dto.getMahkemeAdi())
                .eklemeTarihi(dto.getEklemeTarihi())
                .ekleyenKullaniciId(dto.getEkleyenKullaniciId())
                .silindi(dto.getSilindi())
                .build();
    }

    public List<MahkemeKoduDTO> toMahkemeKoduDtoList(List<MahkemeBilgi> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toMahkemeKoduDTO)
                .collect(Collectors.toList());
    }


}
