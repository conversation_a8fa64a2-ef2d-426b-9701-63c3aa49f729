package iym.makos.model.dto.mahkemekarar.id;

import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.dto.mahkemekarar.MahkemeKararRequest;
import iym.makos.validator.custom.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDYeniKararRequest extends MahkemeKararRequest {

    @NotNull
    @Size(min = 1)
    @Valid
    private List<IDHedefDetay> hedefDetayListesi = new ArrayList<>();

    private List<String> mahkemeAidiyatKodlari = new ArrayList<>();

    private List<String> mahkemeSucTipiKodlari = new ArrayList<>();

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDYeniKararRequest is valid");

        try {

            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR.name() + " olmalıdır");
                return validationResult;
            }

            MahkemeKararTip mahkemeKararTipi = mahkemeKararBilgisi.getMahkemeKararTipi();
            boolean yeniMahkemeKararTipinde = CommonUtils.yeniMahkemeKararTipi(mahkemeKararTipi);
            if (!yeniMahkemeKararTipinde) {
                validationResult.addFailedReason("Mahkeme karar tipi, yeni karar için uygun değildir!");
            }

            for (IDHedefDetay IDHedefDetay : hedefDetayListesi) {
                if (IDHedefDetay.getIlgiliMahkemeKararDetayi() != null) {
                    validationResult.addFailedReason("Yeni Kararda ilişkili mahkeme karari dolu olamaz!");
                }

                if (IDHedefDetay.getUzatmaSayisi() != null && IDHedefDetay.getUzatmaSayisi() > 0) {
                    validationResult.addFailedReason("Yeni Kararda uzatma sayisi dolu olamaz!");
                }

                if (IDHedefDetay.getSure() == null) {
                    validationResult.addFailedReason("Hedefin süresi boş olamaz!");
                } else if (IDHedefDetay.getSure() <= 0) {
                    validationResult.addFailedReason("Hedefin süresi sıfırdan büyük olmalıdır.");
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }
}

