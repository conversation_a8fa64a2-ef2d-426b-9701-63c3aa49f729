package iym.makos.integration;

import iym.common.testcontainer.AbstractOracleTestContainer;
import iym.common.testcontainer.OracleTestContainerConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Simple test to verify Oracle TestContainer configuration works
 * This test uses the same configuration as OracleProductionSqlFilesIntegrationTest
 * to isolate the configuration issue
 */
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@DataJpaTest
@Import(OracleTestContainerConfiguration.class)
@Testcontainers
@ActiveProfiles("testcontainers-oracle")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("Simple Oracle TestContainer Configuration Test")
@Slf4j
public class SimpleOracleTestContainerTest extends AbstractOracleTestContainer {

    @Test
    @DisplayName("🔗 Should connect to Oracle TestContainer")
    void shouldConnectToOracleTestContainer() throws Exception {
        log.info("🚀 [SimpleOracleTestContainerTest] Starting connection test...");

        // 🔧 DEBUGGING: Log connection pool status before test
        logConnectionPoolStatus("Before SimpleOracleTestContainerTest");

        assertNotNull(dataSource, "DataSource should not be null");

        try (Connection connection = dataSource.getConnection()) {
            assertNotNull(connection, "Connection should not be null");
            assertFalse(connection.isClosed(), "Connection should be open");

            // 🔧 DEBUGGING: Log connection details
            log.info("🔍 [CONNECTION DEBUG] Connection Class: {}", connection.getClass().getName());
            log.info("🔍 [CONNECTION DEBUG] Connection String: {}", connection.toString());

            // Test basic query and get Oracle connection details
            try (Statement statement = connection.createStatement()) {
                // Get Oracle connection ID
                ResultSet connIdRs = statement.executeQuery("SELECT SYS_CONTEXT('USERENV', 'SID') as SID, " +
                        "SYS_CONTEXT('USERENV', 'SESSIONID') as SESSION_ID, " +
                        "SYS_CONTEXT('USERENV', 'INSTANCE') as INSTANCE FROM DUAL");
                if (connIdRs.next()) {
                    log.info("🔍 [ORACLE DEBUG] Session ID (SID): {}", connIdRs.getString("SID"));
                    log.info("🔍 [ORACLE DEBUG] Session ID (SESSIONID): {}", connIdRs.getString("SESSION_ID"));
                    log.info("🔍 [ORACLE DEBUG] Instance: {}", connIdRs.getString("INSTANCE"));
                }

                // Basic test query
                ResultSet resultSet = statement.executeQuery("SELECT 1 FROM DUAL");
                assertTrue(resultSet.next(), "Should get result from DUAL");
                assertEquals(1, resultSet.getInt(1), "Should return 1");
            }
        } catch (Exception e) {
            log.error("❌ [SimpleOracleTestContainerTest] Connection test failed: {}", e.getMessage(), e);
            throw e;
        }

        // 🔧 DEBUGGING: Log connection pool status after test
        logConnectionPoolStatus("After SimpleOracleTestContainerTest");

        log.info("✅ Oracle TestContainer connection verified");
    }

    @Test
    @DisplayName("🔍 Should verify Oracle database type")
    void shouldVerifyOracleDatabaseType() throws Exception {
        try (Connection connection = dataSource.getConnection()) {
            String databaseProductName = connection.getMetaData().getDatabaseProductName();
            log.info("Database product name: {}", databaseProductName);
            assertTrue(databaseProductName.toLowerCase().contains("oracle"),
                    "Should be Oracle database, but was: " + databaseProductName);
        }
    }
}
