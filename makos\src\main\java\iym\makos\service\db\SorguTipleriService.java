package iym.makos.service.db;

import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.SorguTipleri;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.DbSorguTipleriService;
import iym.common.util.CommonUtils;
import iym.makos.mapper.MahkemeBilgiMapper;
import iym.makos.mapper.SorguTipiMapper;
import iym.makos.model.dto.db.MahkemeDTO;
import iym.makos.model.dto.db.MahkemeKoduDTO;
import iym.makos.model.dto.db.SorguTipiDTO;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mk.MahkemeKararSorguView;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SorguTipleriService {

    private final DbSorguTipleriService dbSorguTipleriService;
    private final SorguTipiMapper sorguTipiMapper;



    public List<SorguTipiDTO> findAllSorguTipleri(){
        List<SorguTipleri> sorguTipleri = dbSorguTipleriService.findAll();

        List<SorguTipiDTO> result = CommonUtils.safeList(sorguTipleri
                ).stream()
                .map(sorguTipiMapper::toDto)
                .collect(Collectors.toList());

        return result;

    }

}
