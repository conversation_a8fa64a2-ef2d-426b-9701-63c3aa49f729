package iym.makos.domain.talepislem.processor;

import iym.common.enums.KararTuru;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.MahkemeSucTipiDetayTalepRepo;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IDSucTipiGuncellemeIsleyici extends IDMahkemeKararIsleyiciBase {

    private final MahkemeSucTipiDetayTalepRepo mahkemeSucTipiDetayTalepRepo;
    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;

    @Autowired
    public IDSucTipiGuncellemeIsleyici(MahkemeSucTipiDetayTalepRepo mahkemeSucTipiDetayTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
    ) {
        this.mahkemeSucTipiDetayTalepRepo = mahkemeSucTipiDetayTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
    }

    protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {

        try {
            String durum = MahkemeKararTalepIsleyici.toDurum(request.getTalepGuncellemeTuru());
            CommonUtils.safeList(dMahkemeKararTalepRepo.findByMahkemeKararTalepId(request.getMahkemeKararTalepId()))
                    .forEach(dMahkemeKararTalep -> {
                        dMahkemeKararTalep.setDurum(durum);
                        dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                        CommonUtils.safeList(mahkemeSucTipiDetayTalepRepo.findByMahkemeKararDetayTalepId(dMahkemeKararTalep.getId()))
                                .forEach(mahkemeSucTipiDetayTalep -> {
                                    mahkemeSucTipiDetayTalep.setDurum(durum);
                                    mahkemeSucTipiDetayTalepRepo.save(mahkemeSucTipiDetayTalep);
                                });
                    });

            return MahkemeKararTalepUpdateResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build();

        } catch (Exception ex) {
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Iliskili veritabanı guncelleme hatası")
                            .build())
                    .build();
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME;
    }


}

