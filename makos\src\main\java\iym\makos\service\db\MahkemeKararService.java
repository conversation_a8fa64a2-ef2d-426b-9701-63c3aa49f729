package iym.makos.service.db;

import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.sorgu.MahkemeKararSorguInfo;
import iym.common.model.entity.iym.sorgu.MahkemeKararSorguParam;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.mk.MahkemeKararRepo;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.mapper.MahkemeKararSorguMapper;
import iym.makos.model.dto.db.MahkemeKararDTO;
import iym.makos.mapper.MahkemeKararMapper;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mk.MahkemeKararSorguView;
import iym.makos.model.dto.ws.internal.id.IDEvrakAtamaModel;
import iym.makos.model.dto.ws.internal.id.IDIslenecekEvrakModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeKararService {

    private final DbMahkemeKararService dbMahkemeKararService;

    private final MahkemeKararMapper mahkemeKararMapper;

    private final MahkemeKararSorguMapper mahkemeKararSorguMapper;

    private final MahkemeKararRepo mahkemeKararRepo;

    public List<IDIslenecekEvrakModel> getBTKIslenecekEvrakListesi(UserDetailsImpl user, String gorevTipi,boolean nobetciMi){
        List<IDIslenecekEvrakModel> result = null;
        return result;
    }

    public List<IDEvrakAtamaModel> getIDEvrakAtamaHistory(UserDetailsImpl user, Long evrakId){
        List<IDEvrakAtamaModel> result = null;
        return result;
    }


    public List<MahkemeKararSorguView> mahkemeKararSorgu(UserDetailsImpl user, MahkemeKararSorguParam sorguParam){

        List<MahkemeKararSorguInfo> mahkemeKararSorguSonucListesi = mahkemeKararRepo.mahkemeKararSorgu(user.getKullaniciKurum().getValue(), sorguParam);
        List<MahkemeKararSorguView> result = CommonUtils.safeList(mahkemeKararSorguSonucListesi
                ).stream()
                .map(mahkemeKararSorguMapper::toMahkemeKararSorguView)
                .collect(Collectors.toList());

        return result;

    }

    public List<MahkemeKararSorguView> islenecekKararListesi(UserDetailsImpl user){

        List<MahkemeKararSorguInfo> mahkemeKararSorguSonucListesi = mahkemeKararRepo.islenecekKararListesi(user.getKullaniciKurum().getValue());
        List<MahkemeKararSorguView> result = CommonUtils.safeList(mahkemeKararSorguSonucListesi
                ).stream()
                .map(mahkemeKararSorguMapper::toMahkemeKararSorguView)
                .collect(Collectors.toList());

        return result;

    }

    public MahkemeKararDTO findById(Long id){
        return dbMahkemeKararService.findById(id)
                .map(mahkemeKararMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme Karar bilgisi bulunamadı"));
    }


    public List<MahkemeKararDTO> findByEvrakId(Long evrakId){
        List<MahkemeKarar> mahkemeKararList = dbMahkemeKararService.findByEvrakId(evrakId);
        return mahkemeKararMapper.toDtoList(mahkemeKararList);
    }

    public  MahkemeKararDTO getMahkemeKararBilgisi(MahkemeKararDetay detay){
        return findBy(detay.getMahkemeIlIlceKodu(), detay.getMahkemeKodu(), detay.getMahkemeKararNo(), detay.getSorusturmaNo());
    }

    public MahkemeKararDTO findBy(
            String mahkemeIlIlceKodu,
            String mahkemeKodu,
            String mahkemeKararNo,
            String sorusturmaNo
    ){
        Optional<MahkemeKarar> mahkemeKararOpt = dbMahkemeKararService.findBy(
                mahkemeIlIlceKodu,
                mahkemeKodu,
                mahkemeKararNo,
                sorusturmaNo);
        return mahkemeKararMapper.toDto(mahkemeKararOpt.orElse(null));
    }


}
