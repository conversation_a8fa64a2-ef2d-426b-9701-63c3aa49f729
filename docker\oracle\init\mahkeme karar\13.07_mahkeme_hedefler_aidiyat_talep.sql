-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

----------KULLANILIYOR MU?

-- Create sequence for MAHKEME_HEDEFLER_AIDIYAT_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAH_HED_AID_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAH_HED_AID_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_HEDEFLER_AIDIYAT_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_HEDEFLER_AIDIYAT_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_HEDEFLER_AIDIYAT_TALEP (
      ID NUMBER NOT NULL,
      HEDEF_ID NUMBER NOT NULL,
      AIDIYAT_KOD VARCHAR2(15 BYTE) NOT NULL,
      TARIH DATE NOT NULL,
      MAHKEME_KARAR_ID NUMBER NOT NULL,
      DURUMU VARCHAR2(10 BYTE),
      KULLANICI_ID NUMBER,
      CONSTRAINT HEDEF_AIDIYAT_TLPPK PRIMARY KEY (ID) ENABLE
    )';

    -- Create indexes
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MT_IND_HED_AID_HED_ID ON iym.MAHKEME_HEDEFLER_AIDIYAT_TALEP (HEDEF_ID ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MT_IND_HED_AID_MAH_ID ON iym.MAHKEME_HEDEFLER_AIDIYAT_TALEP (MAHKEME_KARAR_ID ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.MAHKEME_HEDEFLER_AIDIYAT_TALEP;
  IF row_count = 0 THEN
    -- Make sure we have hedefler_talep records
    DECLARE
      hedef_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO hedef_count FROM iym.HEDEFLER_TALEP;

      IF hedef_count > 0 THEN
        -- Get the hedefler_talep records
        FOR hedef_rec IN (
          SELECT h.ID as hedef_id, h.MAHKEME_KARAR_ID, h.KULLANICI_ID
          FROM iym.HEDEFLER_TALEP h
        ) LOOP
          -- Sample data - Hedef aidiyat kaydı
          INSERT INTO iym.MAHKEME_HEDEFLER_AIDIYAT_TALEP (
            ID, HEDEF_ID, AIDIYAT_KOD, TARIH, MAHKEME_KARAR_ID, DURUMU, KULLANICI_ID
          ) VALUES (
            iym.MAH_HED_AID_TALEP_SEQ.NEXTVAL, hedef_rec.hedef_id, 'AIDIYAT1',
            SYSDATE, hedef_rec.MAHKEME_KARAR_ID, 'AKTIF', hedef_rec.KULLANICI_ID
          );

          -- Sample data - Hedef aidiyat kaydı 2
          INSERT INTO iym.MAHKEME_HEDEFLER_AIDIYAT_TALEP (
            ID, HEDEF_ID, AIDIYAT_KOD, TARIH, MAHKEME_KARAR_ID, DURUMU, KULLANICI_ID
          ) VALUES (
            iym.MAH_HED_AID_TALEP_SEQ.NEXTVAL, hedef_rec.hedef_id, 'AIDIYAT2',
            SYSDATE, hedef_rec.MAHKEME_KARAR_ID, 'AKTIF', hedef_rec.KULLANICI_ID
          );
        END LOOP;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
