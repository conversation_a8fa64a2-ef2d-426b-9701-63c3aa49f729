package iym.makos.domain.talepislem.idislemeaktar;

import iym.common.enums.KararTuru;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.HedeflerTalepRepo;
import iym.db.jpa.dao.talep.HedeflerAidiyatTalepRepo;
import iym.db.jpa.dao.talep.MahkemeAidiyatTalepRepo;
import iym.db.jpa.dao.talep.MahkemeSuclarTalepRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IDYeniKararIslemeAktarici extends IDMahkemeKararTalepIslemeAktariciBase {

    private final HedeflerTalepRepo hedeflerTalepRepo;
    private final HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private final MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo;
    private final MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo;

    @Autowired
    public IDYeniKararIslemeAktarici(HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo
            , MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo
    ) {
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.mahkemeAidiyatTalepRepo = mahkemeAidiyatTalepRepo;
        this.mahkemeSuclarTalepRepo = mahkemeSuclarTalepRepo;
    }

    protected boolean updateRelatedTables(Long mahkemeKararTalepId) {
        boolean result = false;
        try {
            String durum = "ISLEMDE";
            CommonUtils.safeList(mahkemeAidiyatTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId))
                    .forEach(mahkemeAidiyatTalep -> {
                        mahkemeAidiyatTalep.setDurumu(durum);
                        mahkemeAidiyatTalepRepo.save(mahkemeAidiyatTalep);
                    });

            CommonUtils.safeList(mahkemeSuclarTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId))
                    .forEach(mahkemeSuclarTalep -> {
                        mahkemeSuclarTalep.setDurumu(durum);
                        mahkemeSuclarTalepRepo.save(mahkemeSuclarTalep);
                    });

            CommonUtils.safeList(hedeflerTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId))
                    .forEach(hedeflerTalep -> {
                        hedeflerTalep.setDurumu(durum);
                        hedeflerTalepRepo.save(hedeflerTalep);

                        CommonUtils.safeList(hedeflerAidiyatTalepRepo.findByHedefTalepId(hedeflerTalep.getId()))
                                .forEach(hedeflerAidiyatTalep -> {
                                    hedeflerAidiyatTalep.setDurumu(durum);
                                });
                    });

            result = true;

        } catch (Exception ex) {
            log.error("IDYeniKararIslemeAktarici process failed, mahkemeKararTalepId:{}", mahkemeKararTalepId, ex);
        }
        return result;
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }

}

