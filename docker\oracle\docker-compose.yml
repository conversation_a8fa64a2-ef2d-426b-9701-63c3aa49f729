services:
  oracle:
    image: gvenzl/oracle-xe:********-slim
    container_name: iym-oracle
    environment:
      - ORACLE_PASSWORD=oracle
      - ORACLE_DATABASE=XE
      - APP_USER=iym
      - APP_USER_PASSWORD=iym
    ports:
      - "1521:1521"
    volumes:
      - ./init:/docker-entrypoint-initdb.d
      - oracle-data:/opt/oracle/oradata
    healthcheck:
      test: ["CMD", "sqlplus", "-L", "iym/iym@//localhost:1521/XE", "AS", "SYSDBA", "@/docker-entrypoint-initdb.d/healthcheck.sql"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped

volumes:
  oracle-data:
    driver: local
