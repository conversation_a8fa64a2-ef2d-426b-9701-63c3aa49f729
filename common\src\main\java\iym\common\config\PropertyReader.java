package iym.common.config;

import iym.common.util.AppContextProvider;
import org.springframework.core.env.Environment;

public class PropertyReader {

    private final Environment environment;

    private static class Singleton{
        private static final PropertyReader INSTANCE = new PropertyReader();
    }

    public static PropertyReader getInstance(){
        return Singleton.INSTANCE;
    }

    private PropertyReader(){
        environment = AppContextProvider.getApplicationContext().getEnvironment();
    }

    public String getProperty(String key){
        return environment.getProperty(key);
    }

    public String getProperty(String key, String defaultValue){
        return environment.getProperty(key, defaultValue);
    }

    public <T> T getProperty(String key, Class<T> type) throws IllegalStateException {
        return environment.getProperty(key, type);
    }

    public <T> T getProperty(String key, Class<T> type, T defaultValue) throws IllegalStateException {
        return environment.getProperty(key, type, defaultValue);
    }

    public String getRequiredProperty(String key){
        return environment.getRequiredProperty(key);
    }

    public <T> T getRequiredProperty(String key, Class<T> type) throws IllegalStateException {
        return environment.getRequiredProperty(key, type);
    }

}
