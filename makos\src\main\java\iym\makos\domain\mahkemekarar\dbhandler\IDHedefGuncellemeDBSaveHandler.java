package iym.makos.domain.mahkemekarar.dbhandler;

import iym.common.enums.HedefTip;
import iym.common.model.entity.iym.mk.He<PERSON>fler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.db.jpa.dao.*;
import iym.db.jpa.dao.mk.HedeflerRepo;
import iym.db.jpa.dao.mk.MahkemeKararRepo;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.HedeflerAidiyatTalepRepo;
import iym.db.jpa.dao.talep.HedeflerDetayTalepRepo;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.makos.model.dto.mahkemekarar.id.IDHedefGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class IDHedefGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDHedefGuncellemeRequest> {
    private MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private HedeflerTalepRepo hedeflerTalepRepo;
    private HedeflerRepo hedeflerRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;
    private HedeflerDetayTalepRepo hedeflerDetayTalepRepo;

    @Autowired
    public IDHedefGuncellemeDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerRepo hedeflerRepo
            , HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , HedeflerDetayTalepRepo hedeflerDetayTalepRepo
            , KararRequestMapper kararRequestMapper) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.hedeflerRepo = hedeflerRepo;
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.hedeflerDetayTalepRepo = hedeflerDetayTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
    }

    @Override
    @Transactional
    public Long kaydet(IDHedefGuncellemeRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId = null;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            for (HedefGuncellemeKararDetay hedefBilgisi : request.getHedefGuncellemeKararDetayListesi()) {

                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay iliskiliMahkemeKararDetayRequest = hedefBilgisi.getMahkemeKararDetay();
                Optional<MahkemeKarar> iliskiliMahkemeKararOpt = mahkemeKararRepo.findBy(iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                        , iliskiliMahkemeKararDetayRequest.getMahkemeKodu()
                        , iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                        , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                if (iliskiliMahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetayRequest.getMahkemeKodu(), iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                    throw new MakosResponseException(errorStr);
                }
                MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();

                DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(detayMahkemeKararTalep);

                //Her bir hedefin iliskili mahkeme kararda olup olmadigini kontrol et
                List<HedefGuncellemeDetay> hedefListesi = hedefBilgisi.getHedefGuncellemeDetayListesi();
                if (hedefListesi != null && !hedefListesi.isEmpty()) {
                    for (HedefGuncellemeDetay hedefGuncellemeDetay : hedefListesi) {

                        String hedefNo = hedefGuncellemeDetay.getHedef().getHedefNo();
                        HedefTip hedefTipi = hedefGuncellemeDetay.getHedef().getHedefTip();

                        Optional<Hedefler> iliskiliHedef = hedeflerRepo.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                        if (iliskiliHedef.isEmpty()) {
                            throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                        }

                        HedeflerDetayTalep hedeflerDetayTalep = new HedeflerDetayTalep();
                        for(HedefGuncellemeBilgi hedefGuncellemeBilgisi : hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi()){
                            if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.AD){
                                hedeflerDetayTalep.setHedefAdi(hedefGuncellemeBilgisi.getYeniDegeri());
                            }
                            else if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.SOYAD){
                                hedeflerDetayTalep.setHedefSoyadi(hedefGuncellemeBilgisi.getYeniDegeri());
                            }
                            else if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.TCKIMlIKNO){
                                hedeflerDetayTalep.setTcKimlikNo(hedefGuncellemeBilgisi.getYeniDegeri());
                            }
                            else if(hedefGuncellemeBilgisi.getHedefGuncellemeAlan() == HedefGuncellemeAlan.CANAK_NO){
                                hedeflerDetayTalep.setCanakNo(hedefGuncellemeBilgisi.getYeniDegeri());
                            }
                        }

                        String updateColumnNames = hedefGuncellemeDetay.getHedefGuncellemeBilgiListesi().stream()
                                .map(guncellemeBilgi -> guncellemeBilgi.getHedefGuncellemeAlan().name())
                                .collect(Collectors.joining(","));

                        hedeflerDetayTalep.setUpdateColumnNames(updateColumnNames);
                        hedeflerDetayTalep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());

                        HedeflerDetayTalep savedHedeflerDetayTalep = hedeflerDetayTalepRepo.save(hedeflerDetayTalep);
                        if(savedHedeflerDetayTalep == null){
                            throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_HEDEFDETAY_KAYDETMEHATASI);
                        }
                    }
                }

            }
            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("IDHedefGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

