package iym.makos.domain.mahkemekarar.dbhandler;

import iym.common.enums.GuncellemeTip;
import iym.common.model.entity.iym.mk.MahkemeKararAidiyat;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.db.jpa.dao.mk.MahkemeKararAidiyatRepo;
import iym.db.jpa.dao.mk.MahkemeKararRepo;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.HedeflerAidiyatTalepRepo;
import iym.db.jpa.dao.talep.MahkemeAidiyatDetayTalepRepo;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.makos.model.dto.mahkemekarar.id.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.AidiyatGuncellemeDetay;
import iym.makos.model.api.AidiyatGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDAidiyatBilgisiGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDAidiyatBilgisiGuncellemeRequest> {

    private MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;
    private MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private MahkemeKararAidiyatRepo mahkemeKararAidiyatRepo;

    @Autowired
    public IDAidiyatBilgisiGuncellemeDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo
            , KararRequestMapper kararRequestMapper
            , MahkemeKararAidiyatRepo mahkemeKararAidiyatRepo) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
        this.mahkemeAidiyatDetayTalepRepo = mahkemeAidiyatDetayTalepRepo;
        this.mahkemeKararAidiyatRepo = mahkemeKararAidiyatRepo;
    }

    @Override
    @Transactional
    public Long kaydet(IDAidiyatBilgisiGuncellemeRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            List<AidiyatGuncellemeKararDetay> guncellemeListesi = request.getAidiyatGuncellemeKararDetayListesi();
            if (guncellemeListesi != null) {
                for (AidiyatGuncellemeKararDetay guncellemeBilgisi : guncellemeListesi) {
                    //Güncellemeye konu mahkeme karari bul
                    MahkemeKararDetay iliskiliMahkemeKararDetayRequest = guncellemeBilgisi.getMahkemeKararDetay();
                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = mahkemeKararRepo.findBy(iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetayRequest.getMahkemeKodu()
                            , iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetayRequest.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetayRequest.getMahkemeKodu(), iliskiliMahkemeKararDetayRequest.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetayRequest.getSorusturmaNo());
                        throw new MakosResponseException(errorStr);
                    }
                    MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();


                    DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId);
                    DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(detayMahkemeKararTalep);

                    List<AidiyatGuncellemeDetay> aidiyatGuncellemeListesi = guncellemeBilgisi.getAidiyatGuncellemeDetayListesi();
                    for (AidiyatGuncellemeDetay aidiyatGuncellemeDetay : aidiyatGuncellemeListesi) {

                        GuncellemeTip guncellemeTip = aidiyatGuncellemeDetay.getGuncellemeTip();
                        String aidiyatKodu = aidiyatGuncellemeDetay.getAidiyatKodu();

                        Optional<MahkemeKararAidiyat> mahkemeAidiyat = mahkemeKararAidiyatRepo.findByMahkemeKararIdAndAidiyatKod(iliskiliMahkemeKarar.getId(), aidiyatKodu);
                        if (guncellemeTip == GuncellemeTip.EKLE && mahkemeAidiyat.isPresent()) {
                            throw new Exception("Zaten var");
                        } else if (guncellemeTip == GuncellemeTip.CIKAR && mahkemeAidiyat.isEmpty()) {
                            throw new Exception("Böyle bir aidiyat zaten yok");
                        }

                        MahkemeAidiyatDetayTalep talep = new MahkemeAidiyatDetayTalep();
                        talep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());
                        //DetayMahkemeKararTalep'de olmasina ragmen burada yine de kaydediliyor.
                        talep.setMahkemeKararTalepId(mahkemeKararTalepId);
                        talep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
                        talep.setTarih(kayitTarihi);
                        if (guncellemeTip == GuncellemeTip.EKLE) {
                            talep.setMahkemeAidiyatKoduEkle(aidiyatGuncellemeDetay.getAidiyatKodu());
                        } else {
                            talep.setMahkemeAidiyatKoduCikar(aidiyatGuncellemeDetay.getAidiyatKodu());
                        }
                        MahkemeAidiyatDetayTalep savedMahkemeAidiyatDetayTalep = mahkemeAidiyatDetayTalepRepo.save(talep);
                    }
                }
            }

            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("IDAidiyatBilgisiGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

