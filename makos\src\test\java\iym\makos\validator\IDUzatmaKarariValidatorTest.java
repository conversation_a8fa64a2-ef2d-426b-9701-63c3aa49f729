package iym.makos.validator;

import iym.common.enums.EvrakKurum;
import iym.common.enums.HedefTip;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.enums.SureTip;
import iym.common.model.api.Hedef;
import iym.common.model.api.HedefWithAdSoyad;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.SucTipi;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbSucTipiService;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.testdata.TestDataBuilder;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mahkemekarar.id.IDUzatmaKarariRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDUzatmaKarariValidator.
 * 
 * Tests the validation logic for ID uzatma kararı requests.
 * Verifies business rules, edge cases, and error handling.
 * 
 * <AUTHOR> Team
 */
@DisplayName("IDUzatmaKarariValidator Unit Tests")
@MockitoSettings(strictness = Strictness.LENIENT)
class IDUzatmaKarariValidatorTest extends BaseDomainUnitTest {
    
    @Mock
    private DbMahkemeKararService mockDbMahkemeKararService;
    
    @Mock
    private DbHedeflerService mockDbHedeflerService;
    
    @Mock
    private DbSucTipiService mockDbSucTipiService;
    
    @Mock
    private MahkemeKararRequestCommonValidator mockCommonValidator;
    
    @InjectMocks
    private IDUzatmaKarariValidator validator;
    
    private IDUzatmaKarariRequest validRequest;
    private IDHedefDetay validHedefDetay;
    private MahkemeKararDetay validMahkemeKararDetay;
    
    @BeforeEach
    void setUp() throws Exception {
        // Setup valid test data
        validMahkemeKararDetay = MahkemeKararDetay.builder()
                .mahkemeIlIlceKodu("34001")
                .mahkemeKodu("001")
                .mahkemeKararNo("2023/123")
                .sorusturmaNo("2023/456")
                .build();
        
        // Create HedefWithAdSoyad
        Hedef hedef = Hedef.builder()
                .hedefNo("H001")
                .hedefTip(HedefTip.GSM)
                .build();

        HedefWithAdSoyad hedefWithAdSoyad = HedefWithAdSoyad.builder()
                .hedef(hedef)
                .hedefAd("Test")
                .hedefSoyad("User")
                .tcKimlikNo("12345678901")
                .build();

        validHedefDetay = IDHedefDetay.builder()
                .hedefNoAdSoyad(hedefWithAdSoyad)
                .baslamaTarihi(LocalDateTime.now())
                .sureTip(SureTip.AY)
                .sure(3)
                .uzatmaSayisi(1)
                .ilgiliMahkemeKararDetayi(validMahkemeKararDetay)
                .build();
        
        validRequest = IDUzatmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI)
                .evrakDetay(TestDataBuilder.createValidEvrakDetay())
                .mahkemeKararBilgisi(TestDataBuilder.createValidMahkemeKararBilgisi())
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("MT12345"))
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        
        // Setup common validator mock
        when(mockCommonValidator.validate(any())).thenReturn(new ValidationResult(true));
    }
    
    @Test
    @DisplayName("Should validate successfully when all conditions are met")
    void shouldValidateSuccessfully_whenAllConditionsAreMet() throws Exception {
        // Given
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isTrue();
        assertThat(result.getReasons()).isEmpty();
    }
    
    @Test
    @DisplayName("Should fail validation when OHAL date restriction violated")
    void shouldFailValidation_whenOHALDateRestrictionViolated() throws Exception {
        // Given
        // Create new MahkemeKararBilgisi with ADLI_KHK_YAZILI_EMIR type
        iym.makos.model.api.MahkemeKararBilgisi newMahkemeKararBilgisi =
                iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)
                        .mahkemeKararDetay(validRequest.getMahkemeKararBilgisi().getMahkemeKararDetay())
                        .build();

        validRequest = IDUzatmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI)
                .evrakDetay(TestDataBuilder.createValidEvrakDetay())
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("MT12345"))
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .mahkemeKararBilgisi(newMahkemeKararBilgisi)
                .build();
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
    }
    
    @Test
    @DisplayName("Should fail validation when CANAK number is provided")
    void shouldFailValidation_whenCanakNumberProvided() throws Exception {
        // Given
        // Create new IDHedefDetay with CANAK number
        IDHedefDetay newHedefDetay = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(validHedefDetay.getUzatmaSayisi())
                .ilgiliMahkemeKararDetayi(validHedefDetay.getIlgiliMahkemeKararDetayi())
                .canakNo("CANAK123")
                .build();

        validRequest = createTestRequest(Arrays.asList(newHedefDetay),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Uzatma kararında CANAK numarası girilemez.");
    }
    
    @Test
    @DisplayName("Should fail validation when uzatma sayisi is null")
    void shouldFailValidation_whenUzatmaSayisiIsNull() throws Exception {
        // Given
        // Create new IDHedefDetay with null uzatmaSayisi
        IDHedefDetay newHedefDetay = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(null)
                .ilgiliMahkemeKararDetayi(validHedefDetay.getIlgiliMahkemeKararDetayi())
                .build();

        validRequest = createTestRequest(Arrays.asList(newHedefDetay),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Uzatma Kararinda uzatma sayisi boş olamaz!");
    }
    
    @Test
    @DisplayName("Should fail validation when ilgili mahkeme karar is null")
    void shouldFailValidation_whenIlgiliMahkemeKararIsNull() throws Exception {
        // Given
        // Create new IDHedefDetay with null ilgiliMahkemeKararDetayi
        IDHedefDetay newHedefDetay = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(validHedefDetay.getUzatmaSayisi())
                .ilgiliMahkemeKararDetayi(null)
                .build();

        validRequest = createTestRequest(Arrays.asList(newHedefDetay),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Uzatma Kararinda ilgili mahkeme karari bos olamaz!");
    }
    
    @Test
    @DisplayName("Should fail validation when related mahkeme karar not found in database")
    void shouldFailValidation_whenRelatedMahkemeKararNotFound() throws Exception {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.empty());
        setupOtherValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .anyMatch(reason -> reason.contains("Mahkeme Karar Bulunamadı"));
    }
    
    @Test
    @DisplayName("Should fail validation when hedef not found in database")
    void shouldFailValidation_whenHedefNotFound() throws Exception {
        // Given
        MahkemeKarar mockMahkemeKarar = new MahkemeKarar();
        mockMahkemeKarar.setId(1L);
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKarar));
        when(mockDbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(any(), anyString(), any()))
                .thenReturn(Optional.empty());
        setupOtherValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .anyMatch(reason -> reason.contains("H001 numaralı hedef  ilişkli mahkeme kararda bulunamadı"));
    }
    
    @Test
    @DisplayName("Should fail validation when invalid suc tipi code provided")
    void shouldFailValidation_whenInvalidSucTipiCode() throws Exception {
        // Given
        when(mockDbSucTipiService.findBySucTipiKodu("INVALID_SUC"))
                .thenReturn(Optional.empty());
        validRequest = createTestRequest(Arrays.asList(validHedefDetay),
                Arrays.asList("MT12345"), Arrays.asList("INVALID_SUC"));
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("'INVALID_SUC' kodu ile eşleşen bir suç tipi mevcut değil.");
    }
    
    @Test
    @DisplayName("Should handle null suc tipi kodlari gracefully")
    void shouldHandleNullSucTipiKodlari_gracefully() throws Exception {
        // Given
        validRequest = createTestRequest(Arrays.asList(validHedefDetay),
                Arrays.asList("MT12345"), null);
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isTrue();
    }
    
    @Test
    @DisplayName("Should handle empty suc tipi kodlari gracefully")
    void shouldHandleEmptySucTipiKodlari_gracefully() throws Exception {
        // Given
        validRequest = createTestRequest(Arrays.asList(validHedefDetay),
                Arrays.asList("MT12345"), Collections.emptyList());
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isTrue();
    }
    
    @Test
    @DisplayName("Should handle null aidiyat kodlari gracefully")
    void shouldHandleNullAidiyatKodlari_gracefully() throws Exception {
        // Given
        validRequest = createTestRequest(Arrays.asList(validHedefDetay),
                null, Arrays.asList("SUC001"));
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isTrue();
    }
    
    @Test
    @DisplayName("Should return correct related karar turu")
    void shouldReturnCorrectRelatedKararTuru() {
        // When
        KararTuru result = validator.getRelatedKararTuru();
        
        // Then
        assertThat(result).isEqualTo(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI);
    }
    
    @Test
    @DisplayName("Should handle validation exception gracefully")
    void shouldHandleValidationException_gracefully() throws Exception {
        // Given
        when(mockCommonValidator.validate(any())).thenThrow(new RuntimeException("Database error"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should fail validation for ADLI_KHK_YAZILI_EMIR after OHAL date")
    void shouldFailValidation_forAdliKhkYaziliEmirAfterOhalDate() throws Exception {
        // Given - Create request with ADLI_KHK_YAZILI_EMIR type (which is after OHAL date)
        iym.makos.model.api.MahkemeKararBilgisi ohalKararBilgisi =
                iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)
                        .mahkemeKararDetay(validRequest.getMahkemeKararBilgisi().getMahkemeKararDetay())
                        .build();

        validRequest = IDUzatmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI)
                .evrakDetay(TestDataBuilder.createValidEvrakDetay())
                .mahkemeKararBilgisi(ohalKararBilgisi)
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("MT12345"))
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
    }

    @Test
    @DisplayName("Should fail validation when CANAK number is provided")
    void shouldFailValidation_whenCanakNumberIsProvided() throws Exception {
        // Given - Create hedef detay with CANAK number
        IDHedefDetay hedefDetayWithCanak = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(1)
                .canakNo("CANAK123") // This should cause validation failure
                .ilgiliMahkemeKararDetayi(validHedefDetay.getIlgiliMahkemeKararDetayi())
                .build();

        validRequest = createTestRequest(Arrays.asList(hedefDetayWithCanak),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Uzatma kararında CANAK numarası girilemez.");
    }

    @Test
    @DisplayName("Should fail validation when uzatma sayisi is null - additional test")
    void shouldFailValidation_whenUzatmaSayisiIsNull_additionalTest() throws Exception {
        // Given - Create hedef detay with null uzatma sayisi
        IDHedefDetay hedefDetayWithNullUzatma = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(null) // This should cause validation failure
                .ilgiliMahkemeKararDetayi(validHedefDetay.getIlgiliMahkemeKararDetayi())
                .build();

        validRequest = createTestRequest(Arrays.asList(hedefDetayWithNullUzatma),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Uzatma Kararinda uzatma sayisi boş olamaz!");
    }

    @Test
    @DisplayName("Should fail validation when ilgili mahkeme karar detayi is null")
    void shouldFailValidation_whenIlgiliMahkemeKararDetayiIsNull() throws Exception {
        // Given - Create hedef detay with null ilgili mahkeme karar detayi
        IDHedefDetay hedefDetayWithNullKarar = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(1)
                .ilgiliMahkemeKararDetayi(null) // This should cause validation failure
                .build();

        validRequest = createTestRequest(Arrays.asList(hedefDetayWithNullKarar),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Uzatma Kararinda ilgili mahkeme karari bos olamaz!");
    }

    @Test
    @DisplayName("Should fail validation when mahkeme karar is not found")
    void shouldFailValidation_whenMahkemeKararIsNotFound() throws Exception {
        // Given - Mock mahkeme karar service to return empty
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.empty());
        setupOtherValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .anyMatch(reason -> reason.contains("Mahkeme Karar Bulunamadı"));
    }

    @Test
    @DisplayName("Should fail validation when hedef is not found in related mahkeme karar")
    void shouldFailValidation_whenHedefIsNotFoundInRelatedMahkemeKarar() throws Exception {
        // Given - Mock mahkeme karar service to return valid karar but hedef service to return empty
        MahkemeKarar mockMahkemeKarar = new MahkemeKarar();
        mockMahkemeKarar.setId(1L);

        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKarar));
        when(mockDbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(any(), anyString(), any()))
                .thenReturn(Optional.empty()); // Hedef not found
        setupOtherValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("H001 numaralı hedef  ilişkli mahkeme kararda bulunamadı.");
    }

    @Test
    @DisplayName("Should fail validation when suc tipi is not found")
    void shouldFailValidation_whenSucTipiIsNotFound() throws Exception {
        // Given - Mock suc tipi service to return empty for specific code
        when(mockDbSucTipiService.findBySucTipiKodu("INVALID_SUC"))
                .thenReturn(Optional.empty());

        validRequest = createTestRequest(Arrays.asList(validHedefDetay),
                Arrays.asList("MT12345"), Arrays.asList("INVALID_SUC"));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("'INVALID_SUC' kodu ile eşleşen bir suç tipi mevcut değil.");
    }

    @Test
    @DisplayName("Should handle exception in doValidate method")
    void shouldHandleException_inDoValidateMethod() throws Exception {
        // Given - Mock to throw exception
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Database error"));
        setupOtherValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should validate with empty aidiyat and suc tipi lists")
    void shouldValidate_withEmptyAidiyatAndSucTipiLists() throws Exception {
        // Given - Request with empty lists
        validRequest = createTestRequest(Arrays.asList(validHedefDetay),
                Collections.emptyList(), Collections.emptyList());
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isTrue();
    }

    @Test
    @DisplayName("Should validate with null aidiyat and suc tipi lists")
    void shouldValidate_withNullAidiyatAndSucTipiLists() throws Exception {
        // Given - Request with null lists
        validRequest = createTestRequest(Arrays.asList(validHedefDetay), null, null);
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isTrue();
    }
    
    private void setupValidMocks() {
        MahkemeKarar mockMahkemeKarar = new MahkemeKarar();
        mockMahkemeKarar.setId(1L);
        
        Hedefler mockHedef = new Hedefler();
        mockHedef.setId(1L);
        
        SucTipi mockSucTipi = new SucTipi();
        mockSucTipi.setSucTipiKodu("SUC001");
        
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKarar));
        when(mockDbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(any(), anyString(), any()))
                .thenReturn(Optional.of(mockHedef));
        when(mockDbSucTipiService.findBySucTipiKodu("SUC001"))
                .thenReturn(Optional.of(mockSucTipi));
    }
    
    private void setupOtherValidMocks() {
        SucTipi mockSucTipi = new SucTipi();
        mockSucTipi.setSucTipiKodu("SUC001");

        when(mockDbSucTipiService.findBySucTipiKodu("SUC001"))
                .thenReturn(Optional.of(mockSucTipi));
    }

    private IDUzatmaKarariRequest createTestRequest(List<IDHedefDetay> hedefDetayListesi,
                                                   List<String> aidiyatKodlari,
                                                   List<String> sucTipiKodlari) {
        return IDUzatmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI)
                .evrakDetay(TestDataBuilder.createValidEvrakDetay())
                .mahkemeKararBilgisi(TestDataBuilder.createValidMahkemeKararBilgisi())
                .hedefDetayListesi(hedefDetayListesi)
                .mahkemeAidiyatKodlari(aidiyatKodlari)
                .mahkemeSucTipiKodlari(sucTipiKodlari)
                .build();
    }
}
