package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for MahkemeAidiyatTalep entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Mahkeme Aidiyat  bilgilerini içerir")
public class MahkemeAidiyatDTO {

    @Schema(description = "Mahkeme aidiyat  ID")
    private Long id;

    @Schema(description = "İlişkili mahkeme karar ID")
    @NotNull(message = "Mahkeme ID boş olamaz")
    private Long mahkemeKararId;

    @Schema(description = "Aidiyat kodu", example = "02")
    @NotNull(message = "<PERSON><PERSON><PERSON> kodu boş olamaz")
    @Size(max = 25, message = "Aidiyat kodu 25 karakterden fazla olamaz")
    private String aidiyatKod;

    @Schema(description = "Durum", example = "AKTIF")
    @Size(max = 10, message = "Durum 10 karakterden fazla olamaz")
    private String durumu;
}
