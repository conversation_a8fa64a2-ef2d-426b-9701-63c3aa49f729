package iym.makos.service.db;

import iym.common.model.entity.iym.talep.MahkemeHedeflerAidiyatTalep;
import iym.common.service.db.DbMahkemeHedeflerAidiyatTalepService;
import iym.makos.model.dto.db.MahkemeHedeflerAidiyatTalepDTO;
import iym.makos.mapper.MahkemeHedeflerAidiyatTalepMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service for MahkemeHedeflerAidiyatTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeHedeflerAidiyatTalepService {

    private final DbMahkemeHedeflerAidiyatTalepService dbMahkemeHedeflerAidiyatTalepService;
    private final MahkemeHedeflerAidiyatTalepMapper mahkemeHedeflerAidiyatTalepMapper;

    /**
     * Get all mahkeme hedefler aidiyat talep records
     * @return List of MahkemeHedeflerAidiyatTalepDTO
     */
    public List<MahkemeHedeflerAidiyatTalepDTO> findAll() {
        List<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepList = dbMahkemeHedeflerAidiyatTalepService.findAll();
        return mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    /**
     * Get all mahkeme hedefler aidiyat talep records with pagination
     * @param pageable Pagination information
     * @return Page of MahkemeHedeflerAidiyatTalepDTO
     */
    public Page<MahkemeHedeflerAidiyatTalepDTO> findAll(Pageable pageable) {
        Page<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepPage = dbMahkemeHedeflerAidiyatTalepService.findAll(pageable);
        List<MahkemeHedeflerAidiyatTalepDTO> dtoList = mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, mahkemeHedeflerAidiyatTalepPage.getTotalElements());
    }

    /**
     * Get mahkeme hedefler aidiyat talep by id
     * @param id MahkemeHedeflerAidiyatTalep id
     * @return MahkemeHedeflerAidiyatTalepDTO
     */
    public MahkemeHedeflerAidiyatTalepDTO findById(Long id) {
        MahkemeHedeflerAidiyatTalep mahkemeHedeflerAidiyatTalep = dbMahkemeHedeflerAidiyatTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme hedefler aidiyat talep bulunamadı: " + id));
        return mahkemeHedeflerAidiyatTalepMapper.toDto(mahkemeHedeflerAidiyatTalep);
    }

    /**
     * Get mahkeme hedefler aidiyat talep by hedef id
     * @param hedefId Hedef id
     * @return List of MahkemeHedeflerAidiyatTalepDTO
     */
    public List<MahkemeHedeflerAidiyatTalepDTO> findByHedefId(Long hedefId) {
        List<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepList = dbMahkemeHedeflerAidiyatTalepService.findByHedefId(hedefId);
        return mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    /**
     * Get mahkeme hedefler aidiyat talep by mahkeme karar id
     * @param mahkemeKararId Mahkeme karar id
     * @return List of MahkemeHedeflerAidiyatTalepDTO
     */
    public List<MahkemeHedeflerAidiyatTalepDTO> findByMahkemeKararId(Long mahkemeKararId) {
        List<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepList = dbMahkemeHedeflerAidiyatTalepService.findByMahkemeKararId(mahkemeKararId);
        return mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    /**
     * Get mahkeme hedefler aidiyat talep by aidiyat kod
     * @param aidiyatKod Aidiyat kod
     * @return List of MahkemeHedeflerAidiyatTalepDTO
     */
    public List<MahkemeHedeflerAidiyatTalepDTO> findByAidiyatKod(String aidiyatKod) {
        List<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepList = dbMahkemeHedeflerAidiyatTalepService.findByAidiyatKod(aidiyatKod);
        return mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    /**
     * Get mahkeme hedefler aidiyat talep by durumu
     * @param durumu Durumu
     * @return List of MahkemeHedeflerAidiyatTalepDTO
     */
    public List<MahkemeHedeflerAidiyatTalepDTO> findByDurumu(String durumu) {
        List<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepList = dbMahkemeHedeflerAidiyatTalepService.findByDurumu(durumu);
        return mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    /**
     * Get mahkeme hedefler aidiyat talep by kullanici id
     * @param kullaniciId Kullanici id
     * @return List of MahkemeHedeflerAidiyatTalepDTO
     */
    public List<MahkemeHedeflerAidiyatTalepDTO> findByKullaniciId(Long kullaniciId) {
        List<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepList = dbMahkemeHedeflerAidiyatTalepService.findByKullaniciId(kullaniciId);
        return mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    /**
     * Get mahkeme hedefler aidiyat talep by tarih between
     * @param startDate Start date
     * @param endDate End date
     * @return List of MahkemeHedeflerAidiyatTalepDTO
     */
    public List<MahkemeHedeflerAidiyatTalepDTO> findByTarihBetween(Date startDate, Date endDate) {
        List<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepList = dbMahkemeHedeflerAidiyatTalepService.findByTarihBetween(startDate, endDate);
        return mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    /**
     * Get mahkeme hedefler aidiyat talep by hedef id, aidiyat kod and mahkeme karar id
     * @param hedefId Hedef id
     * @param aidiyatKod Aidiyat kod
     * @param mahkemeKararId Mahkeme karar id
     * @return MahkemeHedeflerAidiyatTalepDTO
     */
    public MahkemeHedeflerAidiyatTalepDTO findByHedefIdAndAidiyatKodAndMahkemeKararId(
            Long hedefId,
            String aidiyatKod,
            Long mahkemeKararId) {
        MahkemeHedeflerAidiyatTalep mahkemeHedeflerAidiyatTalep = dbMahkemeHedeflerAidiyatTalepService.findByHedefIdAndAidiyatKodAndMahkemeKararId(
                hedefId, aidiyatKod, mahkemeKararId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                        "Mahkeme hedefler aidiyat talep bulunamadı: Hedef ID: " + hedefId +
                        ", Aidiyat Kod: " + aidiyatKod +
                        ", Mahkeme Karar ID: " + mahkemeKararId));
        return mahkemeHedeflerAidiyatTalepMapper.toDto(mahkemeHedeflerAidiyatTalep);
    }

    /**
     * Get mahkeme hedefler aidiyat talep by hedef id and mahkeme karar id
     * @param hedefId Hedef id
     * @param mahkemeKararId Mahkeme karar id
     * @return List of MahkemeHedeflerAidiyatTalepDTO
     */
    public List<MahkemeHedeflerAidiyatTalepDTO> findByHedefIdAndMahkemeKararId(Long hedefId, Long mahkemeKararId) {
        List<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepList = dbMahkemeHedeflerAidiyatTalepService.findByHedefIdAndMahkemeKararId(hedefId, mahkemeKararId);
        return mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    /**
     * Create new mahkeme hedefler aidiyat talep
     * @param mahkemeHedeflerAidiyatTalepDTO MahkemeHedeflerAidiyatTalepDTO
     * @return Created MahkemeHedeflerAidiyatTalepDTO
     */
    public MahkemeHedeflerAidiyatTalepDTO create(MahkemeHedeflerAidiyatTalepDTO mahkemeHedeflerAidiyatTalepDTO) {
        // Check if mahkeme hedefler aidiyat talep already exists
        if (mahkemeHedeflerAidiyatTalepDTO.getHedefId() != null &&
            mahkemeHedeflerAidiyatTalepDTO.getAidiyatKod() != null &&
            mahkemeHedeflerAidiyatTalepDTO.getMahkemeKararId() != null) {

            Optional<MahkemeHedeflerAidiyatTalep> existingMahkemeHedeflerAidiyatTalep = dbMahkemeHedeflerAidiyatTalepService.findByHedefIdAndAidiyatKodAndMahkemeKararId(
                    mahkemeHedeflerAidiyatTalepDTO.getHedefId(),
                    mahkemeHedeflerAidiyatTalepDTO.getAidiyatKod(),
                    mahkemeHedeflerAidiyatTalepDTO.getMahkemeKararId());

            if (existingMahkemeHedeflerAidiyatTalep.isPresent()) {
                throw new ResponseStatusException(HttpStatus.CONFLICT,
                    "Bu mahkeme hedefler aidiyat talebi zaten mevcut: Hedef ID: " + mahkemeHedeflerAidiyatTalepDTO.getHedefId() +
                    ", Aidiyat Kod: " + mahkemeHedeflerAidiyatTalepDTO.getAidiyatKod() +
                    ", Mahkeme Karar ID: " + mahkemeHedeflerAidiyatTalepDTO.getMahkemeKararId());
            }
        }

        MahkemeHedeflerAidiyatTalep mahkemeHedeflerAidiyatTalep = mahkemeHedeflerAidiyatTalepMapper.toEntity(mahkemeHedeflerAidiyatTalepDTO);
        dbMahkemeHedeflerAidiyatTalepService.save(mahkemeHedeflerAidiyatTalep);
        log.info("Mahkeme hedefler aidiyat talep oluşturuldu: {}", mahkemeHedeflerAidiyatTalep.getId());
        return mahkemeHedeflerAidiyatTalepMapper.toDto(mahkemeHedeflerAidiyatTalep);
    }

    /**
     * Update existing mahkeme hedefler aidiyat talep
     * @param id MahkemeHedeflerAidiyatTalep id
     * @param mahkemeHedeflerAidiyatTalepDTO MahkemeHedeflerAidiyatTalepDTO
     * @return Updated MahkemeHedeflerAidiyatTalepDTO
     */
    public MahkemeHedeflerAidiyatTalepDTO update(Long id, MahkemeHedeflerAidiyatTalepDTO mahkemeHedeflerAidiyatTalepDTO) {
        MahkemeHedeflerAidiyatTalep existingMahkemeHedeflerAidiyatTalep = dbMahkemeHedeflerAidiyatTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme hedefler aidiyat talep bulunamadı: " + id));

        // Check if updated mahkeme hedefler aidiyat talep would conflict with an existing one
        if (mahkemeHedeflerAidiyatTalepDTO.getHedefId() != null &&
            mahkemeHedeflerAidiyatTalepDTO.getAidiyatKod() != null &&
            mahkemeHedeflerAidiyatTalepDTO.getMahkemeKararId() != null) {

            Optional<MahkemeHedeflerAidiyatTalep> conflictingMahkemeHedeflerAidiyatTalep = dbMahkemeHedeflerAidiyatTalepService.findByHedefIdAndAidiyatKodAndMahkemeKararId(
                    mahkemeHedeflerAidiyatTalepDTO.getHedefId(),
                    mahkemeHedeflerAidiyatTalepDTO.getAidiyatKod(),
                    mahkemeHedeflerAidiyatTalepDTO.getMahkemeKararId());

            if (conflictingMahkemeHedeflerAidiyatTalep.isPresent() && !conflictingMahkemeHedeflerAidiyatTalep.get().getId().equals(id)) {
                throw new ResponseStatusException(HttpStatus.CONFLICT,
                    "Bu mahkeme hedefler aidiyat talebi zaten mevcut: Hedef ID: " + mahkemeHedeflerAidiyatTalepDTO.getHedefId() +
                    ", Aidiyat Kod: " + mahkemeHedeflerAidiyatTalepDTO.getAidiyatKod() +
                    ", Mahkeme Karar ID: " + mahkemeHedeflerAidiyatTalepDTO.getMahkemeKararId());
            }
        }

        MahkemeHedeflerAidiyatTalep updatedMahkemeHedeflerAidiyatTalep = mahkemeHedeflerAidiyatTalepMapper.updateEntityFromDto(existingMahkemeHedeflerAidiyatTalep, mahkemeHedeflerAidiyatTalepDTO);
        dbMahkemeHedeflerAidiyatTalepService.update(updatedMahkemeHedeflerAidiyatTalep);
        log.info("Mahkeme hedefler aidiyat talep güncellendi: {}", updatedMahkemeHedeflerAidiyatTalep.getId());
        return mahkemeHedeflerAidiyatTalepMapper.toDto(updatedMahkemeHedeflerAidiyatTalep);
    }

    /**
     * Delete mahkeme hedefler aidiyat talep
     * @param id MahkemeHedeflerAidiyatTalep id
     */
    public void delete(Long id) {
        MahkemeHedeflerAidiyatTalep mahkemeHedeflerAidiyatTalep = dbMahkemeHedeflerAidiyatTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme hedefler aidiyat talep bulunamadı: " + id));
        dbMahkemeHedeflerAidiyatTalepService.delete(mahkemeHedeflerAidiyatTalep);
        log.info("Mahkeme hedefler aidiyat talep silindi: {}", id);
    }
}
