package iym.makos.mapper;

import iym.common.model.entity.iym.Gorevler2;
import iym.common.model.entity.iym.SucTipi;
import iym.makos.model.dto.db.Gorevler2DTO;
import iym.makos.model.dto.db.SucTipiDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for MahkemeBilgiMapper entity and DTO
 */
@Component
public class SucTipiMapper {


    public SucTipiDTO toDto(SucTipi entity) {
        if (entity == null) {
            return null;
        }

        return SucTipiDTO.builder()
                .sucTipiKodu(entity.getSucTipiKodu())
                .aciklama(entity.getAciklama())
                .mahkemeKaraTipiKodu(entity.getMahkemeKaraTipiKodu())
                .durum(entity.getDurum())
                .build();
    }


    public SucTipi toEntity(SucTipiDTO dto) {
        if (dto == null) {
            return null;
        }

        return SucTipi.builder()
                .sucTipiKodu(dto.getSucTipiKodu())
                .aciklama(dto.getAciklama())
                .mahkemeKaraTipiKodu(dto.getMahkemeKaraTipiKodu())
                .durum(dto.getDurum())
                .build();
    }

    public List<SucTipiDTO> toDtoList(List<SucTipi> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

}
