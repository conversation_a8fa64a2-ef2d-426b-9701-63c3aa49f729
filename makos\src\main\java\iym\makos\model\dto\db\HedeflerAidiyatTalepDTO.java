package iym.makos.model.dto.db;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for HedeflerAidiyatTalep entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Hedefler Aidiyat Talep bilgilerini içerir")
public class HedeflerAidiyatTalepDTO {

    @Schema(description = "Hedefler aidiyat talep ID")
    private Long id;

    @Schema(description = "Hedef ID")
    @NotNull(message = "Hedef ID boş olamaz")
    private Long hedefTalepId;

    @Schema(description = "Aidiyat kodu", example = "AIDIYAT1")
    @NotNull(message = "Aidiyat kodu boş olamaz")
    @Size(max = 15, message = "Aidiyat kodu 15 karakterden fazla olamaz")
    private String aidiyatKod;

    @Schema(description = "Tarih", example = "2023-01-01T00:00:00")
    @NotNull(message = "Tarih boş olamaz")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date tarih;

    @Schema(description = "Kullanıcı ID")
    @NotNull(message = "Kullanıcı ID boş olamaz")
    private Long kullaniciId;

    @Schema(description = "Durum", example = "AKTIF")
    @Size(max = 15, message = "Durum 15 karakterden fazla olamaz")
    private String durumu;
}
