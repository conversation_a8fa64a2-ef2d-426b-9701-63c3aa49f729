package iym.makos.service.db;

import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.model.entity.iym.sorgu.MahkemeKararTalepSorguInfo;
import iym.common.service.db.DbMahkemeKararTalepService;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.mapper.MahkemeKararTalepMapper;
import iym.makos.mapper.MahkemeKararTalepSorguMapper;
import iym.makos.model.dto.db.MahkemeKararTalepDTO;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mktalep.IDMahkemeKararTalepSorgulamaRequest;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mktalep.MahkemeKararTalepIslenecekView;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mktalep.MahkemeKararTalepSorguView;
import iym.makos.utils.UtilService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for MahkemeKararTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeKararTalepService {

    private final UtilService utilService;


    private final DbMahkemeKararTalepService dbMahkemeKararTalepService;

    private final MahkemeKararTalepMapper mahkemeKararTalepMapper;

    private final MahkemeKararTalepSorguMapper mahkemeKararTalepSorguMapper;

    private final MahkemeKararTalepRepo mahkemeKararTalepRepo;
/*
    @Autowired
    public MahkemeKararTalepService(UtilService utilService, DbMahkemeKararTalepService dbMahkemeKararTalepService
            , MahkemeKararTalepMapper mahkemeKararTalepMapper
            , MahkemeKararTalepSorguMapper mahkemeKararTalepSorguMapper
            , MahkemeKararTalepRepoDynamicQueries mahkemeKararTalepRepoDynamicQueries) {
        this.utilService = utilService;
        this.dbMahkemeKararTalepService = dbMahkemeKararTalepService;
        this.mahkemeKararTalepMapper = mahkemeKararTalepMapper;
        this.mahkemeKararTalepSorguMapper = mahkemeKararTalepSorguMapper;
        this.mahkemeKararTalepRepoDynamicQueries = mahkemeKararTalepRepoDynamicQueries;
    }
*/

    public List<MahkemeKararTalepIslenecekView> islenecekMahkemeKararTalepleri(UserDetailsImpl userInfo) {
        log.info("'{}' icin islenecekMahkemeKararTalep listesi sorgulama", userInfo.getUsername());
        String kurumKodu = userInfo.getKullaniciKurum().getValue();

        List<MahkemeKararTalepSorguInfo> islenecekMahkemeKararTalepListesi = mahkemeKararTalepRepo.islenecekMahkemeKararTalepListesi(kurumKodu);
        List<MahkemeKararTalepIslenecekView> result = CommonUtils.safeList(islenecekMahkemeKararTalepListesi
                ).stream()
                .map(mahkemeKararTalepSorguMapper::toKararTalepViewInfo)
                .collect(Collectors.toList());

        return result;
    }

    public List<MahkemeKararTalepSorguView> mahkemeKararTalepSorgu(UserDetailsImpl userInfo, IDMahkemeKararTalepSorgulamaRequest sorguParam) {

        List<MahkemeKararTalepSorguInfo> islenecekMahkemeKararTalepListesi = mahkemeKararTalepRepo.mahkemeKararTalepSorgu(userInfo.getKullaniciKurum().getValue(), sorguParam.getSorguParam());
        List<MahkemeKararTalepSorguView> result = CommonUtils.safeList(islenecekMahkemeKararTalepListesi
                ).stream()
                .map(mahkemeKararTalepSorguMapper::toMahkemeKararTalepSorguView)
                .collect(Collectors.toList());

        return result;

    }


    //TODO :
    public MahkemeKararTalepDTO findById(Long id) {
        return dbMahkemeKararTalepService.findById(id)
                .map(mahkemeKararTalepMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar talep bulunamadı: " + id));
    }

    /**
     * Get mahkeme karar talep records by evrak ID
     *
     * @param evrakId Evrak ID
     * @return List of MahkemeKararTalepDTO
     */

    public List<MahkemeKararTalepDTO> findByEvrakId(Long evrakId) {
        List<MahkemeKararTalep> mahkemeKararTalepList = dbMahkemeKararTalepService.findByEvrakId(evrakId);
        return mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepList);
    }


    /**
     * Get paginated mahkeme karar talep records
     *
     * @param pageable Pageable
     * @return Page of MahkemeKararTalepDTO
     */

    public Page<MahkemeKararTalepDTO> findAll(Pageable pageable) {
        Page<MahkemeKararTalep> mahkemeKararTalepPage = dbMahkemeKararTalepService.findAll(pageable);
        List<MahkemeKararTalepDTO> dtoList = mahkemeKararTalepMapper.toDtoList(mahkemeKararTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, mahkemeKararTalepPage.getTotalElements());
    }

    /**
     * Create new mahkeme karar talep
     *
     * @param mahkemeKararTalepDTO MahkemeKararTalepDTO
     * @return Created MahkemeKararTalepDTO
     */

    public MahkemeKararTalepDTO create(MahkemeKararTalepDTO mahkemeKararTalepDTO) {
        // Check if mahkeme karar already exists
        if (mahkemeKararTalepDTO.getMahkemeKararNo() != null &&
                mahkemeKararTalepDTO.getMahkemeAdi() != null &&
                mahkemeKararTalepDTO.getMahkemeIlIlceKodu() != null) {
            // TODO
        }

        MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepMapper.toEntity(mahkemeKararTalepDTO);
        dbMahkemeKararTalepService.save(mahkemeKararTalep);
        log.info("Mahkeme karar talep oluşturuldu: {}", mahkemeKararTalep.getId());
        return mahkemeKararTalepMapper.toDto(mahkemeKararTalep);
    }

    /**
     * Update mahkeme karar talep
     *
     * @param id                   Mahkeme karar talep ID
     * @param mahkemeKararTalepDTO MahkemeKararTalepDTO
     * @return Updated MahkemeKararTalepDTO
     */

    public MahkemeKararTalepDTO update(Long id, MahkemeKararTalepDTO mahkemeKararTalepDTO) {
        MahkemeKararTalep existingMahkemeKararTalep = dbMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar talep bulunamadı: " + id));

        // Check if mahkeme karar is being changed and already exists
        if (mahkemeKararTalepDTO.getMahkemeKararNo() != null &&
                mahkemeKararTalepDTO.getMahkemeAdi() != null &&
                mahkemeKararTalepDTO.getMahkemeIlIlceKodu() != null &&
                (!mahkemeKararTalepDTO.getMahkemeKararNo().equals(existingMahkemeKararTalep.getMahkemeKararNo()) ||
                        !mahkemeKararTalepDTO.getMahkemeAdi().equals(existingMahkemeKararTalep.getMahkemeAdi()) ||
                        !mahkemeKararTalepDTO.getMahkemeIlIlceKodu().equals(existingMahkemeKararTalep.getMahkemeIlIlceKodu()))) {

            // TODO
        }

        MahkemeKararTalep updatedMahkemeKararTalep = mahkemeKararTalepMapper.updateEntityFromDto(existingMahkemeKararTalep, mahkemeKararTalepDTO);
        dbMahkemeKararTalepService.update(updatedMahkemeKararTalep);
        log.info("Mahkeme karar talep güncellendi: {}", updatedMahkemeKararTalep.getId());
        return mahkemeKararTalepMapper.toDto(updatedMahkemeKararTalep);
    }

    /**
     * Delete mahkeme karar talep
     *
     * @param id Mahkeme karar talep ID
     */

    public void delete(Long id) {
        MahkemeKararTalep mahkemeKararTalep = dbMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar talep bulunamadı: " + id));

        dbMahkemeKararTalepService.delete(mahkemeKararTalep);
        log.info("Mahkeme karar talep silindi: {}", id);
    }


}
