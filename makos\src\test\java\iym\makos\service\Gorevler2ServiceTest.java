package iym.makos.service;

import iym.common.model.entity.iym.Gorevler2;
import iym.common.model.entity.iym.Gorevler2PK;
import iym.common.service.db.DbGorevler2Service;
import iym.makos.mapper.Gorevler2Mapper;
import iym.makos.service.db.Gorevler2Service;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.model.dto.db.Gorevler2DTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class Gorevler2ServiceTest {

    @Mock
    private DbGorevler2Service dbGorevler2Service;

    @Mock
    private Gorevler2Mapper gorevler2Mapper;

    @InjectMocks
    private Gorevler2Service gorevler2Service;

    private Gorevler2 gorevler2;
    private Gorevler2DTO gorevler2DTO;
    private List<Gorevler2> gorevler2List;
    private List<Gorevler2DTO> gorevler2DTOList;
    private Date testDate;

    @BeforeEach
    void setUp() {
        testDate = new Date();

        gorevler2 = Gorevler2.builder()
                .gorev("Yönetici")
                .gorevKodu(1L)
                .gorevImzaAdi("Yönetici İmza")
                .imzaYetki("E")
                .silindi(0L)
                .gorevKodu2("YON")
                .gorevTipi("Yönetim")
                .oncelik(1L)
                .baslamaTarihi(testDate)
                .bitisTarihi(testDate)
                .build();

        Gorevler2 gorevler22 = Gorevler2.builder()
                .gorev("Müdür")
                .gorevKodu(2L)
                .gorevImzaAdi("Müdür İmza")
                .imzaYetki("E")
                .silindi(0L)
                .gorevKodu2("MUD")
                .gorevTipi("Müdürlük")
                .oncelik(2L)
                .baslamaTarihi(testDate)
                .bitisTarihi(testDate)
                .build();

        gorevler2DTO = Gorevler2DTO.builder()
                .gorev("Yönetici")
                .gorevKodu(1L)
                .gorevImzaAdi("Yönetici İmza")
                .imzaYetki("E")
                .silindi(0L)
                .gorevKodu2("YON")
                .gorevTipi("Yönetim")
                .oncelik(1L)
                .baslamaTarihi(testDate)
                .bitisTarihi(testDate)
                .build();

        Gorevler2DTO gorevler2DTO2 = Gorevler2DTO.builder()
                .gorev("Müdür")
                .gorevKodu(2L)
                .gorevImzaAdi("Müdür İmza")
                .imzaYetki("E")
                .silindi(0L)
                .gorevKodu2("MUD")
                .gorevTipi("Müdürlük")
                .oncelik(2L)
                .baslamaTarihi(testDate)
                .bitisTarihi(testDate)
                .build();

        gorevler2List = Arrays.asList(gorevler2, gorevler22);
        gorevler2DTOList = Arrays.asList(gorevler2DTO, gorevler2DTO2);
    }

    @Test
    void findAll_shouldReturnAllGorevler2() {
        // Given
        when(dbGorevler2Service.findAll()).thenReturn(gorevler2List);
        when(gorevler2Mapper.toDtoList(gorevler2List)).thenReturn(gorevler2DTOList);

        // When
        List<Gorevler2DTO> result = gorevler2Service.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(gorevler2DTOList);
        verify(dbGorevler2Service).findAll();
        verify(gorevler2Mapper).toDtoList(gorevler2List);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfGorevler2() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<Gorevler2> gorevler2Page = new PageImpl<>(gorevler2List, pageable, gorevler2List.size());
        
        when(dbGorevler2Service.findAll(pageable)).thenReturn(gorevler2Page);
        when(gorevler2Mapper.toDtoList(gorevler2List)).thenReturn(gorevler2DTOList);

        // When
        Page<Gorevler2DTO> result = gorevler2Service.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(gorevler2DTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbGorevler2Service).findAll(pageable);
        verify(gorevler2Mapper).toDtoList(gorevler2List);
    }

    @Test
    void findByGorevAndGorevKodu_shouldReturnGorevler2_whenExists() {
        // Given
        when(dbGorevler2Service.findByGorevAndGorevKodu("Yönetici", 1L)).thenReturn(Optional.of(gorevler2));
        when(gorevler2Mapper.toDto(gorevler2)).thenReturn(gorevler2DTO);

        // When
        Gorevler2DTO result = gorevler2Service.findByGorevAndGorevKodu("Yönetici", 1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(gorevler2DTO);
        verify(dbGorevler2Service).findByGorevAndGorevKodu("Yönetici", 1L);
        verify(gorevler2Mapper).toDto(gorevler2);
    }

    @Test
    void findByGorevAndGorevKodu_shouldThrowException_whenNotExists() {
        // Given
        when(dbGorevler2Service.findByGorevAndGorevKodu("Yönetici", 1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> gorevler2Service.findByGorevAndGorevKodu("Yönetici", 1L));
        verify(dbGorevler2Service).findByGorevAndGorevKodu("Yönetici", 1L);
        verify(gorevler2Mapper, never()).toDto(any());
    }

    @Test
    void findById_shouldReturnGorevler2_whenExists() {
        // Given
        Gorevler2PK id = new Gorevler2PK("Yönetici", 1L);
        when(dbGorevler2Service.findById(id)).thenReturn(Optional.of(gorevler2));
        when(gorevler2Mapper.toDto(gorevler2)).thenReturn(gorevler2DTO);

        // When
        Gorevler2DTO result = gorevler2Service.findById(id);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(gorevler2DTO);
        verify(dbGorevler2Service).findById(id);
        verify(gorevler2Mapper).toDto(gorevler2);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        Gorevler2PK id = new Gorevler2PK("Yönetici", 1L);
        when(dbGorevler2Service.findById(id)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> gorevler2Service.findById(id));
        verify(dbGorevler2Service).findById(id);
        verify(gorevler2Mapper, never()).toDto(any());
    }

    @Test
    void create_shouldCreateGorevler2() {
        // Given
        when(dbGorevler2Service.existsByGorevAndGorevKodu("Yönetici", 1L)).thenReturn(false);
        when(gorevler2Mapper.toEntity(gorevler2DTO)).thenReturn(gorevler2);
        when(gorevler2Mapper.toDto(gorevler2)).thenReturn(gorevler2DTO);

        // When
        Gorevler2DTO result = gorevler2Service.create(gorevler2DTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(gorevler2DTO);
        verify(dbGorevler2Service).existsByGorevAndGorevKodu("Yönetici", 1L);
        verify(gorevler2Mapper).toEntity(gorevler2DTO);
        verify(dbGorevler2Service).save(gorevler2);
        verify(gorevler2Mapper).toDto(gorevler2);
    }

    @Test
    void create_shouldThrowException_whenGorevler2AlreadyExists() {
        // Given
        when(dbGorevler2Service.existsByGorevAndGorevKodu("Yönetici", 1L)).thenReturn(true);

        // When/Then
        assertThrows(ResponseStatusException.class, () -> gorevler2Service.create(gorevler2DTO));
        verify(dbGorevler2Service).existsByGorevAndGorevKodu("Yönetici", 1L);
        verify(gorevler2Mapper, never()).toEntity(any());
        verify(dbGorevler2Service, never()).save(any());
        verify(gorevler2Mapper, never()).toDto(any());
    }

    @Test
    void update_shouldUpdateGorevler2() {
        // Given
        when(dbGorevler2Service.findByGorevAndGorevKodu("Yönetici", 1L)).thenReturn(Optional.of(gorevler2));
        when(gorevler2Mapper.updateEntityFromDto(gorevler2, gorevler2DTO)).thenReturn(gorevler2);
        when(gorevler2Mapper.toDto(gorevler2)).thenReturn(gorevler2DTO);

        // When
        Gorevler2DTO result = gorevler2Service.update("Yönetici", 1L, gorevler2DTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(gorevler2DTO);
        verify(dbGorevler2Service).findByGorevAndGorevKodu("Yönetici", 1L);
        verify(gorevler2Mapper).updateEntityFromDto(gorevler2, gorevler2DTO);
        verify(dbGorevler2Service).update(gorevler2);
        verify(gorevler2Mapper).toDto(gorevler2);
    }

    @Test
    void update_shouldThrowException_whenGorevler2NotExists() {
        // Given
        when(dbGorevler2Service.findByGorevAndGorevKodu("Yönetici", 1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> gorevler2Service.update("Yönetici", 1L, gorevler2DTO));
        verify(dbGorevler2Service).findByGorevAndGorevKodu("Yönetici", 1L);
        verify(gorevler2Mapper, never()).updateEntityFromDto(any(), any());
        verify(dbGorevler2Service, never()).update(any());
        verify(gorevler2Mapper, never()).toDto(any());
    }

    @Test
    void delete_shouldDeleteGorevler2() {
        // Given
        Gorevler2PK id = new Gorevler2PK("Yönetici", 1L);
        when(dbGorevler2Service.findById(id)).thenReturn(Optional.of(gorevler2));

        // When
        gorevler2Service.delete("Yönetici", 1L);

        // Then
        verify(dbGorevler2Service).findById(id);
        verify(dbGorevler2Service).delete(gorevler2);
    }

    @Test
    void delete_shouldThrowException_whenGorevler2NotExists() {
        // Given
        Gorevler2PK id = new Gorevler2PK("Yönetici", 1L);
        when(dbGorevler2Service.findById(id)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> gorevler2Service.delete("Yönetici", 1L));
        verify(dbGorevler2Service).findById(id);
        verify(dbGorevler2Service, never()).delete(any());
    }
}
