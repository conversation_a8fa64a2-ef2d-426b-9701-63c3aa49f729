package iym.makos.config.exhandler;

import iym.common.enums.ResponseCode;
import iym.common.enums.ResultCode;
import iym.common.model.api.FailedApiResponse;
import iym.common.model.api.Response;
import iym.common.util.EnvironmentUtil;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.ConversionFailedException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.util.*;

@ControllerAdvice
@Slf4j
public class CustomRestExceptionHandler extends ResponseEntityExceptionHandler {

    private final EnvironmentUtil environmentUtil;

    public CustomRestExceptionHandler(EnvironmentUtil environmentUtil) {
        this.environmentUtil = environmentUtil;
    }

    /**
     * Create response with environment-appropriate exception details
     */
    private Response<?> createResponse(ResultCode resultCode, String message, Throwable exception) {
        if (environmentUtil.isDevelopmentEnvironment()) {
            // Development: Include exception details for debugging
            return new Response<>(resultCode, message, exception);
        } else {
            // Production: No exception details for security
            return new Response<>(resultCode, message);
        }
    }

    /**
     * Create MahkemeKararResponse for exception handling
     */
    private FailedApiResponse createFailedResponse(ResponseCode responseCode, String message) {
        return FailedApiResponse.builder()
                .errorId(UUID.randomUUID()) // Generate a unique ID for error tracking
                .responseCode(responseCode)
                .responseMessage(message)
                .build();
    }

    /**
     * Get safe error message for production
     */
    private String getSafeErrorMessage(String originalMessage, String fallbackMessage) {
        if (environmentUtil.isDevelopmentEnvironment()) {
            return originalMessage;
        } else {
            // In production, return generic message to avoid exposing internal details
            return fallbackMessage;
        }
    }

    /**
     * Get current authenticated user for logging purposes
     */
    private String getCurrentUser() {
        try {
            return org.springframework.security.core.context.SecurityContextHolder
                    .getContext()
                    .getAuthentication()
                    .getName();
        } catch (Exception e) {
            return "anonymous";
        }
    }

    @ExceptionHandler({ConstraintViolationException.class})
    public ResponseEntity<FailedApiResponse> handleConstraintViolationException(ConstraintViolationException ex, WebRequest request) {

        List<String> errors = new ArrayList<>();
        for (ConstraintViolation<?> violation : ex.getConstraintViolations()) {
            errors.add(violation.getPropertyPath() + ": " + violation.getMessage());
        }

        String path = ((ServletWebRequest) request).getRequest().getRequestURI();
        String errorMessage = String.join(", ", errors);
        String safeMessage = getSafeErrorMessage(errorMessage, "Validation failed");

        FailedApiResponse response = createFailedResponse(ResponseCode.INVALID_REQUEST, safeMessage);

        // Always log full details for debugging
        log.error("ConstraintViolation. uri:{}, errors:{}", path, errors, ex);
        return new ResponseEntity<>(response, response.getResponseCode().toHttpStatus());
    }

    @ExceptionHandler({ResponseStatusException.class})
    public ResponseEntity<FailedApiResponse> handleResponseStatusException(ResponseStatusException rse) {
        String safeMessage = getSafeErrorMessage(rse.getReason(), "Request failed");
        FailedApiResponse response = createFailedResponse(ResponseCode.FAILED, safeMessage);

        // Always log full details for debugging
        log.error("ResponseStatusException: status:{}, reason:{}", rse.getStatusCode(), rse.getReason(), rse);
        return new ResponseEntity<>(response, rse.getStatusCode());
    }

    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String error = ex.getMessage();
        if (ex.getRootCause() != null && (ex.getRootCause() instanceof IllegalArgumentException)) {
            error = ex.getRootCause().getMessage();
        }

        String path = ((ServletWebRequest) request).getRequest().getRequestURI();
        String safeMessage = getSafeErrorMessage(error, "Invalid request format");
        FailedApiResponse response = createFailedResponse(ResponseCode.FAILED, safeMessage);

        // Always log full details for debugging
        log.error("HttpMessageNotReadable. uri:{}, error:{}", path, error, ex);
        return new ResponseEntity<>(response, headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {

        List<String> errors = new ArrayList<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            if (error instanceof FieldError) {
                String fieldName = ((FieldError) error).getField();
                errors.add(fieldName + " " + error.getDefaultMessage());
            } else {
                errors.add(error.getDefaultMessage());
            }
        });

        String errorMessage = String.join(", ", errors);
        String safeMessage = getSafeErrorMessage(errorMessage, "Validation failed");
        FailedApiResponse response = createFailedResponse(ResponseCode.INVALID_REQUEST, safeMessage);

        // Always log full details for debugging
        log.error("MethodArgumentNotValid: uri:{}, errors:{}", ((ServletWebRequest) request).getRequest().getRequestURI(), errors, ex);
        return new ResponseEntity<>(response, status);
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(MissingServletRequestParameterException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        String error = ex.getParameterName() + " parameter is missing";
        String safeMessage = getSafeErrorMessage(error, "Required parameter is missing");

        FailedApiResponse response = createFailedResponse(ResponseCode.FAILED, safeMessage);

        // Always log full details for debugging
        log.error("MissingServletRequestParameter. uri:{}, parameter:{}", ((ServletWebRequest) request).getRequest().getRequestURI(), ex.getParameterName(), ex);
        return new ResponseEntity<>(response, headers, status);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<FailedApiResponse> handleIllegalArgumentException(IllegalArgumentException ex) {
        String error = ex.getMessage();
        String safeMessage = getSafeErrorMessage(error, "Invalid argument provided");

        FailedApiResponse response = createFailedResponse(ResponseCode.INVALID_REQUEST, safeMessage);

        // Always log full details for debugging
        log.error("IllegalArgumentException. error:{}", error, ex);
        return new ResponseEntity<>(response, response.getResponseCode().toHttpStatus());
    }

    @ExceptionHandler(ConversionFailedException.class)
    public ResponseEntity<FailedApiResponse> handleConversionFailed(RuntimeException e) {
        String safeMessage = getSafeErrorMessage(e.getMessage(), "Data conversion failed");
        FailedApiResponse response = createFailedResponse(ResponseCode.INVALID_REQUEST, safeMessage);

        // Always log full details for debugging
        log.error("ConversionFailed: {}", e.getMessage(), e);
        return new ResponseEntity<>(response, response.getResponseCode().toHttpStatus());
    }

    @ExceptionHandler(AccessDeniedException.class)
    public ResponseEntity<FailedApiResponse> handleAccessDeniedException(AccessDeniedException ex, WebRequest request) {
        String path = ((ServletWebRequest) request).getRequest().getRequestURI();
        String message = "Access Denied: You do not have the required role to access this resource";

        // Access denied messages are safe to show in both environments
        FailedApiResponse response = createFailedResponse(ResponseCode.INVALID_REQUEST, message);

        // Always log full details for debugging
        log.error("Access Denied. uri:{}, user:{}, error:{}", path, getCurrentUser(), ex.getMessage(), ex);
        return new ResponseEntity<>(response, HttpStatus.FORBIDDEN);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<FailedApiResponse> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex, WebRequest request) {
        String error = ex.getName() + " should be of type " + Objects.requireNonNull(ex.getRequiredType()).getName();
        String safeMessage = getSafeErrorMessage(error, "Invalid parameter type");

        FailedApiResponse response = createFailedResponse(ResponseCode.INVALID_REQUEST, safeMessage);

        // Always log full details for debugging
        log.error("MethodArgumentTypeMismatch. uri:{}, parameter:{}, expectedType:{}, actualValue:{}",
                ((ServletWebRequest) request).getRequest().getRequestURI(),
                ex.getName(),
                ex.getRequiredType().getName(),
                ex.getValue(), ex);
        return new ResponseEntity<>(response, response.getResponseCode().toHttpStatus());
    }

    @ExceptionHandler({Exception.class})
    public ResponseEntity<FailedApiResponse> handleAll(Exception e) {
        // For generic exceptions, never expose internal details in production
        String safeMessage = getSafeErrorMessage(e.getMessage(), "Internal Server Error");
        FailedApiResponse response = createFailedResponse(ResponseCode.FAILED, safeMessage);

        // Always log full details for debugging
        log.error("Unhandled Exception: {}", e.getMessage(), e);
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private static Map<String, List<String>> getErrorsMap(List<String> errors) {
        Map<String, List<String>> errorResponse = new HashMap<>();
        errorResponse.put("errors", errors);
        return errorResponse;
    }

}
