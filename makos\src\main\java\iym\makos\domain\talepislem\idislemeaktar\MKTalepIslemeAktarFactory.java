package iym.makos.domain.talepislem.idislemeaktar;


import iym.common.enums.KararTuru;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MKTalepIslemeAktarFactory {

    private final Map<KararTuru, IDMahkemeKararTalepIslemeAktarici> isleyiciListByKararTuru = new HashMap<>();

    @Autowired
    public MKTalepIslemeAktarFactory(List<IDMahkemeKararTalepIslemeAktarici> talepIsleyiciler) {
        for (IDMahkemeKararTalepIslemeAktarici validator : talepIsleyiciler) {
            isleyiciListByKararTuru.put(validator.getRelatedKararTuru(), validator);
        }
    }

    public IDMahkemeKararTalepIslemeAktarici getKararTalepIsleyici(KararTuru kararTuru) {
        return isleyiciListByKararTuru.get(kararTuru);
    }

}