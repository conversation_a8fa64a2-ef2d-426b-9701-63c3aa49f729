package iym.makos.controller;

import iym.common.enums.MakosUserAuditType;
import iym.common.enums.ResponseCode;
import iym.common.model.entity.makos.MakosUserAuditLog;
import iym.common.service.db.DbMakosUserAuditLogService;
import iym.common.util.ExceptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;



import iym.makos.model.dto.audit.GetMakosUserAuditLogListResponse;
import iym.makos.model.dto.audit.GetMakosUserAuditLogResponse;
import iym.makos.model.dto.audit.GetMakosUserAuditLogsByUsernameResponse;
import iym.makos.model.dto.audit.GetMakosUserAuditLogsByTypeResponse;

/**
 * Audit Controller for MAKOS Module
 * Provides endpoints for viewing audit logs
 */
@RestController
@RequestMapping("/audit")
@Slf4j
@Validated
public class AuditController {

    @Autowired
    private DbMakosUserAuditLogService makosUserAuditLogService;

    /**
     * Get paginated list of MAKOS user audit logs
     * Only accessible by ADMIN users
     */
    @GetMapping("/getMakosUserAuditLogList")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<GetMakosUserAuditLogListResponse> getMakosUserAuditLogList(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "200") Integer count,
            @RequestParam(defaultValue = "requestTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection) {
        
        Sort.Direction direction = sortDirection.equalsIgnoreCase("desc") ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        Sort sort = Sort.by(direction, sortBy);
        Pageable pageable = PageRequest.of(page, count, sort);
        
        Page<MakosUserAuditLog> auditLogs = makosUserAuditLogService.findAll(pageable);
        log.info("Retrieved {} MAKOS user audit logs for page {}", auditLogs.getContent().size(), page);
        
        return ResponseEntity.ok(GetMakosUserAuditLogListResponse.builder()
                .response(iym.common.model.api.ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("Audit logs retrieved successfully")
                        .build())
                .auditLogs(auditLogs)
                .build());
    }

    /**
     * Get specific MAKOS user audit log by ID
     * Only accessible by ADMIN users
     */
    @GetMapping("/getMakosUserAuditLog/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<GetMakosUserAuditLogResponse> getMakosUserAuditLog(@PathVariable Long id) {
        MakosUserAuditLog auditLog = makosUserAuditLogService.findById(id)
                .orElseThrow(() -> ExceptionUtils.RECORD_NOT_FOUND);
        
        log.info("Retrieved MAKOS user audit log with ID: {}", id);
        return ResponseEntity.ok(GetMakosUserAuditLogResponse.builder()
                .response(iym.common.model.api.ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("Audit log retrieved successfully")
                        .build())
                .auditLog(auditLog)
                .build());
    }

    /**
     * Get MAKOS user audit logs by username
     * Only accessible by ADMIN users
     */
    @GetMapping("/getMakosUserAuditLogsByUsername")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<GetMakosUserAuditLogsByUsernameResponse> getMakosUserAuditLogsByUsername(
            @RequestParam String username,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "200") Integer count,
            @RequestParam(defaultValue = "requestTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection) {
        
        Sort.Direction direction = sortDirection.equalsIgnoreCase("desc") ? 
            Sort.Direction.DESC : Sort.Direction.ASC;
        Sort sort = Sort.by(direction, sortBy);
        Pageable pageable = PageRequest.of(page, count, sort);
        
        // Create specification for filtering by username
        org.springframework.data.jpa.domain.Specification<MakosUserAuditLog> spec = 
            (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("username"), username);
        
        Page<MakosUserAuditLog> auditLogs = makosUserAuditLogService.findAll(spec, pageable);
        log.info("Retrieved {} MAKOS user audit logs for username: {} (page {}, total: {})", 
                auditLogs.getContent().size(), username, page, auditLogs.getTotalElements());
        
        return ResponseEntity.ok(GetMakosUserAuditLogsByUsernameResponse.builder()
                .response(iym.common.model.api.ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("Audit logs retrieved successfully")
                        .build())
                .auditLogs(auditLogs)
                .build());
    }

    /**
     * Get MAKOS user audit logs by audit type
     * Only accessible by ADMIN users
     */
    @GetMapping("/getMakosUserAuditLogsByType")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<GetMakosUserAuditLogsByTypeResponse> getMakosUserAuditLogsByType(
            @RequestParam String auditType,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "200") Integer count,
            @RequestParam(defaultValue = "requestTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection) {
        
        try {
            MakosUserAuditType userAuditType = MakosUserAuditType.valueOf(auditType);
            
            Sort.Direction direction = sortDirection.equalsIgnoreCase("desc") ? 
                Sort.Direction.DESC : Sort.Direction.ASC;
            Sort sort = Sort.by(direction, sortBy);
            Pageable pageable = PageRequest.of(page, count, sort);
            
            // Create specification for filtering by audit type
            org.springframework.data.jpa.domain.Specification<MakosUserAuditLog> spec = 
                (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("userAuditType"), userAuditType);
            
            Page<MakosUserAuditLog> auditLogs = makosUserAuditLogService.findAll(spec, pageable);
            log.info("Retrieved {} MAKOS user audit logs for audit type: {} (page {}, total: {})", 
                    auditLogs.getContent().size(), auditType, page, auditLogs.getTotalElements());
            
            return ResponseEntity.ok(GetMakosUserAuditLogsByTypeResponse.builder()
                    .response(iym.common.model.api.ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Audit logs retrieved successfully")
                            .build())
                    .auditLogs(auditLogs)
                    .build());
        } catch (IllegalArgumentException e) {
            log.error("Invalid audit type: {}", auditType);
            throw ExceptionUtils.newBadRequest("Invalid audit type: " + auditType);
        }
    }
}
