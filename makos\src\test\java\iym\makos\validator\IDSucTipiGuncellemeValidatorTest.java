package iym.makos.validator;

import iym.common.enums.GuncellemeTip;
import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.mk.MahkemeKararSucTipleri;
import iym.common.model.entity.iym.SucTipi;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbSucTipiService;
import iym.common.service.db.mk.DbMahkemeKararSucTipleriService;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.SucTipiGuncellemeDetay;
import iym.makos.model.api.SucTipiGuncellemeKararDetay;
import iym.makos.model.dto.mahkemekarar.id.IDSucTipiGuncellemeRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Optional;

import static iym.makos.domain.testdata.TestDataBuilder.createValidIDSucTipiGuncellemeRequest;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDSucTipiGuncellemeValidator.
 *
 * Tests validation logic for ID suç tipi güncelleme (crime type update) requests.
 * Uses LENIENT mock settings for flexibility.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("IDSucTipiGuncellemeValidator Unit Tests")
class IDSucTipiGuncellemeValidatorTest extends BaseDomainUnitTest {

    @Mock
    private DbMahkemeKararService mockDbMahkemeKararService;

    @Mock
    private DbSucTipiService mockDbSucTipiService;

    @Mock
    private DbMahkemeKararSucTipleriService mockDbMahkemeKararSucTipleriService;

    @Mock
    private MahkemeKararRequestCommonValidator mockCommonValidator;

    @InjectMocks
    private IDSucTipiGuncellemeValidator validator;

    private IDSucTipiGuncellemeRequest validRequest;

    @BeforeEach
    void setUp() {
        validRequest = createValidIDSucTipiGuncellemeRequest();
        setupValidMocks();
    }

    private void setupValidMocks() {
        // Mock common validator to return valid result
        when(mockCommonValidator.validate(any(IDSucTipiGuncellemeRequest.class)))
                .thenReturn(new ValidationResult(true));

        // Mock mahkeme karar service to return valid mahkeme karar
        MahkemeKarar mockMahkemeKarar = new MahkemeKarar();
        mockMahkemeKarar.setId(1L);
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKarar));

        // Mock suc tipi service to return valid suc tipi
        SucTipi mockSucTipi = new SucTipi();
        mockSucTipi.setSucTipiKodu("TEST_SUC_TIPI");
        when(mockDbSucTipiService.findBySucTipiKodu(anyString()))
                .thenReturn(Optional.of(mockSucTipi));

        // Set the common validator mock
        validator.setMahkemeKararRequestCommonValidator(mockCommonValidator);
    }

    @Test
    @DisplayName("Should pass validation when request is valid")
    void shouldPassValidation_whenRequestIsValid() {
        // Given
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationSuccess(result);
    }

    @Test
    @DisplayName("Should fail validation when mahkeme karar not found")
    void shouldFailValidation_whenMahkemeKararNotFound() {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.empty());

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
                reason.contains("Mahkeme Karar Bulunamadı"));
    }

    @Test
    @DisplayName("Should fail validation when trying to remove non-existing suc tipi")
    void shouldFailValidation_whenTryingToRemoveNonExistingSucTipi() {
        // Given
        when(mockDbMahkemeKararSucTipleriService.findByMahkemeKararIdAndSucTipKodu(anyLong(), anyString()))
                .thenReturn(Optional.empty());

        // Create request with CIKAR operation
        SucTipiGuncellemeDetay sucTipiDetay = SucTipiGuncellemeDetay.builder()
                .sucTipiKodu("NON_EXISTING_SUC_TIPI")
                .guncellemeTip(GuncellemeTip.CIKAR)
                .build();

        SucTipiGuncellemeKararDetay kararDetay = SucTipiGuncellemeKararDetay.builder()
                .mahkemeKararDetay(MahkemeKararDetay.builder()
                        .mahkemeIlIlceKodu("34001")
                        .mahkemeKodu("34001")
                        .mahkemeKararNo("2024/001")
                        .sorusturmaNo("2024/001")
                        .build())
                .sucTipiGuncellemeDetayListesi(Arrays.asList(sucTipiDetay))
                .build();

        validRequest.setSucTipiGuncellemeKararDetayListesi(Arrays.asList(kararDetay));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
                reason.contains("suç tipi bulunamadı"));
    }

    @Test
    @DisplayName("Should fail validation when trying to add non-existing suc tipi")
    void shouldFailValidation_whenTryingToAddNonExistingSucTipi() {
        // Given
        when(mockDbSucTipiService.findBySucTipiKodu(anyString()))
                .thenReturn(Optional.empty());

        // Create request with EKLE operation
        SucTipiGuncellemeDetay sucTipiDetay = SucTipiGuncellemeDetay.builder()
                .sucTipiKodu("NON_EXISTING_SUC_TIPI")
                .guncellemeTip(GuncellemeTip.EKLE)
                .build();

        SucTipiGuncellemeKararDetay kararDetay = SucTipiGuncellemeKararDetay.builder()
                .mahkemeKararDetay(MahkemeKararDetay.builder()
                        .mahkemeIlIlceKodu("34001")
                        .mahkemeKodu("34001")
                        .mahkemeKararNo("2024/001")
                        .sorusturmaNo("2024/001")
                        .build())
                .sucTipiGuncellemeDetayListesi(Arrays.asList(sucTipiDetay))
                .build();

        validRequest.setSucTipiGuncellemeKararDetayListesi(Arrays.asList(kararDetay));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
                reason.contains("koduna sahip şuç tipi bulunamadı"));
    }

    @Test
    @DisplayName("Should pass validation when adding existing suc tipi")
    void shouldPassValidation_whenAddingExistingSucTipi() {
        // Given
        // Mock existing suc tipi for EKLE operation
        SucTipi mockSucTipi = new SucTipi();
        mockSucTipi.setSucTipiKodu("EXISTING_SUC_TIPI");
        when(mockDbSucTipiService.findBySucTipiKodu("EXISTING_SUC_TIPI"))
                .thenReturn(Optional.of(mockSucTipi));

        // Create request with EKLE operation
        SucTipiGuncellemeDetay sucTipiDetay = SucTipiGuncellemeDetay.builder()
                .sucTipiKodu("EXISTING_SUC_TIPI")
                .guncellemeTip(GuncellemeTip.EKLE)
                .build();

        SucTipiGuncellemeKararDetay kararDetay = SucTipiGuncellemeKararDetay.builder()
                .mahkemeKararDetay(MahkemeKararDetay.builder()
                        .mahkemeIlIlceKodu("34001")
                        .mahkemeKodu("34001")
                        .mahkemeKararNo("2024/001")
                        .sorusturmaNo("2024/001")
                        .build())
                .sucTipiGuncellemeDetayListesi(Arrays.asList(sucTipiDetay))
                .build();

        validRequest.setSucTipiGuncellemeKararDetayListesi(Arrays.asList(kararDetay));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationSuccess(result);
    }

    @Test
    @DisplayName("Should pass validation when removing existing suc tipi")
    void shouldPassValidation_whenRemovingExistingSucTipi() {
        // Given
        // Mock existing mahkeme karar suc for CIKAR operation
        MahkemeKararSucTipleri mockMahkemeKararSuc = new MahkemeKararSucTipleri();
        when(mockDbMahkemeKararSucTipleriService.findByMahkemeKararIdAndSucTipKodu(anyLong(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKararSuc));

        // Create request with CIKAR operation
        SucTipiGuncellemeDetay sucTipiDetay = SucTipiGuncellemeDetay.builder()
                .sucTipiKodu("EXISTING_SUC_TIPI")
                .guncellemeTip(GuncellemeTip.CIKAR)
                .build();

        SucTipiGuncellemeKararDetay kararDetay = SucTipiGuncellemeKararDetay.builder()
                .mahkemeKararDetay(MahkemeKararDetay.builder()
                        .mahkemeIlIlceKodu("34001")
                        .mahkemeKodu("34001")
                        .mahkemeKararNo("2024/001")
                        .sorusturmaNo("2024/001")
                        .build())
                .sucTipiGuncellemeDetayListesi(Arrays.asList(sucTipiDetay))
                .build();

        validRequest.setSucTipiGuncellemeKararDetayListesi(Arrays.asList(kararDetay));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationSuccess(result);
    }

    @Test
    @DisplayName("Should handle validation exception gracefully")
    void shouldHandleValidationException_gracefully() {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should return correct karar turu")
    void shouldReturnCorrectKararTuru() {
        // When
        KararTuru result = validator.getRelatedKararTuru();

        // Then
        assertEquals(KararTuru.ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME, result);
    }

    @Test
    @DisplayName("Should return early when request.isValid() fails")
    void shouldReturnEarly_whenRequestIsValidFails() {
        // Given - Mock common validator to return invalid result
        when(mockCommonValidator.validate(any(IDSucTipiGuncellemeRequest.class)))
                .thenReturn(new ValidationResult("Request validation failed"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Request validation failed");
    }

    @Test
    @DisplayName("Should return early when common validation fails")
    void shouldReturnEarly_whenCommonValidationFails() {
        // Given - Create request with wrong karar turu to make isValid() fail
        validRequest.setKararTuru(KararTuru.GENEL_EVRAK);

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        // Should return early without calling database services
    }
}
