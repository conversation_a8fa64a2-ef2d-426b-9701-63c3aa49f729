package iym.makos.mapper;

import iym.common.model.entity.iym.MahkemeKararTipleri;
import iym.common.model.entity.iym.SucTipi;
import iym.makos.model.dto.db.MahkemeKararTipiDTO;
import iym.makos.model.dto.db.SucTipiDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for MahkemeBilgiMapper entity and DTO
 */
@Component
public class MahkemeKararTipiMapper {


    public MahkemeKararTipiDTO toDto(MahkemeKararTipleri entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeKararTipiDTO.builder()
                .kararKodu(entity.getKararKodu())
                .kararTuru(entity.getKararTuru())
                .sonlandirma(entity.getSonlandirma())
                .kararTipi(entity.getKararTipi())
                .build();
    }


    public MahkemeKararTipleri toEntity(MahkemeKararTipiDTO dto) {
        if (dto == null) {
            return null;
        }

        return MahkemeKararTipleri.builder()
                .kararKodu(dto.getKararKodu())
                .kararTuru(dto.getKararTuru())
                .sonlandirma(dto.getSonlandirma())
                .kararTipi(dto.getKararTipi())
                .build();
    }

    public List<MahkemeKararTipiDTO> toDtoList(List<MahkemeKararTipleri> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

}
