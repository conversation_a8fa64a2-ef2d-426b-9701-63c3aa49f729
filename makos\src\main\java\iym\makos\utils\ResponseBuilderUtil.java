package iym.makos.utils;

import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.mahkemekarar.MahkemeKararResponse;

import java.lang.reflect.Method;
import java.util.function.Supplier;

public class ResponseBuilderUtil {


    private ResponseBuilderUtil() {}

    public static <T extends MahkemeKararResponse> T buildResponse(Supplier<T> responseSupplier,
                                                           MakosResponseCode responseCode,
                                                           String responseMessage,
                                                           Object data) {
        T responseObject = responseSupplier.get();

        responseObject.setResponse(MakosApiResponse.builder()
                .responseCode(responseCode)
                .responseMessage(responseMessage)
                .build());

        if (data != null) {
            injectData(responseObject, data);
        }

        return responseObject;
    }

    public static <T extends MahkemeKararResponse> T buildSuccessResponse(Supplier<T> responseSupplier, Object data) {
        return buildResponse(responseSupplier, MakosResponseCode.SUCCESS, "Success", data);
    }

    public static <T extends MahkemeKararResponse> T buildErrorResponse(Supplier<T> responseSupplier, String errorMessage) {
        return buildResponse(responseSupplier, MakosResponseCode.FAILED,
                errorMessage != null ? errorMessage : "Internal Error", null);
    }

    private static void injectData(Object target, Object data) {
        try {
            Method setDataMethod = target.getClass().getMethod("setData", data.getClass());
            setDataMethod.invoke(target, data);
        } catch (NoSuchMethodException e) {
            // Data alanı opsiyoneldir, yoksa geç.
        } catch (Exception e) {
            throw new RuntimeException("Data injection failed", e);
        }
    }

}
