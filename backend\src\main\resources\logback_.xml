<?xml version="1.0" encoding="UTF-8"?>
<configuration  scan="true" scanPeriod="60 seconds">

    <appender name="consoleAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{dd.MM.yyyy HH:mm:ss.SSS} %-5level %msg [%logger{0}:%L] [%thread]%n</pattern>
        </encoder>
    </appender>

    <appender name="dailyRollingFileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/iym-backend.log</file>

        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{dd.MM.YYYY HH:mm:ss.SSS} %-5level %logger{36}:%L %msg [%thread]%n</Pattern>
        </encoder>

        <!-- daily rollover-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>./logs/iym-backend.log.%d{yyyyMMdd}.txt</FileNamePattern>
            <!-- keep 30 days' worth of history -->
            <!--<maxHistory>30</maxHistory>-->
        </rollingPolicy>
    </appender>

    <logger name="org.springframework" level="TRACE" additivity="false"/>
    <logger name="org.hibernate" level="INFO" additivity="false"/>

    <root level="DEBUG">
        <appender-ref ref="consoleAppender" />
    </root>

</configuration>
