# Development Environment Configuration for Backend
# Database Configuration for Development

# PostgreSQL Database configuration
spring.datasource.url=****************************************
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.username=demo_user
spring.datasource.password=demo_password

# JPA configuration for development
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.hbm2ddl.auto=none
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Flyway configuration for development
spring.flyway.enabled=false
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.validate-on-migrate=true
spring.flyway.clean-disabled=true
spring.flyway.default-schema=public

# Connection pool configuration for development
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5

# Development specific logging
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
logging.level.iym=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR

# Swagger configuration for development
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true

# Application specific properties for development
app.seed-data=true

# JWT Configuration for development
app.jwtSecret=dev-secret-key-asdRET567-asdHd-52sdauer-dfgZSDfas5sCd34df-123dFTH56HG-asd45FgbsdDd334-aasd456fdvb
# 24 hours for development
app.jwtExpirationInSec=86400

# CORS Configuration for development (allow all local origins)
cors.allowed.origins=http://localhost:3000,http://localhost:4200,http://127.0.0.1:3000,http://127.0.0.1:4200

makos.api.base-url=${MAKOS_API_BASE_URL:http://localhost:5000/makosapi}
makos.api.username=${MAKOS_API_USERNAME:makos_admin}
makos.api.password=${MAKOS_API_PASSWORD:123456}
makos.api.connect-timeout=${MAKOS_API_CONNECT_TIMEOUT:5000}
makos.api.read-timeout=${MAKOS_API_READ_TIMEOUT:30000}
