package iym.makos.mapper;

import iym.common.model.entity.iym.talep.MahkemeHedeflerAidiyatTalep;
import org.springframework.stereotype.Component;
import iym.makos.model.dto.db.MahkemeHedeflerAidiyatTalepDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for MahkemeHedeflerAidiyatTalep entity and DTO
 */
@Component
public class MahkemeHedeflerAidiyatTalepMapper {

    /**
     * Convert entity to DTO
     * @param entity MahkemeHedeflerAidiyatTalep entity
     * @return MahkemeHedeflerAidiyatTalepDTO
     */
    public MahkemeHedeflerAidiyatTalepDTO toDto(MahkemeHedeflerAidiyatTalep entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeHedeflerAidiyatTalepDTO.builder()
                .id(entity.getId())
                .hedefId(entity.getHedefId())
                .aidiyatKod(entity.getAidiyatKod())
                .tarih(entity.getTarih())
                .mahkemeKararId(entity.getMahkemeKararId())
                .durumu(entity.getDurumu())
                .kullaniciId(entity.getKullaniciId())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto MahkemeHedeflerAidiyatTalepDTO
     * @return MahkemeHedeflerAidiyatTalep entity
     */
    public MahkemeHedeflerAidiyatTalep toEntity(MahkemeHedeflerAidiyatTalepDTO dto) {
        if (dto == null) {
            return null;
        }

        return MahkemeHedeflerAidiyatTalep.builder()
                .id(dto.getId())
                .hedefId(dto.getHedefId())
                .aidiyatKod(dto.getAidiyatKod())
                .tarih(dto.getTarih())
                .mahkemeKararId(dto.getMahkemeKararId())
                .durumu(dto.getDurumu())
                .kullaniciId(dto.getKullaniciId())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity MahkemeHedeflerAidiyatTalep entity to update
     * @param dto MahkemeHedeflerAidiyatTalepDTO with new values
     * @return Updated MahkemeHedeflerAidiyatTalep entity
     */
    public MahkemeHedeflerAidiyatTalep updateEntityFromDto(MahkemeHedeflerAidiyatTalep entity, MahkemeHedeflerAidiyatTalepDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setHedefId(dto.getHedefId());
        entity.setAidiyatKod(dto.getAidiyatKod());
        entity.setTarih(dto.getTarih());
        entity.setMahkemeKararId(dto.getMahkemeKararId());
        entity.setDurumu(dto.getDurumu());
        entity.setKullaniciId(dto.getKullaniciId());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of MahkemeHedeflerAidiyatTalep entities
     * @return List of MahkemeHedeflerAidiyatTalepDTO
     */
    public List<MahkemeHedeflerAidiyatTalepDTO> toDtoList(List<MahkemeHedeflerAidiyatTalep> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of MahkemeHedeflerAidiyatTalepDTO
     * @return List of MahkemeHedeflerAidiyatTalep entities
     */
    public List<MahkemeHedeflerAidiyatTalep> toEntityList(List<MahkemeHedeflerAidiyatTalepDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
