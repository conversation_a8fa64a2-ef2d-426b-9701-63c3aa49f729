package iym.makos.domain.mahkemekarar.dbhandler;

import iym.common.enums.GuncellemeTip;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.mk.MahkemeKararSucTipleri;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeSucTipiDetayTalep;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.mk.MahkemeKararAidiyatRepo;
import iym.db.jpa.dao.mk.MahkemeKararRepo;
import iym.db.jpa.dao.mk.MahkemeKararSucTipleriRepo;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.HedeflerAidiyatTalepRepo;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.MahkemeSucTipiDetayTalepRepo;
import iym.makos.model.dto.mahkemekarar.id.IDSucTipiGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class IDSucTipiGuncellemeDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDSucTipiGuncellemeRequest> {

    private MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararSucTipleriRepo mahkemeKararSucTipleriRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;
    private MahkemeSucTipiDetayTalepRepo mahkemeSucTipiDetayTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private MahkemeKararAidiyatRepo mahkemeKararAidiyatRepo;

    @Autowired
    public IDSucTipiGuncellemeDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararSucTipleriRepo mahkemeKararSucTipleriRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , MahkemeSucTipiDetayTalepRepo mahkemeSucTipiDetayTalepRepo
            , KararRequestMapper kararRequestMapper
            , MahkemeKararAidiyatRepo mahkemeKararAidiyatRepo) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararSucTipleriRepo = mahkemeKararSucTipleriRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
        this.mahkemeSucTipiDetayTalepRepo = mahkemeSucTipiDetayTalepRepo;
        this.mahkemeKararAidiyatRepo = mahkemeKararAidiyatRepo;
    }

    @Override
    @Transactional
    public Long kaydet(IDSucTipiGuncellemeRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }
            List<SucTipiGuncellemeKararDetay> guncellemeListesi = request.getSucTipiGuncellemeKararDetayListesi();

            for (SucTipiGuncellemeKararDetay guncellemeBilgisi : guncellemeListesi) {
                //Güncellemeye konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = guncellemeBilgisi.getMahkemeKararDetay();
                Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu(), ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    throw new MakosResponseException(errorStr);
                }
                MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


                DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(detayMahkemeKararTalep);

                for (SucTipiGuncellemeDetay sucTipiGuncellemeDetay : CommonUtils.safeList( guncellemeBilgisi.getSucTipiGuncellemeDetayListesi())) {

                    GuncellemeTip guncellemeTip = sucTipiGuncellemeDetay.getGuncellemeTip();
                    String sucTipiKodu = sucTipiGuncellemeDetay.getSucTipiKodu();

                    Optional<MahkemeKararSucTipleri> mahkemeKararSucOpt = mahkemeKararSucTipleriRepo.findByMahkemeKararIdAndSucTipKodu(iliskiliMahkemeKarar.getId(), sucTipiKodu);
                    if (guncellemeTip == GuncellemeTip.EKLE && !mahkemeKararSucOpt.isEmpty() ) {
                        throw new Exception( String.format( MakosResponseErrorCodes.MK_SUCTIPI_ZATENVAR, iliskiliMahkemeKarar.getId(), sucTipiKodu));
                    } else if (guncellemeTip == GuncellemeTip.CIKAR && mahkemeKararSucOpt.isEmpty()) {
                        throw new Exception( String.format( MakosResponseErrorCodes.MK_SUCTIPI_BULUNAMADI, iliskiliMahkemeKarar.getId(), sucTipiKodu));
                    }

                    MahkemeSucTipiDetayTalep mahkemeSucTipiDetayTalep = new MahkemeSucTipiDetayTalep();
                    mahkemeSucTipiDetayTalep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());
                    mahkemeSucTipiDetayTalep.setMahkemeKararTalepId(mahkemeKararTalepId);
                    mahkemeSucTipiDetayTalep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
                    mahkemeSucTipiDetayTalep.setTarih(kayitTarihi);
                    if (guncellemeTip == GuncellemeTip.EKLE) {
                        mahkemeSucTipiDetayTalep.setMahkemeSucTipiKoduEkle(sucTipiKodu);
                    } else {
                        mahkemeSucTipiDetayTalep.setMahkemeSucTipiKoduCikar(sucTipiKodu);
                    }
                    MahkemeSucTipiDetayTalep savedMahkemeAidiyatDetayTalep = mahkemeSucTipiDetayTalepRepo.save(mahkemeSucTipiDetayTalep);
                }
            }


            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("IDAidiyatBilgisiGuncelleme handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }

}

