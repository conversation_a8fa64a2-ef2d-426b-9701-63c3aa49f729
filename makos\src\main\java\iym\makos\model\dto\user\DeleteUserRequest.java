package iym.makos.model.dto.user;

import iym.common.validation.ValidationResult;
import iym.makos.model.MakosRequest;
import iym.makos.validator.custom.MakosRequestValid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;



/**
 * Delete user request DTO for MAKOS user management
 */
@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
@MakosRequestValid
@Slf4j
public class DeleteUserRequest implements MakosRequest {

    @NotNull
    private Long userId;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if DeleteUserRequest is valid");
        
        ValidationResult validationResult = new ValidationResult(true);
        
        try {
            if (userId == null) {
                validationResult.addFailedReason("User ID cannot be null");
            }
        } catch (Exception e) {
            log.error("Validation failed", e);
            validationResult.addFailedReason("Validation error: " + e.getMessage());
        }
        
        return validationResult;
    }
} 