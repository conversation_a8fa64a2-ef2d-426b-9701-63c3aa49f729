-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MA<PERSON>_KARAR_TIPLERI if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON><PERSON>_<PERSON>ARAR_TIPLERI_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAH_KARAR_TIPLERI_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAH_KARAR_TIPLERI table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAH_KARAR_TIPLERI';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAH_KARAR_TIPLERI (
      ID NUMBER NOT NULL,
      <PERSON><PERSON><PERSON>_KODU NUMBER NOT NULL,
      KARAR_TIPI VARCHAR2(100) NOT NULL,
      K<PERSON><PERSON>_TURU VARCHAR2(10) NOT NULL,
      SONLANDIRMAMI NUMBER(1,0) DEFAULT 0,
      CONSTRAINT MAH_KARAR_TIP_PRM PRIMARY KEY(KARAR_KODU) ENABLE
    )';

  END IF;
END;
/


-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.MAH_KARAR_TIPLERI;
    IF row_count = 0 THEN

          INSERT INTO iym.MAH_KARAR_TIPLERI (ID, KARAR_KODU, KARAR_TIPI, KARAR_TURU, SONLANDIRMAMI)
          VALUES (iym.MAH_KARAR_TIPLERI_SEQ.NEXTVAL, '100', 'ÖNLEYİCİ-HAKİM KARARI', 'ONLEYICI', 0);

          INSERT INTO iym.MAH_KARAR_TIPLERI (ID, KARAR_KODU, KARAR_TIPI, KARAR_TURU, SONLANDIRMAMI)
          VALUES (iym.MAH_KARAR_TIPLERI_SEQ.NEXTVAL, '300', 'ADLİ-HAKİM KARARI', 'ADLI', 0);

    END IF;
END;
/


COMMIT;
