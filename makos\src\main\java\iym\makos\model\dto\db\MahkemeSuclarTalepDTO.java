package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for MahkemeSuclarTalep entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Mahkeme Suçlar Talep bilgilerini içerir")
public class MahkemeSuclarTalepDTO {

    @Schema(description = "Mahkeme suçlar talep ID")
    private Long id;

    @Schema(description = "İlişkili mahkeme karar talep ID")
    @NotNull(message = "Mahkeme karar ID boş olamaz")
    private Long mahkemeKararTalepId;

    @Schema(description = "Mahkeme suç tip kodu", example = "01")
    @NotNull(message = "Mahkeme suç tip kodu boş olamaz")
    @Size(max = 10, message = "Mahkeme suç tip kodu 10 karakterden fazla olamaz")
    private String sucTipKodu;

    @Schema(description = "Durum", example = "AKTIF")
    @Size(max = 20, message = "Durum 20 karakterden fazla olamaz")
    private String durumu;
}
