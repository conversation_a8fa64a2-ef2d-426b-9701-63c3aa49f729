package iym.makos.model.dto.auth;

import iym.common.enums.IletisimTespitiKararTuru;
import iym.common.enums.KullaniciKurum;
import iym.common.enums.MakosUserRoleType;
import iym.common.validation.ValidationResult;
import iym.makos.model.MakosRequest;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class RegisterRequest implements MakosRequest {

    @NotBlank(message = "Username is required")
    private String userName;

    @NotBlank(message = "Password is required")
    private String password;

    @NotNull(message = "Role is required")
    private MakosUserRoleType role;

    @NotNull(message = "Kurum is required")
    private KullaniciKurum kurum;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if RegisterRequest is valid");

        ValidationResult validationResult = new ValidationResult(true);

        try {
            if (userName == null || userName.trim().isEmpty()) {
                validationResult.addFailedReason("Username cannot be null or empty");
            }
            if (userName.length() < 4 || userName.length() > 100) {
                validationResult.addFailedReason("Username cannot be less then 4 chars and greater then 100 chars");
            }
            if (password == null || password.trim().isEmpty()) {
                validationResult.addFailedReason("Password cannot be null or empty");
            }
            if (role == null) {
                validationResult.addFailedReason("Role cannot be null");
            }
            if (kurum == null) {
                validationResult.addFailedReason("Kurum cannot be null");
            }
        } catch (Exception e) {
            log.error("Validation failed", e);
            validationResult.addFailedReason("Validation error: " + e.getMessage());
        }

        return validationResult;
    }
} 