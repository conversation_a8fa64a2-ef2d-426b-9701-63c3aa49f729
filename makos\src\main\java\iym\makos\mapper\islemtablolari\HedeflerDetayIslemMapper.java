package iym.makos.mapper.islemtablolari;

import iym.common.model.entity.iym.mk.DetayMahkemeKararIslem;
import iym.common.model.entity.iym.mk.HedeflerDetayIslem;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Mapper for MahkemeKararTalep entity and DTO
 */
@Component
public class HedeflerDetayIslemMapper {

    public HedeflerDetayIslem fromDetayMahkemeKararTalep(HedeflerDetayTalep entity){
        if (entity == null) {
            return null;
        }

        return HedeflerDetayIslem.builder()
                .canakNo(entity.getCanakNo())
                .hedefAdi(entity.getHedefAdi())
                .hedefSoyadi(entity.getHedefSoyadi())
                .tcKimlikNo(entity.getTcKimlikNo())
                .kayit<PERSON><PERSON><PERSON>(new Date())
                .updateColumnNames(entity.getUpdateColumnNames())
                .iliskiliHedefId(entity.getIliskiliHedefId())
                .durumu(entity.getDurumu())
                .build();
    }




}
