package iym.makos.domain.mahkemekarar.processor;

import iym.common.validation.ValidationResult;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mahkemekarar.dbhandler.MahkemeKararDBSaveHandler;
import iym.makos.model.dto.mahkemekarar.id.IDUzatmaKarariRequest;
import iym.makos.model.dto.mahkemekarar.id.IDUzatmaKarariResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.validator.IMahkemeKararRequestValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Date;

import static iym.makos.domain.testdata.TestDataBuilder.createTestUser;
import static iym.makos.domain.testdata.TestDataBuilder.createValidIDUzatmaKarariRequest;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDUzatmaKarariRequestProcessor.
 *
 * Tests the processor implementation for ID uzatma kararı requests.
 * Verifies validation, processing, and error handling scenarios.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("IDUzatmaKarariRequestProcessor Unit Tests")
class IDUzatmaKarariRequestProcessorTest extends BaseDomainUnitTest {
    
    @Mock
    private IMahkemeKararRequestValidator<IDUzatmaKarariRequest> mockValidator;
    
    @Mock
    private MahkemeKararDBSaveHandler<IDUzatmaKarariRequest> mockSaver;
    
    @InjectMocks
    private IDUzatmaKarariRequestProcessor processor;
    
    private IDUzatmaKarariRequest testRequest;
    private UserDetailsImpl testUser;
    
    @BeforeEach
    void setUp() {
        testRequest = createValidIDUzatmaKarariRequest();
        testUser = createTestUser();
    }
    
    @Test
    @DisplayName("Should process successfully when validation passes and save succeeds")
    void shouldProcessSuccessfully_whenValidationPassesAndSaveSucceeds() throws Exception {
        // Given
        ValidationResult validResult = validResult();
        when(mockValidator.validate(testRequest)).thenReturn(validResult);
        when(mockSaver.kaydet(any(IDUzatmaKarariRequest.class), any(Date.class), anyLong()))
                .thenReturn(12345L);
        
        // When
        IDUzatmaKarariResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseSuccess(response);
        assertThat(response.getEvrakId()).isEqualTo(12345L);
    }
    
    @Test
    @DisplayName("Should return invalid request response when validation fails")
    void shouldReturnInvalidRequestResponse_whenValidationFails() throws Exception {
        // Given
        ValidationResult invalidResult = new ValidationResult("Validation error");
        when(mockValidator.validate(testRequest)).thenReturn(invalidResult);
        
        // When
        IDUzatmaKarariResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).contains("Validation error");
        assertThat(response.getEvrakId()).isNull();
    }
    
    @Test
    @DisplayName("Should return failed response when save throws exception")
    void shouldReturnFailedResponse_whenSaveThrowsException() throws Exception {
        // Given
        ValidationResult validResult = validResult();
        when(mockValidator.validate(testRequest)).thenReturn(validResult);
        when(mockSaver.kaydet(any(IDUzatmaKarariRequest.class), any(Date.class), anyLong()))
                .thenThrow(new RuntimeException("Database error"));
        
        // When
        IDUzatmaKarariResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseError(response, MakosResponseCode.FAILED);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("INTERNAL ERROR");
        assertThat(response.getEvrakId()).isNull();
    }
    
    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() {
        // When
        Class<IDUzatmaKarariRequest> requestType = processor.getRelatedRequestType();
        
        // Then
        assertThat(requestType).isEqualTo(IDUzatmaKarariRequest.class);
    }
    
    @Test
    @DisplayName("Should return correct response type")
    void shouldReturnCorrectResponseType() {
        // When
        Class<IDUzatmaKarariResponse> responseType = processor.getRelatedResponseType();
        
        // Then
        assertThat(responseType).isEqualTo(IDUzatmaKarariResponse.class);
    }
    
    @Test
    @DisplayName("Should handle null request gracefully")
    void shouldHandleNullRequest_gracefully() {
        // When
        IDUzatmaKarariResponse response = processor.process(null, testUser);

        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("Request cannot be null");
    }
    
    @Test
    @DisplayName("Should handle null user gracefully")
    void shouldHandleNullUser_gracefully() throws Exception {
        // When
        IDUzatmaKarariResponse response = processor.process(testRequest, null);

        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("User cannot be null");
    }
}
