package iym.makos.controller.filters;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.enums.ResponseCode;
import iym.common.model.entity.makos.log.MakosKararRequestLog;
import iym.common.service.db.DbMakosKararRequestLogService;
import iym.common.util.HashUtils;
import iym.common.util.HttpUtils;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.controller.MahkemeKararTalepController;
import iym.makos.controller.filters.util.CachedBodyHttpServletRequest;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StreamUtils;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Filter for logging court decision requests in MAKOS module
 * Logs all mahkeme karar related requests and responses for audit and monitoring
 */
@Slf4j
public class MakosKararTalepRequestLogFilter implements Filter {

    private final static String MAHKEME_KARAR_DETAY_PART = MahkemeKararTalepController.MAHKEME_KARAR_DETAY_PART;
    private final static String MAHKEME_KARAR_DOSYA_PART = MahkemeKararTalepController.MAHKEME_KARAR_DOSYA_PART;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final DbMakosKararRequestLogService makosKararRequestLogService;

    public MakosKararTalepRequestLogFilter(DbMakosKararRequestLogService makosKararRequestLogService) {
        this.makosKararRequestLogService = makosKararRequestLogService;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {

        try {

            log.debug("MakosKararRequestLogFilter started for request");

            //----------  LOG REQUEST  ----------
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserDetailsImpl userDetailsImpl = null;

            if (authentication != null && !authentication.getPrincipal().equals("anonymousUser")) {
                userDetailsImpl = (UserDetailsImpl) authentication.getPrincipal();
            }

            HttpServletRequest originalRequest = (HttpServletRequest) servletRequest;
            HttpServletResponse originalResponse = (HttpServletResponse) servletResponse;

            boolean isMultipart = originalRequest.getContentType() != null &&
                    originalRequest.getContentType().toLowerCase().startsWith("multipart/");

            String jsonString = null;
            String fileName = null;
            String fileCheckSum = null;
            CachedBodyHttpServletRequest cachedRequest;
            ServletRequest requestForChain;
            ContentCachingResponseWrapper cachedResponse = new ContentCachingResponseWrapper(originalResponse);

            if (isMultipart) {
                // Use the original request for multipart parsing
                try {
                    Part kararDetayPart = originalRequest.getPart(MAHKEME_KARAR_DETAY_PART);
                    if (kararDetayPart != null){
                        jsonString = new String(kararDetayPart.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
                    }else{
                        log.error(MAHKEME_KARAR_DETAY_PART + " alani bulunamadi!");
                        sendFailedResponse(servletResponse, MAHKEME_KARAR_DETAY_PART + " alani bulunamadi!");
                        return;
                    }

                    Part kararDosyaPart = originalRequest.getPart(MAHKEME_KARAR_DOSYA_PART);
                    if (kararDosyaPart != null){
                        // TODO log those values into log table
                        fileName = getFileName(kararDosyaPart);
                        fileCheckSum = HashUtils.md5Checksum(kararDosyaPart.getInputStream().readAllBytes());
                    }else{
                        log.error(MAHKEME_KARAR_DOSYA_PART + " alani bulunamadi!");
                        sendFailedResponse(servletResponse, MAHKEME_KARAR_DOSYA_PART + " alani bulunamadi!");
                        return;
                    }
                } catch (Exception e) {
                    log.error("Multipart processing failed", e);
                    sendFailedResponse(servletResponse, "MultiPart isleme hatasi");
                    return;
                }
                // Pass the original request to the filter chain
                requestForChain = originalRequest;
            }else{
                log.error("Request is not multipart");
                sendFailedResponse(servletResponse, "MultiPart istek gonderilmelidir!");
                return;
            }
            /*
            else {
                // Use cached wrapper for non-multipart
                cachedRequest = new CachedBodyHttpServletRequest(originalRequest);
                byte[] inputStreamBytes = StreamUtils.copyToByteArray(cachedRequest.getInputStream());
                if (inputStreamBytes.length > 0) {
                    jsonString = new String(inputStreamBytes, StandardCharsets.UTF_8);
                }
                requestForChain = cachedRequest;
            }
             */

            // Extract mahkeme karar details from MahkemeKararRequest structure
            String mahkemeKararNo = null;
            String sorusturmaNo = null;
            String evrakNo = null;
            UUID requestId = null;

            try {
                Map<String, Object> jsonRequest = new ObjectMapper().readValue(jsonString, Map.class);

                // Extract requestId
                if (jsonRequest.containsKey("id")) {
                    requestId = UUID.fromString((String) jsonRequest.get("id"));
                }

                // Extract mahkemeKararNo and sorusturmaNo
                if (jsonRequest.containsKey("mahkemeKararBilgisi")) {
                    Map<String, Object> mahkemeKararBilgisi = (Map<String, Object>) jsonRequest.get("mahkemeKararBilgisi");
                    if (mahkemeKararBilgisi != null && mahkemeKararBilgisi.containsKey("mahkemeKararDetay")) {
                        Map<String, Object> mahkemeKararDetay = (Map<String, Object>) mahkemeKararBilgisi.get("mahkemeKararDetay");
                        if (mahkemeKararDetay != null) {
                            mahkemeKararNo = (String) mahkemeKararDetay.get("mahkemeKararNo");
                            sorusturmaNo = (String) mahkemeKararDetay.get("sorusturmaNo");
                        }
                    }
                }

                // Extract evrakNo
                if (jsonRequest.containsKey("evrakDetay")) {
                    Map<String, Object> evrakDetay = (Map<String, Object>) jsonRequest.get("evrakDetay");
                    if (evrakDetay != null) {
                        evrakNo = (String) evrakDetay.get("evrakNo");
                    }
                }
            } catch (Exception e) {
                log.warn("Could not parse JSON from request body or part", e);
            }

            // requested api function
            URI uri;
            try {
                uri = new URI(originalRequest.getRequestURL().toString());
            } catch (URISyntaxException e) {
                log.error("Can't get uri from {}", originalRequest.getRequestURL().toString(), e);
                throw new RuntimeException(e);
            }
            String[] segments = uri.getPath().split("/");

            // client ip address info
            String ipAddress = HttpUtils.getClientIpAddress(originalRequest);

            // build karar request log with request information
            MakosKararRequestLog kararRequestLog = new MakosKararRequestLog();
            kararRequestLog.setRequestId(requestId);
            //kararRequestLog.setRequestURL(originalRequest.getRequestURL().toString());
            kararRequestLog.setRequestURL(segments[segments.length - 1]);
            kararRequestLog.setUsername(userDetailsImpl == null ? null : userDetailsImpl.getUsername());
            kararRequestLog.setActingUsername(userDetailsImpl == null ? null : userDetailsImpl.getActingUserName());
            kararRequestLog.setUserIp(ipAddress);
            kararRequestLog.setMahkemeKararNo(mahkemeKararNo);
            kararRequestLog.setSorusturmaNo(sorusturmaNo);
            kararRequestLog.setRequestTime(LocalDateTime.now());
            kararRequestLog.setEvrakNo(evrakNo);

            // Store request body (no length limit, CLOB)
            if (jsonString.length() > 0) {
                kararRequestLog.setRequestBody(jsonString);
            }

            // save to db, then use the same object to write response info after filter chain
            makosKararRequestLogService.save(kararRequestLog);
            log.debug("MakosKararRequestLogFilter ended for request");

            //----------  DO FILTER CHAIN  ----------
            filterChain.doFilter(requestForChain, cachedResponse);

            //----------  LOG RESPONSE  ----------
            log.debug("MakosKararRequestLogFilter started for response.");
            kararRequestLog.setResponseTime(LocalDateTime.now());
            kararRequestLog.setResponseCode(cachedResponse.getStatus());
            String responseBody = new String(cachedResponse.getContentAsByteArray(), cachedResponse.getCharacterEncoding());
            if (responseBody.length() > 0) {
                kararRequestLog.setResponseBody(responseBody);
            }
            makosKararRequestLogService.save(kararRequestLog);

            // Write the cached response body to the actual response output stream
            cachedResponse.copyBodyToResponse();
            log.debug("MakosKararRequestLogFilter ended for response");

        } catch (Exception e) {
            log.error("MakosKararRequestLogFilter failed", e);
            sendFailedResponse(servletResponse, "MakosKararTalepRequestLogFilter hatasi");
        }
    }

    /**
     * Utility method to get file name from HTTP header content-disposition
     */
    private String getFileName(Part part) {
        String contentDisposition = part.getHeader("content-disposition");
        String[] tokens = contentDisposition.split(";");
        for (String token : tokens) {
            if (token.trim().startsWith("filename")) {
                return token.substring(token.indexOf("=") + 2, token.length() - 1);
            }
        }
        return "";
    }

    private void sendFailedResponse(ServletResponse servletResponse, String errorMessage) throws IOException {
        HttpServletResponse httpServletResponse = (HttpServletResponse) servletResponse;
        httpServletResponse.setStatus(HttpServletResponse.SC_BAD_REQUEST);
        httpServletResponse.setContentType("application/json");

        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("responseCode", ResponseCode.FAILED);
        responseMap.put("responseMessage", errorMessage);
        objectMapper.writeValue(httpServletResponse.getWriter(), responseMap);
    }
}
