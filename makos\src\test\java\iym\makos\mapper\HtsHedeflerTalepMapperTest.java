package iym.makos.mapper;

import iym.common.model.entity.iym.talep.HtsHedeflerTalep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.model.dto.db.HtsHedeflerTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class HtsHedeflerTalepMapperTest {

    private HtsHedeflerTalepMapper htsHedeflerTalepMapper;
    private HtsHedeflerTalep htsHedeflerTalep;
    private HtsHedeflerTalepDTO htsHedeflerTalepDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        htsHedeflerTalepMapper = new HtsHedeflerTalepMapper();
        testDate = new Date();

        htsHedeflerTalep = HtsHedeflerTalep.builder()
                .id(1L)
                .mahkemeKararId(100L)
                .hedefNo("5551234567")
                .karsiHedefNo("5559876543")
                .sorguTipi("ARAMA")
                .baslangicTarihi(testDate)
                .bitisTarihi(testDate)
                .tespitTuru("DETAYLI")
                .kullaniciId("1")
                .durumu("AKTIF")
                .build();

        htsHedeflerTalepDTO = HtsHedeflerTalepDTO.builder()
                .id(1L)
                .mahkemeKararId(100L)
                .hedefNo("5551234567")
                .karsiHedefNo("5559876543")
                .sorguTipi("ARAMA")
                .baslangicTarihi(testDate)
                .bitisTarihi(testDate)
                .tespitTuru("DETAYLI")
                .kullaniciId("1")
                .durumu("AKTIF")
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        HtsHedeflerTalepDTO result = htsHedeflerTalepMapper.toDto(htsHedeflerTalep);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(htsHedeflerTalep.getId());
        assertThat(result.getMahkemeKararId()).isEqualTo(htsHedeflerTalep.getMahkemeKararId());
        assertThat(result.getHedefNo()).isEqualTo(htsHedeflerTalep.getHedefNo());
        assertThat(result.getKarsiHedefNo()).isEqualTo(htsHedeflerTalep.getKarsiHedefNo());
        assertThat(result.getSorguTipi()).isEqualTo(htsHedeflerTalep.getSorguTipi());
        assertThat(result.getBaslangicTarihi()).isEqualTo(htsHedeflerTalep.getBaslangicTarihi());
        assertThat(result.getBitisTarihi()).isEqualTo(htsHedeflerTalep.getBitisTarihi());
        assertThat(result.getTespitTuru()).isEqualTo(htsHedeflerTalep.getTespitTuru());
        assertThat(result.getKullaniciId()).isEqualTo(htsHedeflerTalep.getKullaniciId());
        assertThat(result.getDurumu()).isEqualTo(htsHedeflerTalep.getDurumu());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HtsHedeflerTalepDTO result = htsHedeflerTalepMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        HtsHedeflerTalep result = htsHedeflerTalepMapper.toEntity(htsHedeflerTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(htsHedeflerTalepDTO.getId());
        assertThat(result.getMahkemeKararId()).isEqualTo(htsHedeflerTalepDTO.getMahkemeKararId());
        assertThat(result.getHedefNo()).isEqualTo(htsHedeflerTalepDTO.getHedefNo());
        assertThat(result.getKarsiHedefNo()).isEqualTo(htsHedeflerTalepDTO.getKarsiHedefNo());
        assertThat(result.getSorguTipi()).isEqualTo(htsHedeflerTalepDTO.getSorguTipi());
        assertThat(result.getBaslangicTarihi()).isEqualTo(htsHedeflerTalepDTO.getBaslangicTarihi());
        assertThat(result.getBitisTarihi()).isEqualTo(htsHedeflerTalepDTO.getBitisTarihi());
        assertThat(result.getTespitTuru()).isEqualTo(htsHedeflerTalepDTO.getTespitTuru());
        assertThat(result.getKullaniciId()).isEqualTo(htsHedeflerTalepDTO.getKullaniciId());
        assertThat(result.getDurumu()).isEqualTo(htsHedeflerTalepDTO.getDurumu());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        HtsHedeflerTalep result = htsHedeflerTalepMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        HtsHedeflerTalep existingEntity = HtsHedeflerTalep.builder()
                .id(1L)
                .mahkemeKararId(100L)
                .hedefNo("5551234567")
                .karsiHedefNo("5559876543")
                .sorguTipi("ARAMA")
                .baslangicTarihi(testDate)
                .bitisTarihi(testDate)
                .tespitTuru("DETAYLI")
                .kullaniciId("1")
                .durumu("AKTIF")
                .build();

        HtsHedeflerTalepDTO updatedDto = HtsHedeflerTalepDTO.builder()
                .id(1L)
                .mahkemeKararId(101L) // Should not be updated
                .hedefNo("5551234568")
                .karsiHedefNo("5559876544")
                .sorguTipi("SMS")
                .baslangicTarihi(new Date(testDate.getTime() + 86400000)) // +1 day
                .bitisTarihi(new Date(testDate.getTime() + 172800000)) // +2 days
                .tespitTuru("ÖZET")
                .kullaniciId("2")
                .durumu("PASIF")
                .build();

        // When
        HtsHedeflerTalep result = htsHedeflerTalepMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getMahkemeKararId()).isEqualTo(100L); // Should not be updated
        assertThat(result.getHedefNo()).isEqualTo("5551234568");
        assertThat(result.getKarsiHedefNo()).isEqualTo("5559876544");
        assertThat(result.getSorguTipi()).isEqualTo("SMS");
        assertThat(result.getBaslangicTarihi()).isEqualTo(updatedDto.getBaslangicTarihi());
        assertThat(result.getBitisTarihi()).isEqualTo(updatedDto.getBitisTarihi());
        assertThat(result.getTespitTuru()).isEqualTo("ÖZET");
        assertThat(result.getKullaniciId()).isEqualTo("2");
        assertThat(result.getDurumu()).isEqualTo("PASIF");
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        HtsHedeflerTalep existingEntity = HtsHedeflerTalep.builder()
                .id(1L)
                .mahkemeKararId(100L)
                .hedefNo("5551234567")
                .karsiHedefNo("5559876543")
                .sorguTipi("ARAMA")
                .baslangicTarihi(testDate)
                .bitisTarihi(testDate)
                .tespitTuru("DETAYLI")
                .kullaniciId("1")
                .durumu("AKTIF")
                .build();

        // When
        HtsHedeflerTalep result = htsHedeflerTalepMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HtsHedeflerTalep result = htsHedeflerTalepMapper.updateEntityFromDto(null, htsHedeflerTalepDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<HtsHedeflerTalep> entityList = Arrays.asList(htsHedeflerTalep, htsHedeflerTalep);

        // When
        List<HtsHedeflerTalepDTO> result = htsHedeflerTalepMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(htsHedeflerTalep.getId());
        assertThat(result.get(1).getId()).isEqualTo(htsHedeflerTalep.getId());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<HtsHedeflerTalepDTO> result = htsHedeflerTalepMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<HtsHedeflerTalepDTO> dtoList = Arrays.asList(htsHedeflerTalepDTO, htsHedeflerTalepDTO);

        // When
        List<HtsHedeflerTalep> result = htsHedeflerTalepMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(htsHedeflerTalepDTO.getId());
        assertThat(result.get(1).getId()).isEqualTo(htsHedeflerTalepDTO.getId());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<HtsHedeflerTalep> result = htsHedeflerTalepMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
