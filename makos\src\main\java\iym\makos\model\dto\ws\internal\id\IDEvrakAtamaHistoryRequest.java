package iym.makos.model.dto.ws.internal.id;

import iym.common.validation.ValidationResult;
import iym.makos.model.BaseMakosRequest;
import iym.makos.validator.custom.MakosRequestValid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDEvrakAtamaHistoryRequest extends BaseMakosRequest {

    @NotNull
    Long evrakId;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDEvrakAtamaHistoryRequest is valid");
        ValidationResult validationResult = new ValidationResult(true);

        return validationResult;
    }

}

