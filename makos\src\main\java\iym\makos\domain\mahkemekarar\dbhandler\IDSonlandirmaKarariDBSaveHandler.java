package iym.makos.domain.mahkemekarar.dbhandler;

import iym.common.enums.HedefTip;
import iym.common.model.entity.iym.mk.He<PERSON>fler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.*;
import iym.db.jpa.dao.*;
import iym.db.jpa.dao.mk.HedeflerRepo;
import iym.db.jpa.dao.mk.MahkemeKararRepo;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.HedeflerAidiyatTalepRepo;
import iym.db.jpa.dao.talep.HedeflerDetayTalepRepo;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.makos.model.dto.mahkemekarar.id.IDSonlandirmaKarariRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public abstract class IDSonlandirmaKarariDBSaveHandler extends MahkemeKararRequestDbSaveHandlerBase<IDSonlandirmaKarariRequest> {
    private MahkemeKararRepo mahkemeKararRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private HedeflerTalepRepo hedeflerTalepRepo;
    private HedeflerDetayTalepRepo hedeflerDetayTalepRepo;
    private HedeflerRepo hedeflerRepo;
    private HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private KararRequestMapper kararRequestMapper;

    @Autowired
    public IDSonlandirmaKarariDBSaveHandler(MahkemeKararRepo mahkemeKararRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerRepo hedeflerRepo
            , HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , KararRequestMapper kararRequestMapper
            , HedeflerDetayTalepRepo hedeflerDetayTalepRepo) {
        this.mahkemeKararRepo = mahkemeKararRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.hedeflerRepo = hedeflerRepo;
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.kararRequestMapper = kararRequestMapper;
        this.hedeflerDetayTalepRepo = hedeflerDetayTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;

    }

    @Override
    @Transactional
    public Long kaydet(IDSonlandirmaKarariRequest request, Date kayitTarihi, Long kullaniciId) throws Exception {
        try {
            Long mahkemeKararTalepId = mahkemeKararRequestCommonDbSaver.handleDbSave(request, kayitTarihi, kullaniciId);
            Long evrakId = null;
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEKARARTALEP_KAYDETMEHATASI);
            } else {
                evrakId = mahkemeKararTalepOpt.get().getEvrakId();
            }

            //List<MahkemeAidiyatTalep> savedMahkemeAidiyatList = saveMahkemeKararTalepAidiyatListesi(kararRequest.getMahkemeAidiyatKodlari(), mahkemeKararTalepId);

            //List<MahkemeSuclarTalep> savedMahkemeSucList = saveMahkemeKararTalepSucKoduListesi(kararRequest.getMahkemeSucTipiKodlari(), mahkemeKararTalepId);

            for (IDHedefDetay iDHedefDetay : request.getHedefDetayListesi()) {
                String hedefNo = iDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefNo();
                HedefTip hedefTipi = iDHedefDetay.getHedefNoAdSoyad().getHedef().getHedefTip();

                //Uzatmaya konu mahkeme karari bul
                MahkemeKararDetay ilgiliMahhemeKararDetay = iDHedefDetay.getIlgiliMahkemeKararDetayi();
                Optional<MahkemeKarar> mahkemeKararOpt = mahkemeKararRepo.findBy(ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKodu()
                        , ilgiliMahhemeKararDetay.getMahkemeKararNo()
                        , ilgiliMahhemeKararDetay.getSorusturmaNo());
                if (mahkemeKararOpt.isEmpty()) {
                    String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, ilgiliMahhemeKararDetay.getMahkemeIlIlceKodu()
                            , ilgiliMahhemeKararDetay.getMahkemeKodu(), ilgiliMahhemeKararDetay.getMahkemeKararNo()
                            , ilgiliMahhemeKararDetay.getSorusturmaNo());
                    throw new MakosResponseException(errorStr);
                }
                MahkemeKarar iliskiliMahkemeKarar = mahkemeKararOpt.get();


                //Uzatma Hedefi uzatmaya konu karar listesinin hedeflerinde var mi?
                Optional<Hedefler> iliskiliHedefOpt = hedeflerRepo.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                if (iliskiliHedefOpt.isEmpty()) {
                    throw new MakosResponseException(MakosResponseErrorCodes.HEDEF_BULUNAMADI, hedefNo, String.valueOf(hedefTipi.getHedefKodu()));
                }

                DetayMahkemeKararTalep detayMahkemeKararTalep = kararRequestMapper.toDMahkemeKararTalepDetay(iliskiliMahkemeKarar, mahkemeKararTalepId, evrakId);
                DetayMahkemeKararTalep savedDMahkemeKararTalep = dMahkemeKararTalepRepo.save(detayMahkemeKararTalep);

                HedeflerDetayTalep hedeflerDetayTalep = kararRequestMapper.toHedeflerDetayTalep(iDHedefDetay, mahkemeKararTalepId, iliskiliHedefOpt.get().getId(), kullaniciId, kayitTarihi);
                hedeflerDetayTalep.setMahkemeKararDetayTalepId(savedDMahkemeKararTalep.getId());
                HedeflerDetayTalep savedHedeflerDetayTalep = hedeflerDetayTalepRepo.save(hedeflerDetayTalep);

                HedeflerTalep hedeflerTalepEntity = kararRequestMapper.toHedeflerTalep(iDHedefDetay, mahkemeKararTalepId, kullaniciId, kayitTarihi);
                hedeflerTalepEntity.setKapatmaKararId(mahkemeKararOpt.get().getId());

                HedeflerTalep savedHedeflerTalep = hedeflerTalepRepo.save(hedeflerTalepEntity);
                //Hedef Aidiyat Talepleri Kaydet
                if (iDHedefDetay.getHedefAidiyatKodlari() != null) {
                    List<HedeflerAidiyatTalep> savedHedefAidiyatList = saveHedeflerTalepSucKoduListesi(iDHedefDetay.getHedefAidiyatKodlari(), savedHedeflerTalep.getId(),
                            savedHedeflerTalep.getHedefNo(), kayitTarihi, kullaniciId);
                }
            }

            return mahkemeKararTalepId;
        } catch (Exception ex) {
            log.error("IDSonlandirmaKarari handleDbSave failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            throw new RuntimeException(ex);
        }
    }



    private List<HedeflerAidiyatTalep> saveHedeflerTalepSucKoduListesi(List<String> sucKoduListesi, Long hedeflerTalepId, String hedefNo, Date islemTarihi, Long kullaniciId) throws Exception {
        List<HedeflerAidiyatTalep> result = new ArrayList<>();

        for (String aidiyatKodu : sucKoduListesi) {
            HedeflerAidiyatTalep hedeflerAidiyatTalep = kararRequestMapper.toHedeflerAidiyatTalep(kullaniciId, hedeflerTalepId, aidiyatKodu, islemTarihi);
            HedeflerAidiyatTalep savedHedeflerAidiyatTalep = hedeflerAidiyatTalepRepo.save(hedeflerAidiyatTalep);
            result.add(savedHedeflerAidiyatTalep);
        }
        return result;
    }


}

