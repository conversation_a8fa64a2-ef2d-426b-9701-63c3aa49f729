package iym.makos.service;

import iym.common.model.entity.iym.talep.MahkemeHedeflerAidiyatTalep;
import iym.common.service.db.DbMahkemeHedeflerAidiyatTalepService;
import iym.makos.mapper.MahkemeHedeflerAidiyatTalepMapper;
import iym.makos.service.db.MahkemeHedeflerAidiyatTalepService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.model.dto.db.MahkemeHedeflerAidiyatTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MahkemeHedeflerAidiyatTalepServiceTest {

    @Mock
    private DbMahkemeHedeflerAidiyatTalepService dbMahkemeHedeflerAidiyatTalepService;

    @Mock
    private MahkemeHedeflerAidiyatTalepMapper mahkemeHedeflerAidiyatTalepMapper;

    @InjectMocks
    private MahkemeHedeflerAidiyatTalepService mahkemeHedeflerAidiyatTalepService;

    private MahkemeHedeflerAidiyatTalep mahkemeHedeflerAidiyatTalep;
    private MahkemeHedeflerAidiyatTalepDTO mahkemeHedeflerAidiyatTalepDTO;
    private List<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepList;
    private List<MahkemeHedeflerAidiyatTalepDTO> mahkemeHedeflerAidiyatTalepDTOList;
    private Date testDate;

    @BeforeEach
    void setUp() {
        testDate = new Date();

        mahkemeHedeflerAidiyatTalep = MahkemeHedeflerAidiyatTalep.builder()
                .id(1L)
                .hedefId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .mahkemeKararId(200L)
                .durumu("AKTIF")
                .kullaniciId(300L)
                .build();

        MahkemeHedeflerAidiyatTalep mahkemeHedeflerAidiyatTalep2 = MahkemeHedeflerAidiyatTalep.builder()
                .id(2L)
                .hedefId(101L)
                .aidiyatKod("AIDIYAT2")
                .tarih(testDate)
                .mahkemeKararId(201L)
                .durumu("AKTIF")
                .kullaniciId(301L)
                .build();

        mahkemeHedeflerAidiyatTalepDTO = MahkemeHedeflerAidiyatTalepDTO.builder()
                .id(1L)
                .hedefId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .mahkemeKararId(200L)
                .durumu("AKTIF")
                .kullaniciId(300L)
                .build();

        MahkemeHedeflerAidiyatTalepDTO mahkemeHedeflerAidiyatTalepDTO2 = MahkemeHedeflerAidiyatTalepDTO.builder()
                .id(2L)
                .hedefId(101L)
                .aidiyatKod("AIDIYAT2")
                .tarih(testDate)
                .mahkemeKararId(201L)
                .durumu("AKTIF")
                .kullaniciId(301L)
                .build();

        mahkemeHedeflerAidiyatTalepList = Arrays.asList(mahkemeHedeflerAidiyatTalep, mahkemeHedeflerAidiyatTalep2);
        mahkemeHedeflerAidiyatTalepDTOList = Arrays.asList(mahkemeHedeflerAidiyatTalepDTO, mahkemeHedeflerAidiyatTalepDTO2);
    }

    @Test
    void findAll_shouldReturnAllMahkemeHedeflerAidiyatTalep() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findAll()).thenReturn(mahkemeHedeflerAidiyatTalepList);
        when(mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList)).thenReturn(mahkemeHedeflerAidiyatTalepDTOList);

        // When
        List<MahkemeHedeflerAidiyatTalepDTO> result = mahkemeHedeflerAidiyatTalepService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(mahkemeHedeflerAidiyatTalepDTOList);
        verify(dbMahkemeHedeflerAidiyatTalepService).findAll();
        verify(mahkemeHedeflerAidiyatTalepMapper).toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfMahkemeHedeflerAidiyatTalep() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<MahkemeHedeflerAidiyatTalep> mahkemeHedeflerAidiyatTalepPage = new PageImpl<>(mahkemeHedeflerAidiyatTalepList, pageable, mahkemeHedeflerAidiyatTalepList.size());
        
        when(dbMahkemeHedeflerAidiyatTalepService.findAll(pageable)).thenReturn(mahkemeHedeflerAidiyatTalepPage);
        when(mahkemeHedeflerAidiyatTalepMapper.toDtoList(mahkemeHedeflerAidiyatTalepList)).thenReturn(mahkemeHedeflerAidiyatTalepDTOList);

        // When
        Page<MahkemeHedeflerAidiyatTalepDTO> result = mahkemeHedeflerAidiyatTalepService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(mahkemeHedeflerAidiyatTalepDTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbMahkemeHedeflerAidiyatTalepService).findAll(pageable);
        verify(mahkemeHedeflerAidiyatTalepMapper).toDtoList(mahkemeHedeflerAidiyatTalepList);
    }

    @Test
    void findById_shouldReturnMahkemeHedeflerAidiyatTalep_whenExists() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.of(mahkemeHedeflerAidiyatTalep));
        when(mahkemeHedeflerAidiyatTalepMapper.toDto(mahkemeHedeflerAidiyatTalep)).thenReturn(mahkemeHedeflerAidiyatTalepDTO);

        // When
        MahkemeHedeflerAidiyatTalepDTO result = mahkemeHedeflerAidiyatTalepService.findById(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(mahkemeHedeflerAidiyatTalepDTO);
        verify(dbMahkemeHedeflerAidiyatTalepService).findById(1L);
        verify(mahkemeHedeflerAidiyatTalepMapper).toDto(mahkemeHedeflerAidiyatTalep);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> mahkemeHedeflerAidiyatTalepService.findById(1L));
        verify(dbMahkemeHedeflerAidiyatTalepService).findById(1L);
        verify(mahkemeHedeflerAidiyatTalepMapper, never()).toDto(any());
    }

    @Test
    void findByHedefId_shouldReturnMahkemeHedeflerAidiyatTalepList() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findByHedefId(100L)).thenReturn(Arrays.asList(mahkemeHedeflerAidiyatTalep));
        when(mahkemeHedeflerAidiyatTalepMapper.toDtoList(Arrays.asList(mahkemeHedeflerAidiyatTalep))).thenReturn(Arrays.asList(mahkemeHedeflerAidiyatTalepDTO));

        // When
        List<MahkemeHedeflerAidiyatTalepDTO> result = mahkemeHedeflerAidiyatTalepService.findByHedefId(100L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(mahkemeHedeflerAidiyatTalepDTO);
        verify(dbMahkemeHedeflerAidiyatTalepService).findByHedefId(100L);
        verify(mahkemeHedeflerAidiyatTalepMapper).toDtoList(Arrays.asList(mahkemeHedeflerAidiyatTalep));
    }

    @Test
    void create_shouldCreateMahkemeHedeflerAidiyatTalep() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findByHedefIdAndAidiyatKodAndMahkemeKararId(
                100L, "AIDIYAT1", 200L)).thenReturn(Optional.empty());
        when(mahkemeHedeflerAidiyatTalepMapper.toEntity(mahkemeHedeflerAidiyatTalepDTO)).thenReturn(mahkemeHedeflerAidiyatTalep);
        when(mahkemeHedeflerAidiyatTalepMapper.toDto(mahkemeHedeflerAidiyatTalep)).thenReturn(mahkemeHedeflerAidiyatTalepDTO);

        // When
        MahkemeHedeflerAidiyatTalepDTO result = mahkemeHedeflerAidiyatTalepService.create(mahkemeHedeflerAidiyatTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(mahkemeHedeflerAidiyatTalepDTO);
        verify(dbMahkemeHedeflerAidiyatTalepService).findByHedefIdAndAidiyatKodAndMahkemeKararId(
                100L, "AIDIYAT1", 200L);
        verify(mahkemeHedeflerAidiyatTalepMapper).toEntity(mahkemeHedeflerAidiyatTalepDTO);
        verify(dbMahkemeHedeflerAidiyatTalepService).save(mahkemeHedeflerAidiyatTalep);
        verify(mahkemeHedeflerAidiyatTalepMapper).toDto(mahkemeHedeflerAidiyatTalep);
    }

    @Test
    void create_shouldThrowException_whenMahkemeHedeflerAidiyatTalepAlreadyExists() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findByHedefIdAndAidiyatKodAndMahkemeKararId(
                100L, "AIDIYAT1", 200L)).thenReturn(Optional.of(mahkemeHedeflerAidiyatTalep));

        // When/Then
        assertThrows(ResponseStatusException.class, () -> mahkemeHedeflerAidiyatTalepService.create(mahkemeHedeflerAidiyatTalepDTO));
        verify(dbMahkemeHedeflerAidiyatTalepService).findByHedefIdAndAidiyatKodAndMahkemeKararId(
                100L, "AIDIYAT1", 200L);
        verify(mahkemeHedeflerAidiyatTalepMapper, never()).toEntity(any());
        verify(dbMahkemeHedeflerAidiyatTalepService, never()).save(any());
        verify(mahkemeHedeflerAidiyatTalepMapper, never()).toDto(any());
    }

    @Test
    void update_shouldUpdateMahkemeHedeflerAidiyatTalep() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.of(mahkemeHedeflerAidiyatTalep));
        when(dbMahkemeHedeflerAidiyatTalepService.findByHedefIdAndAidiyatKodAndMahkemeKararId(
                100L, "AIDIYAT1", 200L)).thenReturn(Optional.of(mahkemeHedeflerAidiyatTalep));
        when(mahkemeHedeflerAidiyatTalepMapper.updateEntityFromDto(mahkemeHedeflerAidiyatTalep, mahkemeHedeflerAidiyatTalepDTO)).thenReturn(mahkemeHedeflerAidiyatTalep);
        when(mahkemeHedeflerAidiyatTalepMapper.toDto(mahkemeHedeflerAidiyatTalep)).thenReturn(mahkemeHedeflerAidiyatTalepDTO);

        // When
        MahkemeHedeflerAidiyatTalepDTO result = mahkemeHedeflerAidiyatTalepService.update(1L, mahkemeHedeflerAidiyatTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(mahkemeHedeflerAidiyatTalepDTO);
        verify(dbMahkemeHedeflerAidiyatTalepService).findById(1L);
        verify(dbMahkemeHedeflerAidiyatTalepService).findByHedefIdAndAidiyatKodAndMahkemeKararId(
                100L, "AIDIYAT1", 200L);
        verify(mahkemeHedeflerAidiyatTalepMapper).updateEntityFromDto(mahkemeHedeflerAidiyatTalep, mahkemeHedeflerAidiyatTalepDTO);
        verify(dbMahkemeHedeflerAidiyatTalepService).update(mahkemeHedeflerAidiyatTalep);
        verify(mahkemeHedeflerAidiyatTalepMapper).toDto(mahkemeHedeflerAidiyatTalep);
    }

    @Test
    void update_shouldThrowException_whenMahkemeHedeflerAidiyatTalepNotExists() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> mahkemeHedeflerAidiyatTalepService.update(1L, mahkemeHedeflerAidiyatTalepDTO));
        verify(dbMahkemeHedeflerAidiyatTalepService).findById(1L);
        verify(dbMahkemeHedeflerAidiyatTalepService, never()).findByHedefIdAndAidiyatKodAndMahkemeKararId(
                anyLong(), anyString(), anyLong());
        verify(mahkemeHedeflerAidiyatTalepMapper, never()).updateEntityFromDto(any(), any());
        verify(dbMahkemeHedeflerAidiyatTalepService, never()).update(any());
        verify(mahkemeHedeflerAidiyatTalepMapper, never()).toDto(any());
    }

    @Test
    void delete_shouldDeleteMahkemeHedeflerAidiyatTalep() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.of(mahkemeHedeflerAidiyatTalep));

        // When
        mahkemeHedeflerAidiyatTalepService.delete(1L);

        // Then
        verify(dbMahkemeHedeflerAidiyatTalepService).findById(1L);
        verify(dbMahkemeHedeflerAidiyatTalepService).delete(mahkemeHedeflerAidiyatTalep);
    }

    @Test
    void delete_shouldThrowException_whenMahkemeHedeflerAidiyatTalepNotExists() {
        // Given
        when(dbMahkemeHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> mahkemeHedeflerAidiyatTalepService.delete(1L));
        verify(dbMahkemeHedeflerAidiyatTalepService).findById(1L);
        verify(dbMahkemeHedeflerAidiyatTalepService, never()).delete(any());
    }
}
