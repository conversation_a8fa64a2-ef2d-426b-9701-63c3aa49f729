package iym.makos.controller;

import iym.common.enums.MakosUserRoleType;
import iym.common.enums.ResponseCode;
import iym.common.model.api.ApiResponse;
import iym.common.model.entity.makos.MakosUser;
import iym.common.service.db.DbMakosUserService;
import iym.common.util.ExceptionUtils;
import iym.makos.model.dto.user.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.Collection;
import java.util.List;

/**
 * User Controller for MAKOS Module
 * Handles user management operations
 */
@RestController
@RequestMapping("/user")
@Slf4j
public class UserController {

    @Autowired
    private DbMakosUserService userService;
    
    @Autowired
    PasswordEncoder passwordEncoder;

    /**
     * Get user by ID
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<GetUserByIdResponse> getUserById(@PathVariable(value = "id") Long id) {
        MakosUser user = userService.findById(id)
                .orElseThrow(() -> ExceptionUtils.USER_NOT_FOUND);
        
        return ResponseEntity.ok(GetUserByIdResponse.builder()
                .user(user)
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("User retrieved successfully")
                        .build())
                .build());
    }

    /**
     * Get all users
     */
    @GetMapping("/getAllUsers")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UsersListResponse> getAllUsers() {
        List<MakosUser> userList = userService.findAllByOrderByUsernameAsc();
        return ResponseEntity.ok(UsersListResponse.builder()
                .users(userList)
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("Users retrieved successfully")
                        .build())
                .build());
    }

    /**
     * Get users for admin - simplified version without role-based filtering
     */
    @GetMapping("/getUsersForAdmin")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_TEMSILCISI')")
    public ResponseEntity<UsersListResponse> getUsersForAdmin(Authentication auth) {
        @SuppressWarnings("unchecked")
        Collection<SimpleGrantedAuthority> authorities = (Collection<SimpleGrantedAuthority>) auth.getAuthorities();
        MakosUserRoleType secureUserRole = MakosUserRoleType.valueOf(authorities.iterator().next().getAuthority());
        
        // For now, return all users ordered by username
        // TODO: Implement role-based filtering if needed
        List<MakosUser> userList = userService.findAllByOrderByUsernameAsc();
        log.info("getUsersForAdmin called by user with role: {}", secureUserRole);
        return ResponseEntity.ok(UsersListResponse.builder()
                .users(userList)
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("Users retrieved successfully")
                        .build())
                .build());
    }

    /**
     * Activate user
     */
    @PostMapping("/activate")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_TEMSILCISI')")
    public ResponseEntity<ActivateUserResponse> activateUser(@RequestBody UsernameRequest request, Authentication auth) {
        @SuppressWarnings("unchecked")
        Collection<SimpleGrantedAuthority> authorities = (Collection<SimpleGrantedAuthority>) auth.getAuthorities();
        MakosUserRoleType secureUserRole = MakosUserRoleType.valueOf(authorities.iterator().next().getAuthority());
        
        log.info("Activating user: {} by user with role: {}", request.getUsername(), secureUserRole);
        userService.activateUser(request.getUsername());
        return ResponseEntity.ok(ActivateUserResponse.builder()
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("User activated successfully")
                        .build())
                .build());
    }

    /**
     * Deactivate user
     */
    @PostMapping("/deactivate")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_TEMSILCISI')")
    public ResponseEntity<DeactivateUserResponse> deactivateUser(@RequestBody UsernameRequest request, Authentication auth) {
        @SuppressWarnings("unchecked")
        Collection<SimpleGrantedAuthority> authorities = (Collection<SimpleGrantedAuthority>) auth.getAuthorities();
        MakosUserRoleType secureUserRole = MakosUserRoleType.valueOf(authorities.iterator().next().getAuthority());
        
        log.info("Deactivating user: {} by user with role: {}", request.getUsername(), secureUserRole);
        userService.deactivateUser(request.getUsername());
        return ResponseEntity.ok(DeactivateUserResponse.builder()
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("User deactivated successfully")
                        .build())
                .build());
    }

    /**
     * Get user by username
     */
    @GetMapping
    public ResponseEntity<GetUserResponse> getUser(@RequestParam(value = "username") String username) {
        MakosUser user = userService.findByUsername(username)
                .orElseThrow(() -> ExceptionUtils.USER_NOT_FOUND);
        
        return ResponseEntity.ok(GetUserResponse.builder()
                .user(user)
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("User retrieved successfully")
                        .build())
                .build());
    }

    /**
     * Add new user
     */
    @PostMapping("/add")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_TEMSILCISI')")
    public ResponseEntity<AddUserResponse> addUser(@RequestBody AddUserRequest request) {
        userService.findByUsername(request.getUser().getUsername())
                .ifPresent(s -> {
                    throw new ResponseStatusException(HttpStatus.PRECONDITION_FAILED, "User already exist");
                });

        // Set password from newPassword field and encode it
        request.getUser().setPassword(request.getUser().getNewPassword());
        request.getUser().setPassword(passwordEncoder.encode(request.getUser().getPassword()));
        userService.save(request.getUser());

        log.info("User added successfully: {}", request.getUser().getUsername());
        return ResponseEntity.ok(AddUserResponse.builder()
                .id(request.getId())
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("Add User Success")
                        .build())
                .build());
    }

    /**
     * Update user
     */
    @PostMapping("/update")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_TEMSILCISI')")
    public ResponseEntity<UpdateUserResponse> updateUser(@RequestBody UpdateUserRequest request, Authentication auth) {
        @SuppressWarnings("unchecked")
        Collection<SimpleGrantedAuthority> authorities = (Collection<SimpleGrantedAuthority>) auth.getAuthorities();
        MakosUserRoleType secureUserRole = MakosUserRoleType.valueOf(authorities.iterator().next().getAuthority());
        
        log.info("Updating user: {} by user with role: {}", request.getUser().getUsername(), secureUserRole);
        userService.updateUser(request.getUser());
        return ResponseEntity.ok(UpdateUserResponse.builder()
                .id(request.getId())
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("Update User Success")
                        .build())
                .build());
    }

    /**
     * Delete user
     */
    @PostMapping("/delete")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<DeleteUserResponse> deleteUser(@RequestBody DeleteUserRequest request) {
        log.info("Deleting user with ID: {}", request.getUserId());
        userService.deleteById(request.getUserId());
        return ResponseEntity.ok(DeleteUserResponse.builder()
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("User deleted successfully")
                        .build())
                .build());
    }
}
