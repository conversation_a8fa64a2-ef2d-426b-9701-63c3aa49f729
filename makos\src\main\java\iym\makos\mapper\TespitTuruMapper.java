package iym.makos.mapper;

import iym.common.model.entity.iym.SorguTipleri;
import iym.common.model.entity.iym.TespitTurleri;
import iym.makos.model.dto.db.SorguTipiDTO;
import iym.makos.model.dto.db.TespitTuruDTO;
import org.springframework.stereotype.Component;

/**
 * Mapper for MahkemeBilgiMapper entity and DTO
 */
@Component
public class TespitTuruMapper {


    public TespitTuruDTO toDto(TespitTurleri entity) {
        if (entity == null) {
            return null;
        }

        return TespitTuruDTO.builder()
                .tespitTuru(entity.getTespitTuru())
                .aciklama(entity.getAciklama())
                .build();
    }


    public TespitTurleri toEntity(TespitTuruDTO dto) {
        if (dto == null) {
            return null;
        }

        return TespitTurleri.builder()
                .tespitTuru(dto.getTespitTuru())
                .aciklama(dto.getAciklama())
                .build();
    }


}
