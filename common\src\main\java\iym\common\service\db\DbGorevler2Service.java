package iym.common.service.db;

import iym.common.model.entity.iym.Gorevler2;
import iym.common.model.entity.iym.Gorevler2PK;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for Gorevler2 entity
 */
public interface DbGorevler2Service extends GenericDbService<Gorevler2, Gorevler2PK> {

    List<Gorevler2> findByGorev(String gorev);
    
    List<Gorevler2> findByGorevKodu(Long gorevKodu);
    
    Optional<Gorevler2> findByGorevAndGorevKodu(String gorev, Long gorevKodu);
    
    List<Gorevler2> findByGorevImzaAdi(String gorevImzaAdi);
    
    List<Gorevler2> findByImzaYetki(String imzaYetki);
    
    List<Gorevler2> findBySilindi(Long silindi);
    
    List<Gorevler2> findByGorevKodu2(String gorevKodu2);
    
    List<Gorevler2> findByGorevTipi(String gorevTipi);
    
    List<Gorevler2> findByOncelik(Long oncelik);
    
    List<Gorevler2> findByBaslamaTarihiBetween(Date startDate, Date endDate);
    
    List<Gorevler2> findByBitisTarihiBetween(Date startDate, Date endDate);
    
    List<Gorevler2> findByBaslamaTarihiLessThanEqualAndBitisTarihiGreaterThanEqual(Date date1, Date date2);
    
    List<Gorevler2> findByGorevContainingIgnoreCase(String gorev);
    
    List<Gorevler2> findByGorevImzaAdiContainingIgnoreCase(String gorevImzaAdi);
    
    List<Gorevler2> findByGorevTipiAndImzaYetki(String gorevTipi, String imzaYetki);
    
    List<Gorevler2> findByGorevTipiAndSilindi(String gorevTipi, Long silindi);
    
    boolean existsByGorevAndGorevKodu(String gorev, Long gorevKodu);
}
