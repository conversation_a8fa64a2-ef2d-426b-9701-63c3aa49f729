package iym.makos.model.dto.ws.internal.id;

import iym.common.model.api.ApiResponseBase;
import iym.makos.model.dto.mahkemekarar.MahkemeKararResponse;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class IDEvrakAtamaResponse extends ApiResponseBase {

    @NotNull
    private Long evrakId;

    private String aciklama;

}