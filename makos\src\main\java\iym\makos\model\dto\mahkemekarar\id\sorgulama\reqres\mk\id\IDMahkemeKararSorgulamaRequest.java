package iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mk.id;

import iym.common.model.entity.iym.sorgu.MahkemeKararSorguParam;
import iym.common.validation.ValidationResult;
import iym.makos.model.BaseMakosRequest;
import iym.makos.validator.custom.MakosRequestValid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDMahkemeKararSorgulamaRequest extends BaseMakosRequest {

    @NotNull
    MahkemeKararSorguParam sorguParam;


    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IslenecekIDMahkemeKararRequest is valid");
        ValidationResult validationResult = new ValidationResult(true);

        return validationResult;
    }

}

