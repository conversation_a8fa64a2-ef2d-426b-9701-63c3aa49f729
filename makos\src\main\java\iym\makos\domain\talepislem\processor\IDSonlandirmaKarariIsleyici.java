package iym.makos.domain.talepislem.processor;

import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.HedeflerDetayTalepRepo;
import iym.db.jpa.dao.HedeflerTalepRepo;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class IDSonlandirmaKarariIsleyici extends IDMahkemeKararIsleyiciBase {

    private final HedeflerDetayTalepRepo hedeflerDetayTalepRepo;
    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private final HedeflerTalepRepo hedeflerTalepRepo;

    @Autowired
    public IDSonlandirmaKarariIsleyici(HedeflerDetayTalepRepo hedeflerDetayTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , HedeflerTalepRepo hedeflerTalepRepo
    ) {
        this.hedeflerDetayTalepRepo = hedeflerDetayTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
        this.hedeflerTalepRepo = hedeflerTalepRepo;
    }

    protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {

        try {
            String durum = MahkemeKararTalepIsleyici.toDurum(request.getTalepGuncellemeTuru());
            CommonUtils.safeList(hedeflerDetayTalepRepo.findByMahkemeKararTalepId(request.getMahkemeKararTalepId()))
                    .forEach(hedeflerDetayTalep -> {
                        hedeflerDetayTalep.setDurumu(durum);
                        hedeflerDetayTalepRepo.save(hedeflerDetayTalep);

                        Long detayMahkemeKararId = hedeflerDetayTalep.getMahkemeKararDetayTalepId();

                        Optional<DetayMahkemeKararTalep> mahkemeKararTalepDetayOpt = dMahkemeKararTalepRepo.findById(detayMahkemeKararId);
                        if (mahkemeKararTalepDetayOpt.isPresent()) {
                            DetayMahkemeKararTalep detayMahkemeKararTalep = mahkemeKararTalepDetayOpt.get();
                            detayMahkemeKararTalep.setDurum(durum);
                            dMahkemeKararTalepRepo.save(detayMahkemeKararTalep);
                        }

                    });

            CommonUtils.safeList(hedeflerTalepRepo.findByMahkemeKararTalepId(request.getMahkemeKararTalepId()))
                    .forEach(hedeflerTalep -> {
                        hedeflerTalep.setDurumu(durum);
                        hedeflerTalepRepo.save(hedeflerTalep);
                    });

            return MahkemeKararTalepUpdateResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build();

        } catch (Exception ex) {
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Iliskili veritabanı guncelleme hatası")
                            .build())
                    .build();
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI;
    }

}

