package iym.makos.domain.talepislem.processor;

import iym.common.enums.KararTuru;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.talep.HedeflerAidiyatTalepRepo;
import iym.db.jpa.dao.HedeflerTalepRepo;
import iym.db.jpa.dao.talep.MahkemeAidiyatTalepRepo;
import iym.db.jpa.dao.talep.MahkemeSuclarTalepRepo;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IDYeniKararIsleyici extends IDMahkemeKararIsleyiciBase {

    private final HedeflerTalepRepo hedeflerTalepRepo;
    private final HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo;
    private final MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo;
    private final MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo;

    @Autowired
    public IDYeniKararIsleyici(HedeflerTalepRepo hedeflerTalepRepo
            , HedeflerAidiyatTalepRepo hedeflerAidiyatTalepRepo
            , MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo
            , MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo
    ) {
        this.hedeflerTalepRepo = hedeflerTalepRepo;
        this.hedeflerAidiyatTalepRepo = hedeflerAidiyatTalepRepo;
        this.mahkemeAidiyatTalepRepo = mahkemeAidiyatTalepRepo;
        this.mahkemeSuclarTalepRepo = mahkemeSuclarTalepRepo;
    }

    protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {

        try {
            String durum = MahkemeKararTalepIsleyici.toDurum(request.getTalepGuncellemeTuru());
            CommonUtils.safeList(mahkemeAidiyatTalepRepo.findByMahkemeKararTalepId(request.getMahkemeKararTalepId()))
                    .forEach(mahkemeAidiyatTalep -> {
                        mahkemeAidiyatTalep.setDurumu(durum);
                        mahkemeAidiyatTalepRepo.save(mahkemeAidiyatTalep);
                    });

            CommonUtils.safeList(mahkemeSuclarTalepRepo.findByMahkemeKararTalepId(request.getMahkemeKararTalepId()))
                    .forEach(mahkemeSuclarTalep -> {
                        mahkemeSuclarTalep.setDurumu(durum);
                        mahkemeSuclarTalepRepo.save(mahkemeSuclarTalep);
                    });

            CommonUtils.safeList(hedeflerTalepRepo.findByMahkemeKararTalepId(request.getMahkemeKararTalepId()))
                    .forEach(hedeflerTalep -> {
                        hedeflerTalep.setDurumu(durum);
                        hedeflerTalepRepo.save(hedeflerTalep);

                        CommonUtils.safeList(hedeflerAidiyatTalepRepo.findByHedefTalepId(hedeflerTalep.getId()))
                                .forEach(hedeflerAidiyatTalep -> {
                                    hedeflerAidiyatTalep.setDurumu(durum);
                                });
                    });

            return MahkemeKararTalepUpdateResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build();

        } catch (Exception ex) {
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Iliskili veritabanı guncelleme hatası")
                            .build())
                    .build();
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }

}

