package iym.makos.mapper.islemtablolari;

import iym.common.model.entity.iym.mk.MahkemeKararIslem;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import org.springframework.stereotype.Component;

/**
 * Mapper for MahkemeKararTalep entity and DTO
 */
@Component
public class MahkemeKararIslemMapper {



    public MahkemeKararIslem fromMahkemeKararIslem(MahkemeKararTalep talep){
        if (talep == null) {
            return null;
        }
        //islem talebin id'sini almaktadir.
        return MahkemeKararIslem.builder()
                .id(talep.getId())
                .evrakId(talep.getEvrakId())
                .kullaniciId(talep.getKullaniciId())
                .kayitTarihi(talep.getKayitTarihi())
                .durum(talep.getDurum())
                .hukukBirim(talep.getHukukBirim())
                .kararTip(talep.getKararTip())
                .mahkemeBaslamaTarihi(talep.getMahKararBasTar())
                .mahkemeBitisTarihi(talep.getMahKararBitisTar())
                .mahkemeAdi(talep.getMahkemeAdi())
                .mahkemeKararNo(talep.getMahkemeKararNo())
                .mahkemeIlIlceKodu(talep.getMahkemeIlIlceKodu())
                .aciklama(talep.getAciklama())
                .hakimSicilNo(talep.getHakimSicilNo())
                .sorusturmaNo(talep.getSorusturmaNo())
                .mahkemeKodu(talep.getMahkemeKodu())
                .build();
    }




}
