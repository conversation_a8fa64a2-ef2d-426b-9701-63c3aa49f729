package iym.makos.domain.mahkemekarar.processor;

import iym.common.validation.ValidationResult;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mahkemekarar.dbhandler.MahkemeKararDBSaveHandler;
import iym.makos.model.dto.mahkemekarar.id.IDSonlandirmaKarariRequest;
import iym.makos.model.dto.mahkemekarar.id.IDSonlandirmaKarariResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.validator.IMahkemeKararRequestValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Date;

import static iym.makos.domain.testdata.TestDataBuilder.createTestUser;
import static iym.makos.domain.testdata.TestDataBuilder.createValidIDSonlandirmaKarariRequest;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDSonlandirmaKarariRequestProcessor.
 *
 * Tests the processor implementation for ID sonlandırma kararı requests.
 * Verifies validation, processing, and error handling scenarios.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("IDSonlandirmaKarariRequestProcessor Unit Tests")
class IDSonlandirmaKarariRequestProcessorTest extends BaseDomainUnitTest {
    
    @Mock
    private IMahkemeKararRequestValidator<IDSonlandirmaKarariRequest> mockValidator;
    
    @Mock
    private MahkemeKararDBSaveHandler<IDSonlandirmaKarariRequest> mockSaver;
    
    @InjectMocks
    private IDSonlandirmaKarariRequestProcessor processor;
    
    private IDSonlandirmaKarariRequest testRequest;
    private UserDetailsImpl testUser;
    
    @BeforeEach
    void setUp() {
        testRequest = createValidIDSonlandirmaKarariRequest();
        testUser = createTestUser();
    }
    
    @Test
    @DisplayName("Should process successfully when validation passes and save succeeds")
    void shouldProcessSuccessfully_whenValidationPassesAndSaveSucceeds() throws Exception {
        // Given
        ValidationResult validResult = validResult();
        when(mockValidator.validate(testRequest)).thenReturn(validResult);
        when(mockSaver.kaydet(any(IDSonlandirmaKarariRequest.class), any(Date.class), anyLong()))
                .thenReturn(12345L);
        
        // When
        IDSonlandirmaKarariResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseSuccess(response);
        assertThat(response.getEvrakId()).isEqualTo(12345L);
    }
    
    @Test
    @DisplayName("Should return invalid request response when validation fails")
    void shouldReturnInvalidRequestResponse_whenValidationFails() throws Exception {
        // Given
        ValidationResult invalidResult = new ValidationResult("Validation error");
        when(mockValidator.validate(testRequest)).thenReturn(invalidResult);
        
        // When
        IDSonlandirmaKarariResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).contains("Validation error");
        assertThat(response.getEvrakId()).isNull();
    }
    
    @Test
    @DisplayName("Should return failed response when save throws exception")
    void shouldReturnFailedResponse_whenSaveThrowsException() throws Exception {
        // Given
        ValidationResult validResult = validResult();
        when(mockValidator.validate(testRequest)).thenReturn(validResult);
        when(mockSaver.kaydet(any(IDSonlandirmaKarariRequest.class), any(Date.class), anyLong()))
                .thenThrow(new RuntimeException("Database error"));
        
        // When
        IDSonlandirmaKarariResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertResponseError(response, MakosResponseCode.FAILED);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("INTERNAL ERROR");
        assertThat(response.getEvrakId()).isNull();
    }
    
    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() {
        // When
        Class<IDSonlandirmaKarariRequest> requestType = processor.getRelatedRequestType();
        
        // Then
        assertThat(requestType).isEqualTo(IDSonlandirmaKarariRequest.class);
    }
    
    @Test
    @DisplayName("Should return correct response type")
    void shouldReturnCorrectResponseType() {
        // When
        Class<IDSonlandirmaKarariResponse> responseType = processor.getRelatedResponseType();
        
        // Then
        assertThat(responseType).isEqualTo(IDSonlandirmaKarariResponse.class);
    }
    
    @Test
    @DisplayName("Should handle null request gracefully")
    void shouldHandleNullRequest_gracefully() {
        // When
        IDSonlandirmaKarariResponse response = processor.process(null, testUser);

        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("Request cannot be null");
    }
    
    @Test
    @DisplayName("Should handle null user gracefully")
    void shouldHandleNullUser_gracefully() throws Exception {
        // When
        IDSonlandirmaKarariResponse response = processor.process(testRequest, null);

        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("User cannot be null");
    }
}
