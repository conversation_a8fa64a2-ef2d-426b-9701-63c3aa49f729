package iym.makos.validator;


import iym.common.enums.KararTuru;
import iym.makos.model.dto.mahkemekarar.MahkemeKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class MahkemeKararRequestValidatorFactory {

    private final Map<Class<? extends MahkemeKararRequest>, IMahkemeKararRequestValidator<?>> validatorsByRequest = new HashMap<>();
    private final Map<KararTuru, IMahkemeKararRequestValidator<?>> validatorsByKararTuru = new HashMap<>();

    @Autowired
    public MahkemeKararRequestValidatorFactory(List<IMahkemeKararRequestValidator<?>> validators) {
        for (IMahkemeKararRequestValidator<?> validator : validators) {
            validatorsByRequest.put(validator.getRelatedRequestType(), validator);
            validatorsByKararTuru.put(validator.getRelatedKararTuru(), validator);
        }
    }

    public IMahkemeKararRequestValidator<?> getValidator(KararTuru kararTuru) {
        return validatorsByKararTuru.get(kararTuru);
    }

    @SuppressWarnings("unchecked")
    public <T extends MahkemeKararRequest> IMahkemeKararRequestValidator<T> getValidator(Class<T> requestType) {
        return (IMahkemeKararRequestValidator<T>) validatorsByRequest.get(requestType);
    }

}