package iym.spring.db.loader;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableTransactionManagement
@ComponentScan({"iym.db"})
@EntityScan(basePackages={"iym.common.model.entity"})
@EnableJpaRepositories(basePackages={"iym.db"})
@Slf4j
public class DbLoader {

    public DbLoader(){
        log.info("DbLoader initializing");
    }
}
