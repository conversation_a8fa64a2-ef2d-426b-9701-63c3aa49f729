package iym.makos.controller;

import iym.common.enums.ResponseCode;
import iym.common.model.api.ApiResponse;
import iym.makos.model.dto.db.*;
import iym.makos.model.dto.sorgu.*;
import iym.makos.service.db.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/iymSorgu")
@Slf4j
public class IymSorguController {

    @Autowired
    private SucTipiService sucTipiService;

    @Autowired
    private MahkemeKararTipiService mahkemeKararTipiService;

    @Autowired
    private MahkemeBilgiService mahkemeBilgiService;

    @Autowired
    TespitTurleriService tespitTurleriService;

    @Autowired
    SorguTipleriService sorguTipleriService;


    @GetMapping("sucTipleri")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<SucTipleriResponse> sucTipleri() {

        log.debug("sucTipleri sorgulama request received.");

        try {

            List<SucTipiDTO> sucTipleri = sucTipiService.findByDurum("A");
            log.info("sucTipleri sorgulandi. Size {}", sucTipleri.size());

            return ResponseEntity.ok(SucTipleriResponse.builder()
                    .sucTipleri(sucTipleri)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Suç Tipleri Sorgulama Başarılı")
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("sucTipleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(SucTipleriResponse.builder()
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Suç Tipleri Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }

    @GetMapping("mahkemeKararTipleri")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<MahkemeKararTipleriResponse> mahkemeKararTipleri() {

        log.debug("Mahkeme Karar Tipleri Sorgulama  Request Received.");

        try {

            List<MahkemeKararTipiDTO> mahkemeKararTipleri = mahkemeKararTipiService.findAll();

            log.info("Mahkeme Karar Tipleri Sorgulandı. Size {}", mahkemeKararTipleri.size());

            return ResponseEntity.ok(MahkemeKararTipleriResponse.builder()
                    .mahkemeKararTipiListesi(mahkemeKararTipleri)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Suç Tipleri Sorgulama Başarılı")
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("sucTipleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(MahkemeKararTipleriResponse.builder()
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Suç Tipleri Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }


    @GetMapping("mahkemeKodlari")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<MahkemeKodlariResponse> mahkemeKodlari() {

        log.debug("Mahkeme Kodu Listesi Request Received.");

        try {

            List<MahkemeKoduDTO> mahkemeKoduListesi = mahkemeBilgiService.findAllMahkemeKodlari();

            log.info("Mahkeme Kodu Listesi Sorgulandı. Size {}", mahkemeKoduListesi.size());

            return ResponseEntity.ok(MahkemeKodlariResponse.builder()
                    .mahkemeKodListesi(mahkemeKoduListesi)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Mahkeme Kodu Listesi Sorgulama Başarılı")
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("sucTipleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(MahkemeKodlariResponse.builder()
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Mahkeme Kodu Listesi Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }


/*
    @GetMapping("hedefTipleri")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<HedefTipleriResponse> hedefTipleri() {

        log.debug("hedefTipleri sorgulama request received.");

        try {

            List<HedefTipi> hedefTipleri = new ArrayList<>(); //
            log.info("hedefTipleri sorgulandi. Size {}", hedefTipleri.size());

            return ResponseEntity.ok(HedefTipleriResponse.builder()
                    .hedefTipleri(hedefTipleri)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Hedef Tipleri Sorgulama Başarılı")
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("hedefTipleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(HedefTipleriResponse.builder()
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Hedef Tipleri Sorgulama Başarısız")
                            .build())
                    .build());
        }
    }
*/
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<SorguTipiListResponse> sorguTipleri() {

        log.debug("sorguTipleri sorgulama request received.");

        try {

            List<SorguTipiDTO> sorguTipleri = sorguTipleriService.findAllSorguTipleri();

            log.info("sorguTipleri sorgulandi. Size {}", sorguTipleri.size());

            return ResponseEntity.ok(SorguTipiListResponse.builder()
                    .sorguTipleri(sorguTipleri)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Sorgu Tipleri Sorgulama Başarılı")
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("sorguTipleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(SorguTipiListResponse.builder()
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Sorgu Tipleri Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }

    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_KULLANICI', 'KURUM_TEMSILCISI')")
    public ResponseEntity<TespitTuruListResponse> tespitTurleri() {

        log.debug("tespitTurleri sorgulama request received.");

        try {

            List<TespitTuruDTO> tespitTurleri = tespitTurleriService.findAllTespitTurleri();

            log.info("tespitTurleri sorgulandi. Size {}", tespitTurleri.size());

            return ResponseEntity.ok(TespitTuruListResponse.builder()
                    .tespitTurleri(tespitTurleri)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Tespit Turleri Sorgulama Başarılı")
                            .build())
                    .build());

        } catch (Exception ex) {
            log.error("tespitTurleri sorgulama hatası : ", ex);
            return ResponseEntity.ok(TespitTuruListResponse.builder()
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Tespit Turleri Sorgulama Başarısız")
                            .build())
                    .build());
        }

    }



}
