package iym.makos.model.dto.auth;

import iym.common.model.api.ApiResponseBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * Change password response DTO for MAKOS authentication
 */
@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class ChangePasswordResponse extends ApiResponseBase {
    // Add any additional fields if needed
} 