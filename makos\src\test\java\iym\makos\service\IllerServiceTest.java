package iym.makos.service;

import iym.common.model.entity.iym.Iller;
import iym.common.service.db.DbIllerService;
import iym.makos.mapper.IllerMapper;
import iym.makos.service.db.IllerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.model.dto.db.IllerDTO;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IllerServiceTest {

    @Mock
    private DbIllerService dbIllerService;

    @Mock
    private IllerMapper illerMapper;

    @InjectMocks
    private IllerService illerService;

    private Iller iller;
    private IllerDTO illerDTO;
    private List<Iller> illerList;
    private List<IllerDTO> illerDTOList;

    @BeforeEach
    void setUp() {
        iller = Iller.builder()
                .ilKod("0600")
                .ilAdi("ANKARA")
                .ilceAdi("MERKEZ")
                .build();

        Iller iller2 = Iller.builder()
                .ilKod("3400")
                .ilAdi("İSTANBUL")
                .ilceAdi("MERKEZ")
                .build();

        illerDTO = IllerDTO.builder()
                .ilKod("0600")
                .ilAdi("ANKARA")
                .ilceAdi("MERKEZ")
                .build();

        IllerDTO illerDTO2 = IllerDTO.builder()
                .ilKod("3400")
                .ilAdi("İSTANBUL")
                .ilceAdi("MERKEZ")
                .build();

        illerList = Arrays.asList(iller, iller2);
        illerDTOList = Arrays.asList(illerDTO, illerDTO2);
    }

    @Test
    void findAll_shouldReturnAllIller() {
        // Given
        when(dbIllerService.findAll()).thenReturn(illerList);
        when(illerMapper.toDtoList(illerList)).thenReturn(illerDTOList);

        // When
        List<IllerDTO> result = illerService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(illerDTOList);
        verify(dbIllerService).findAll();
        verify(illerMapper).toDtoList(illerList);
    }

    @Test
    void findAllOrderedByIlAdi_shouldReturnAllIllerOrderedByIlAdi() {
        // Given
        when(dbIllerService.findAllByOrderByIlAdiAsc()).thenReturn(illerList);
        when(illerMapper.toDtoList(illerList)).thenReturn(illerDTOList);

        // When
        List<IllerDTO> result = illerService.findAllOrderedByIlAdi();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(illerDTOList);
        verify(dbIllerService).findAllByOrderByIlAdiAsc();
        verify(illerMapper).toDtoList(illerList);
    }

    @Test
    void findAllOrderedByIlKod_shouldReturnAllIllerOrderedByIlKod() {
        // Given
        when(dbIllerService.findAllByOrderByIlKodAsc()).thenReturn(illerList);
        when(illerMapper.toDtoList(illerList)).thenReturn(illerDTOList);

        // When
        List<IllerDTO> result = illerService.findAllOrderedByIlKod();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(illerDTOList);
        verify(dbIllerService).findAllByOrderByIlKodAsc();
        verify(illerMapper).toDtoList(illerList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfIller() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<Iller> illerPage = new PageImpl<>(illerList, pageable, illerList.size());
        
        when(dbIllerService.findAll(pageable)).thenReturn(illerPage);
        when(illerMapper.toDtoList(illerList)).thenReturn(illerDTOList);

        // When
        Page<IllerDTO> result = illerService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(illerDTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbIllerService).findAll(pageable);
        verify(illerMapper).toDtoList(illerList);
    }

    @Test
    void findById_shouldReturnIller_whenExists() {
        // Given
        when(dbIllerService.findById("0600")).thenReturn(Optional.of(iller));
        when(illerMapper.toDto(iller)).thenReturn(illerDTO);

        // When
        IllerDTO result = illerService.findById("0600");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(illerDTO);
        verify(dbIllerService).findById("0600");
        verify(illerMapper).toDto(iller);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        when(dbIllerService.findById("0600")).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> illerService.findById("0600"));
        verify(dbIllerService).findById("0600");
        verify(illerMapper, never()).toDto(any());
    }

    @Test
    void create_shouldCreateIller() {
        // Given
        when(dbIllerService.existsById("0600")).thenReturn(false);
        when(dbIllerService.existsByIlAdiAndIlceAdi("ANKARA", "MERKEZ")).thenReturn(false);
        when(illerMapper.toEntity(illerDTO)).thenReturn(iller);
        when(illerMapper.toDto(iller)).thenReturn(illerDTO);

        // When
        IllerDTO result = illerService.create(illerDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(illerDTO);
        verify(dbIllerService).existsById("0600");
        verify(dbIllerService).existsByIlAdiAndIlceAdi("ANKARA", "MERKEZ");
        verify(illerMapper).toEntity(illerDTO);
        verify(dbIllerService).save(iller);
        verify(illerMapper).toDto(iller);
    }

    @Test
    void create_shouldThrowException_whenIlKodAlreadyExists() {
        // Given
        when(dbIllerService.existsById("0600")).thenReturn(true);

        // When/Then
        assertThrows(ResponseStatusException.class, () -> illerService.create(illerDTO));
        verify(dbIllerService).existsById("0600");
        verify(illerMapper, never()).toEntity(any());
        verify(dbIllerService, never()).save(any());
        verify(illerMapper, never()).toDto(any());
    }

    @Test
    void create_shouldThrowException_whenIlAdiAndIlceAdiAlreadyExists() {
        // Given
        when(dbIllerService.existsById("0600")).thenReturn(false);
        when(dbIllerService.existsByIlAdiAndIlceAdi("ANKARA", "MERKEZ")).thenReturn(true);

        // When/Then
        assertThrows(ResponseStatusException.class, () -> illerService.create(illerDTO));
        verify(dbIllerService).existsById("0600");
        verify(dbIllerService).existsByIlAdiAndIlceAdi("ANKARA", "MERKEZ");
        verify(illerMapper, never()).toEntity(any());
        verify(dbIllerService, never()).save(any());
        verify(illerMapper, never()).toDto(any());
    }

    @Test
    void update_shouldUpdateIller() {
        // Given
        when(dbIllerService.findById("0600")).thenReturn(Optional.of(iller));
        when(dbIllerService.findByIlAdiAndIlceAdi("ANKARA", "ÇANKAYA")).thenReturn(Arrays.asList());
        when(illerMapper.updateEntityFromDto(iller, illerDTO)).thenReturn(iller);
        when(illerMapper.toDto(iller)).thenReturn(illerDTO);

        // Update ilçe adı
        illerDTO.setIlceAdi("ÇANKAYA");

        // When
        IllerDTO result = illerService.update("0600", illerDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(illerDTO);
        verify(dbIllerService).findById("0600");
        verify(dbIllerService).findByIlAdiAndIlceAdi("ANKARA", "ÇANKAYA");
        verify(illerMapper).updateEntityFromDto(iller, illerDTO);
        verify(dbIllerService).update(iller);
        verify(illerMapper).toDto(iller);
    }

    @Test
    void update_shouldThrowException_whenIllerNotExists() {
        // Given
        when(dbIllerService.findById("0600")).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> illerService.update("0600", illerDTO));
        verify(dbIllerService).findById("0600");
        verify(illerMapper, never()).updateEntityFromDto(any(), any());
        verify(dbIllerService, never()).update(any());
        verify(illerMapper, never()).toDto(any());
    }

    @Test
    void delete_shouldDeleteIller() {
        // Given
        when(dbIllerService.findById("0600")).thenReturn(Optional.of(iller));

        // When
        illerService.delete("0600");

        // Then
        verify(dbIllerService).findById("0600");
        verify(dbIllerService).delete(iller);
    }

    @Test
    void delete_shouldThrowException_whenIllerNotExists() {
        // Given
        when(dbIllerService.findById("0600")).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> illerService.delete("0600"));
        verify(dbIllerService).findById("0600");
        verify(dbIllerService, never()).delete(any());
    }
}
