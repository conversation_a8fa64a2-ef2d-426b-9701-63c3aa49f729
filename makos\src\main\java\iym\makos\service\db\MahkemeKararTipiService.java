package iym.makos.service.db;

import iym.common.model.entity.iym.MahkemeKararTipleri;
import iym.common.service.db.DbMahkemeKararTipleriService;
import iym.makos.mapper.MahkemeKararTipiMapper;
import iym.makos.model.dto.db.MahkemeKararTipiDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;


@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeKararTipiService {

    private final DbMahkemeKararTipleriService dbMahkemeKararTipleriService;
    private final MahkemeKararTipiMapper mahkemeKararTipiMapper;


    public MahkemeKararTipiDTO getMahkemeKararTipiByKdu(String kararKodu){
        return dbMahkemeKararTipleriService.findByKararKodu(kararKodu)
                .map(mahkemeKararTipiMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme karar tipi bilgisi bulunamadı"));

    }

    public List<MahkemeKararTipiDTO> findAll(){
        List<MahkemeKararTipleri> mahkemeKararTipleri = dbMahkemeKararTipleriService.findAll();
        return mahkemeKararTipiMapper.toDtoList(mahkemeKararTipleri);
    }



}
