# MAKOS Domain Unit Tests

Bu dizin MAKOS domain katmanının unit testlerini içerir. Domain katmanı mahkeme kararı işlemlerini **Strategy Pattern**, **Factory Pattern** ve **Template Method Pattern** kullanarak yönetir.

## Test Yapısı

### Base Classes
- **BaseDomainUnitTest**: Tüm domain unit testleri için temel sınıf
  - Mockito extension sağlar
  - Test profili aktivasyonu
  - Ortak test yardımcı metodları

### Test Data Builders
- **TestDataBuilder**: Test verisi oluşturma yardımcı sınıfı
  - Geçerli test nesneleri oluşturur
  - Builder pattern desteği
  - Varsayılan değerler sağlar

### Test Utilities
- **TestAssertions**: Domain-specific assertion metodları
  - ValidationResult assertions
  - Response assertions
  - Type assertions

## Test Kategorileri

### 1. Factory Tests
- **ProcessorFactory**: Processor seçimi testleri
- **MahkemeKararRequestValidatorFactory**: Validator seçimi testleri
- **MahkemeKararRequestDBSaveHandlerFactory**: DB handler seçimi testleri

### 2. Processor Tests
- **GenelKararRequestProcessor**: Genel evrak işleme testleri
- **IDSonlandirmaKarariRequestProcessor**: ID sonlandırma işleme testleri
- Diğer processor implementasyonları

### 3. Validator Tests
- **IDYeniKararValidator**: ID yeni karar validasyon testleri
- **IDUzatmaKarariValidator**: ID uzatma validasyon testleri
- **MahkemeKararRequestCommonValidator**: Ortak validasyon testleri
- Diğer validator implementasyonları

### 4. DB Handler Tests
- **GenelEvrakDBSaveHandler**: Genel evrak kaydetme testleri
- **IDUzatmaKarariDBSaveHandler**: ID uzatma kaydetme testleri
- Diğer DB handler implementasyonları

## Test Çalıştırma

### Tüm Unit Testler
```bash
mvn test -Dspring.profiles.active=test
```

### Belirli Test Sınıfı
```bash
mvn test -Dtest=ProcessorFactoryTest -Dspring.profiles.active=test
```

### Belirli Test Paketi
```bash
mvn test -Dtest="iym.makos.domain.mahkemekarar.processor.*Test" -Dspring.profiles.active=test
```

## Test Yazma Kuralları

### 1. Naming Convention
- Test sınıfları: `{ClassName}Test`
- Test metodları: `should{ExpectedBehavior}_when{StateUnderTest}`

### 2. Test Structure (Given-When-Then)
```java
@Test
@DisplayName("Should return success response when validation passes")
void shouldReturnSuccessResponse_whenValidationPasses() {
    // Given
    ValidationResult validResult = ValidationResult.valid();
    when(mockValidator.validate(request)).thenReturn(validResult);
    
    // When
    Response response = processor.process(request, user);
    
    // Then
    assertResponseSuccess(response);
}
```

### 3. Mock Usage
- Sadece gerekli dependency'leri mock'layın
- `@Mock` annotation kullanın
- `@InjectMocks` ile test edilen sınıfı inject edin

### 4. Test Data
- `TestDataBuilder` sınıfını kullanın
- Geçerli test verisi için factory metodları
- Builder pattern ile özelleştirme

### 5. Assertions
- `TestAssertions` sınıfındaki domain-specific metodları kullanın
- AssertJ ile kombine edin
- Anlamlı hata mesajları sağlayın

## Test Coverage

### Hedef Coverage
- Line Coverage: %90+
- Branch Coverage: %85+
- Method Coverage: %95+

### Coverage Raporu
```bash
mvn jacoco:report
```

## Best Practices

### 1. Test Yazma İlkeleri
- **Test Isolation**: Her test bağımsız olmalı, diğer testlerden etkilenmemeli
- **Fast Tests**: Unit testler hızlı çalışmalı (< 100ms per test)
- **Readable Tests**: Test kodu production kodu kadar okunabilir olmalı
- **Maintainable Tests**: Bakımı kolay testler yazın, DRY prensibini uygulayın
- **Deterministic Tests**: Testler her çalıştırıldığında aynı sonucu vermeli

### 2. Test Kapsamı
- **Happy Path**: Normal akış senaryolarını test edin
- **Edge Cases**: Sınır durumları ve corner case'leri test edin
- **Error Scenarios**: Hata senaryolarını ve exception handling'i test edin
- **Null Safety**: Null değerler için güvenlik testleri yapın
- **Validation Rules**: Tüm validation kurallarını test edin

### 3. Mock Kullanımı
- **Minimal Mocking**: Sadece gerekli dependency'leri mock'layın
- **Behavior Verification**: Mock'ların doğru çağrıldığını doğrulayın
- **State Verification**: Sonuç durumunu kontrol edin
- **Stub vs Mock**: Stub için when(), Mock için verify() kullanın

### 4. Test Data Management
- **Builder Pattern**: TestDataBuilder sınıfını kullanın
- **Valid Defaults**: Geçerli varsayılan değerler sağlayın
- **Customization**: Builder pattern ile özelleştirme imkanı sunun
- **Realistic Data**: Gerçekçi test verileri kullanın

## Test Implementasyon Durumu

### ✅ Tamamlanmış Testler (469 Tests - %99.4 Success Rate)

#### Validator Tests (121 Tests)
- **IDUzatmaKarariValidatorTest**: 23 tests - ID uzatma kararı validation testleri ✅
- **IDSonlandirmaKarariValidatorTest**: 21 tests - ID sonlandırma kararı validation testleri ✅
- **ITKararValidatorTest**: 11 tests - İletişimin tespiti kararı validation testleri ✅
- **IDHedefGuncellemeValidatorTest**: 10 tests - ID hedef güncelleme validation testleri ✅
- **IDAidiyatBilgisiGuncellemeValidatorTest**: 8 tests - ID aidiyet güncelleme validation testleri ✅
- **IDSucTipiGuncellemeValidatorTest**: 9 tests - ID suç tipi güncelleme validation testleri ✅
- **IDMahkemeKararGuncellemeValidatorTest**: 9 tests - ID mahkeme kararı güncelleme validation testleri ✅
- **IDYeniKararValidatorTest**: ID yeni karar validation testleri ✅
- **MahkemeKararRequestCommonValidatorTest**: 11 tests - Ortak validation testleri ✅
- **MahkemeKararRequestValidatorBaseTest**: 9 tests - Base validator testleri ✅
- **MahkemeKararRequestValidatorFactoryTest**: 7 tests - Validator factory testleri ✅

#### Processor Tests (21 Tests)
- **ProcessorFactoryTest**: Factory pattern processor seçimi testleri ✅
- **GenelKararRequestProcessorTest**: Genel evrak işleme processor testleri ✅
- **IDYeniKararRequestProcessorTest**: 7 tests - ID yeni karar processor testleri ✅
- **IDUzatmaKarariRequestProcessorTest**: 7 tests - ID uzatma kararı processor testleri ✅
- **IDSonlandirmaKarariRequestProcessorTest**: 7 tests - ID sonlandırma kararı processor testleri ✅

#### DB Handler Tests
- **GenelEvrakDBSaveHandlerTest**: Genel evrak DB kaydetme testleri ✅
- **IDUzatmaKarariDBSaveHandler**: Mevcut implementasyon ile test ediliyor ✅
- **IDSonlandirmaKarariDBSaveHandler**: Mevcut implementasyon ile test ediliyor ✅
- **ITKararDBSaveHandler**: Mevcut implementasyon ile test ediliyor ✅

#### Infrastructure Tests
- **BaseDomainUnitTest**: Test altyapısı base sınıfı ✅
- **TestDataBuilder**: Kapsamlı test data builder pattern ✅
- **TestAssertions**: Domain-specific assertion metodları ✅

Bu implementasyonları referans alarak diğer domain sınıflarının testlerini yazabilirsiniz.

## Katkıda Bulunma

1. Yeni test yazarken mevcut pattern'leri takip edin
2. Test data builder'ları kullanın
3. Domain-specific assertion'ları kullanın
4. Test coverage'ı kontrol edin
5. Code review sürecine katılın
