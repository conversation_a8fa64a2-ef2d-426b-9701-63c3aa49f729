package iym.makos.validator;

import iym.common.enums.KararTuru;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.model.dto.mahkemekarar.id.GenelEvrakRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static iym.makos.domain.testdata.TestDataBuilder.createValidGenelEvrakRequest;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for MahkemeKararRequestValidatorBase.
 * <p>
 * Tests the base validation logic shared across all mahkeme karar validators.
 * Uses a concrete implementation (TestValidator) to test the abstract base class.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("MahkemeKararRequestValidatorBase Unit Tests")
class MahkemeKararRequestValidatorBaseTest extends BaseDomainUnitTest {

    @Mock
    private MahkemeKararRequestCommonValidator mockCommonValidator;

    private TestValidator validator;
    private GenelEvrakRequest validRequest;

    @BeforeEach
    void setUp() {
        validator = new TestValidator();
        validator.setMahkemeKararRequestCommonValidator(mockCommonValidator);
        validRequest = createValidGenelEvrakRequest();
        setupValidMocks();
    }

    private void setupValidMocks() {
        // Mock common validator to return valid result
        when(mockCommonValidator.validate(any(GenelEvrakRequest.class)))
                .thenReturn(new ValidationResult(true));
    }

    @Test
    @DisplayName("Should pass validation when all validations succeed")
    void shouldPassValidation_whenAllValidationsSucceed() {
        // Given
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationSuccess(result);
        assertThat(validator.doValidateCalled).isTrue();
    }

    @Test
    @DisplayName("Should return early when request.isValid() fails")
    void shouldReturnEarly_whenRequestIsValidFails() {
        // Given - Create request with wrong karar turu to make isValid() fail
        validRequest.setKararTuru(KararTuru.ILETISIMIN_TESPITI);

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
                reason.contains("Karar türü: " + KararTuru.GENEL_EVRAK.name() + " olmalıdır"));
        
        // Verify that common validator and doValidate were never called
        verify(mockCommonValidator, never()).validate(any());
        assertThat(validator.doValidateCalled).isFalse();
    }

    @Test
    @DisplayName("Should return early when common validation fails")
    void shouldReturnEarly_whenCommonValidationFails() {
        // Given - Mock common validator to return invalid result
        when(mockCommonValidator.validate(any(GenelEvrakRequest.class)))
                .thenReturn(new ValidationResult("Common validation failed"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Common validation failed");
        
        // Verify that doValidate was never called
        assertThat(validator.doValidateCalled).isFalse();
    }

    @Test
    @DisplayName("Should handle validation exception gracefully")
    void shouldHandleValidationException_gracefully() {
        // Given - Mock common validator to throw exception
        when(mockCommonValidator.validate(any(GenelEvrakRequest.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should handle null request gracefully")
    void shouldHandleNullRequest_gracefully() {
        // When
        ValidationResult result = validator.validate(null);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() {
        // When
        Class<GenelEvrakRequest> requestType = validator.getRelatedRequestType();

        // Then
        assertEquals(GenelEvrakRequest.class, requestType);
    }

    @Test
    @DisplayName("Should call doValidate when all previous validations pass")
    void shouldCallDoValidate_whenAllPreviousValidationsPass() {
        // Given
        setupValidMocks();
        validator.doValidateResult = new ValidationResult("Custom validation failed");

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Custom validation failed");
        assertThat(validator.doValidateCalled).isTrue();
    }

    @Test
    @DisplayName("Should handle doValidate exception gracefully")
    void shouldHandleDoValidateException_gracefully() {
        // Given
        setupValidMocks();
        validator.shouldThrowException = true;

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should maintain validation flow order")
    void shouldMaintainValidationFlowOrder() {
        // Given
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationSuccess(result);
        
        // Verify the validation flow order
        verify(mockCommonValidator).validate(validRequest);
        assertThat(validator.doValidateCalled).isTrue();
    }

    /**
     * Concrete test implementation of MahkemeKararRequestValidatorBase
     * for testing the abstract base class functionality.
     */
    private static class TestValidator extends MahkemeKararRequestValidatorBase<GenelEvrakRequest> {
        
        boolean doValidateCalled = false;
        boolean shouldThrowException = false;
        ValidationResult doValidateResult = new ValidationResult(true);

        @Override
        protected ValidationResult doValidate(GenelEvrakRequest request) {
            doValidateCalled = true;
            
            if (shouldThrowException) {
                throw new RuntimeException("Test exception in doValidate");
            }
            
            return doValidateResult;
        }

        @Override
        public KararTuru getRelatedKararTuru() {
            return KararTuru.GENEL_EVRAK;
        }
    }
}
