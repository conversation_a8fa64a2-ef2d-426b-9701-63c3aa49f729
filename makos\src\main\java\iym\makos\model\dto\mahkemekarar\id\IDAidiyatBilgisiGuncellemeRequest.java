package iym.makos.model.dto.mahkemekarar.id;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.AidiyatGuncellemeDetay;
import iym.makos.model.api.AidiyatGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mahkemekarar.MahkemeKararRequest;
import iym.makos.validator.custom.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDAidiyatBilgisiGuncellemeRequest extends MahkemeKararRequest {

    @NotNull
    @Valid
    @Size(min = 1)
    @Schema(description = "Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek aidiyat bilgileri")
    private List<AidiyatGuncellemeKararDetay> aidiyatGuncellemeKararDetayListesi;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if AidiyatBilgisiGuncellemeRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME.name() + " olmalıdır");
                return validationResult;
            }

            MahkemeKararTip kararTip = mahkemeKararBilgisi.getMahkemeKararTipi();
            if (kararTip != MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME) {
                validationResult.addFailedReason("Mahkeme karar Tipi " + MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME.name() + " olmalıdır");
            }

            if (aidiyatGuncellemeKararDetayListesi == null || aidiyatGuncellemeKararDetayListesi.isEmpty()) {
                validationResult.addFailedReason("Güncellemeye konu olan en az bir detay girilmelidir!");
            } else {
                for (AidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetay : aidiyatGuncellemeKararDetayListesi) {

                    MahkemeKararDetay iliskiliMahkemeKararDetay = aidiyatGuncellemeKararDetay.getMahkemeKararDetay();
                    if (iliskiliMahkemeKararDetay == null) {
                        validationResult.addFailedReason("Güncellemeye konu mahkeme karar bilgileri boş olamaz.!");
                    }
                    List<AidiyatGuncellemeDetay> guncellemeListesi = aidiyatGuncellemeKararDetay.getAidiyatGuncellemeDetayListesi();
                    if (guncellemeListesi == null || guncellemeListesi.isEmpty()) {
                        validationResult.addFailedReason("Güncellenecek aidiyat listesi boş olamaz.!");
                    }
                }
            }

            return validationResult;

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME;
    }
}

