package iym.makos.domain.talepislem.idislemeaktar;

import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.mk.DetayMahkemeKararIslem;
import iym.common.model.entity.iym.mk.MahkemeAidiyatDetayIslem;
import iym.common.util.ApplicationConstants;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.mk.DetayMahkemeKararIslemRepo;
import iym.db.jpa.dao.mk.MahkemeAidiyatDetayIslemRepo;
import iym.db.jpa.dao.talep.MahkemeAidiyatDetayTalepRepo;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.makos.mapper.islemtablolari.DetayMahkemeKararIslemMapper;
import iym.makos.mapper.islemtablolari.MahkemeAidiyatDetayIslemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IDAidiyatBilgisiGuncellemeIslemeAktarici extends IDMahkemeKararTalepIslemeAktariciBase {

    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;
    private final MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo;

    private final DetayMahkemeKararIslemRepo detayMahkemeKararIslemRepo;
    private final DetayMahkemeKararIslemMapper detayMahkemeKararIslemMapper;
    private final MahkemeAidiyatDetayIslemRepo mahkemeAidiyatDetayIslemRepo;
    private final MahkemeAidiyatDetayIslemMapper mahkemeAidiyatDetayIslemMapper;

    @Autowired
    public IDAidiyatBilgisiGuncellemeIslemeAktarici(MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
            , DetayMahkemeKararIslemRepo detayMahkemeKararIslemRepo
            , DetayMahkemeKararIslemMapper detayMahkemeKararIslemMapper
            , MahkemeAidiyatDetayIslemRepo mahkemeAidiyatDetayIslemRepo
            , MahkemeAidiyatDetayIslemMapper mahkemeAidiyatDetayIslemMapper
    ) {
        this.mahkemeAidiyatDetayTalepRepo = mahkemeAidiyatDetayTalepRepo;
        this.detayMahkemeKararIslemMapper = detayMahkemeKararIslemMapper;
        this.detayMahkemeKararIslemRepo = detayMahkemeKararIslemRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
        this.mahkemeAidiyatDetayIslemRepo = mahkemeAidiyatDetayIslemRepo;
        this.mahkemeAidiyatDetayIslemMapper = mahkemeAidiyatDetayIslemMapper;
    }

    protected boolean updateRelatedTables(Long mahmekeKararTalepId) {
        boolean result = false;
        try {
            String durum = ApplicationConstants.MKTALEP_DURUM_ISLEMDE;
            CommonUtils.safeList(dMahkemeKararTalepRepo.findByMahkemeKararTalepId(mahmekeKararTalepId))
                    .forEach(dMahkemeKararTalep -> {
                        dMahkemeKararTalep.setDurum(durum);
                        dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                        DetayMahkemeKararIslem detayMahkemeKararIslem = detayMahkemeKararIslemMapper.fromDetayMahkemeKararTalep(dMahkemeKararTalep);
                        DetayMahkemeKararIslem savedDetayMahkemeKararIslem = detayMahkemeKararIslemRepo.save(detayMahkemeKararIslem);

                        CommonUtils.safeList(mahkemeAidiyatDetayTalepRepo.findByMahkemeKararDetayTalepId(dMahkemeKararTalep.getId()))
                                .forEach(mahkemeAidiyatDetayTalep -> {
                                    mahkemeAidiyatDetayTalep.setDurum(durum);
                                    mahkemeAidiyatDetayTalepRepo.save(mahkemeAidiyatDetayTalep);

                                    MahkemeAidiyatDetayIslem mahkemeAidiyatDetayIslem = mahkemeAidiyatDetayIslemMapper.fromMahkemeAidiyatDetayTalep(mahkemeAidiyatDetayTalep);
                                    mahkemeAidiyatDetayIslem.setDetayMahkemeKararIslemId(savedDetayMahkemeKararIslem.getId());
                                    mahkemeAidiyatDetayIslem.setDurum(durum);
                                    mahkemeAidiyatDetayIslemRepo.save(mahkemeAidiyatDetayIslem);

                                });
                    });

            result = true;

        } catch (Exception ex) {
            log.error("IDAidiyatBilgisiGuncellemeIslemeAktarici process failed, mahkemeKararTalepId:{}", mahmekeKararTalepId, ex);
        }
        return result;
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME;
    }

}

