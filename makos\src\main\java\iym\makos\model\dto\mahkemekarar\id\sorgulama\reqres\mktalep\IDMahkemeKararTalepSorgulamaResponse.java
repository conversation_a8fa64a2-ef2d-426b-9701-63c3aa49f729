package iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mktalep;

import iym.common.model.api.ApiResponseBase;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mktalep.MahkemeKararTalepSorguView;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class IDMahkemeKararTalepSorgulamaResponse extends ApiResponseBase {
    private List<MahkemeKararTalepSorguView> mahkemeKararTalepSorguViewListesi;
} 