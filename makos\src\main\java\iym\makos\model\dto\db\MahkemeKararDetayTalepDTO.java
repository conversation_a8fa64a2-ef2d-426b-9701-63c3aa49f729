package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for DMahkemeKararTalep entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "DMahkeme Karar Talep bilgilerini içerir")
public class MahkemeKararDetayTalepDTO {

    @Schema(description = "Mahkeme karar talep ID")
    private Long id;



    @Schema(description = "Mahkeme Karar Talep ID")
    @NotNull(message = "Mahkeme Karar Talep ID boş olamaz")
    private Long mahkemeKararId;

    @Schema(description = "Evrak ID")
    @NotNull(message = "<PERSON>vrak ID boş olamaz")
    private Long evrakId;

    @Schema(description = "<PERSON><PERSON>ıcı ID")
    @NotNull(message = "<PERSON>llanıcı ID boş olamaz")
    private Long kullaniciId;


    private Date kayitTarihi;


    private String durum;


    private Long iliskiliMahkemeKararId;


    private String kararTipDetay;


    private String mahkemeAdiDetay;


    private String mahkemeKararNoDetay;

    private String sorusturmaNoDetay;


    private String mahkemeIlIlceKoduDetay;


    private String mahkemeKoduDetay;


    private String aciklama;



}
