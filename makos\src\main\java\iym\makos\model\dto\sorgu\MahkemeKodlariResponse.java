package iym.makos.model.dto.sorgu;

import iym.common.model.api.ApiResponseBase;
import iym.makos.model.dto.db.MahkemeDTO;
import iym.makos.model.dto.db.MahkemeKoduDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class MahkemeKodlariResponse extends ApiResponseBase {
    private List<MahkemeKoduDTO> mahkemeKodListesi;
}
