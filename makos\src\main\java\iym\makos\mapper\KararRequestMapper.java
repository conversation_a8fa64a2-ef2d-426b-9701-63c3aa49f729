package iym.makos.mapper;

import iym.common.model.api.HedefWithAdSoyad;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.*;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.*;
import iym.common.util.CommonUtils;
import iym.common.util.NumberUtils;
import iym.makos.model.api.*;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * Mapper class for converting between EvrakKayit entity and DTO
 */
@Component
public class KararRequestMapper {

    public DetayMahkemeKararTalep toDMahkemeKararTalepDetay(MahkemeKarar iliskiliMahkemeKarar, Long mahkemeKararTalepId, Long evrakId){
        if(iliskiliMahkemeKarar == null){
            return null;
        }

        DetayMahkemeKararTalep detayMahkemeKararTalep = new DetayMahkemeKararTalep();
        detayMahkemeKararTalep.setIliskiliMahkemeKararId(iliskiliMahkemeKarar.getId());
        detayMahkemeKararTalep.setMahkemeIlIlceKoduDetay(iliskiliMahkemeKarar.getMahkemeIlIlceKodu());
        detayMahkemeKararTalep.setMahkemeKoduDetay(iliskiliMahkemeKarar.getMahkemeKodu());
        detayMahkemeKararTalep.setSorusturmaNoDetay(iliskiliMahkemeKarar.getSorusturmaNo());
        detayMahkemeKararTalep.setMahkemeKararNoDetay(iliskiliMahkemeKarar.getMahkemeKararNo());
        detayMahkemeKararTalep.setKararTipDetay(iliskiliMahkemeKarar.getKararTip());
        detayMahkemeKararTalep.setMahkemeAdiDetay(iliskiliMahkemeKarar.getMahkemeAdi());
        //
        detayMahkemeKararTalep.setEvrakId(evrakId);
        detayMahkemeKararTalep.setMahkemeKararTalepId(evrakId);

        return detayMahkemeKararTalep;

    }
    public HedeflerTalep toHedeflerTalep(IDHedefDetay dto, Long mahkemeKararTalepId, Long kullaniciId, Date kayitTarihi){
        if(dto == null){
            return null;
        }

        return HedeflerTalep.builder()
                .hedefNo(dto.getHedefNoAdSoyad().getHedef().getHedefNo())
                .hedefTipi(NumberUtils.toLong(dto.getHedefNoAdSoyad().getHedef().getHedefTip().getHedefKodu()))
                .hedefAdi(dto.getHedefNoAdSoyad().getHedefAd())
                .hedefSoyadi(dto.getHedefNoAdSoyad().getHedefSoyad())
                .baslamaTarihi(CommonUtils.toDate(dto.getBaslamaTarihi()))
                .suresi(NumberUtils.toLong(dto.getSure()))
                .sureTipi(NumberUtils.toLong(dto.getSureTip().getValue()))
                .uzatmaSayisi(NumberUtils.toLong(dto.getUzatmaSayisi()))
                .canakNo(dto.getCanakNo())
                .kullaniciId(kullaniciId)
                .kapatmaTarihi(kayitTarihi)
                .mahkemeKararTalepId(mahkemeKararTalepId)
                //.acilmi(dto.getHedefNoAdSoyad().getHedef(). ? "E" : "H")
                .build();


    }

    public HedeflerDetayTalep toHedeflerDetayTalep(IDHedefDetay dto, Long mahkemeKararTalepId, Long iliskliHedefId, Long kullaniciId, Date kayitTarihi){
        if(dto == null){
            return null;
        }

        return HedeflerDetayTalep.builder()
                .hedefNo(dto.getHedefNoAdSoyad().getHedef().getHedefNo())
                .hedefTipi(NumberUtils.toLong(dto.getHedefNoAdSoyad().getHedef().getHedefTip().getHedefKodu()))
                .hedefAdi(dto.getHedefNoAdSoyad().getHedefAd())
                .hedefSoyadi(dto.getHedefNoAdSoyad().getHedefSoyad())
                .mahkemeKararTalepId(mahkemeKararTalepId)
                .iliskiliHedefId(iliskliHedefId)
                .kayitTarihi(kayitTarihi)
                .build();


    }

    public HedeflerAidiyatTalep toHedeflerAidiyatTalep(Long kullaniciID, Long hedeflerTalepId, String aidiyatKodu, Date kayitTarihi){
        HedeflerAidiyatTalep hedeflerAidiyatTalep = new HedeflerAidiyatTalep();
        hedeflerAidiyatTalep.setHedefTalepId(hedeflerTalepId);
        hedeflerAidiyatTalep.setAidiyatKod(aidiyatKodu);
        hedeflerAidiyatTalep.setTarih(kayitTarihi);
        hedeflerAidiyatTalep.setKullaniciId(kullaniciID);
        return  hedeflerAidiyatTalep;
    }

    public EvrakKayit toEvrakKayit(EvrakDetay dto, String evrakSiraNo, String evrakTipi, Date kayitTarihi, Long kullaniciId) {
        if (dto == null) {
            return null;
        }

        return EvrakKayit.builder()
                .evrakNo(dto.getEvrakNo())
                .girisTarih(new Date())
                .evrakTarihi(CommonUtils.toDate(dto.getEvrakTarihi()))
                .evrakGeldigiKurumKodu(dto.getEvrakKurumKodu())
                .kayKullaniciId(kullaniciId)
                .evrakTipi(dto.getEvrakTuru().name())
                .havaleBirim(dto.getHavaleBirimi())
                .aciklama(dto.getAciklama())
                .geldigiIlIlceKodu(dto.getGeldigiIlIlceKodu())
                .evrakKonusu(dto.getEvrakKonusu())
                .evrakYonu(dto.getEvrakTuru().name())
                .acilmi(dto.isAcilmi() ? "E" : "H")
                .girisTarih(kayitTarihi)
                .evrakTipi(evrakTipi)
                .evrakSiraNo(evrakSiraNo)
                .build();
    }

    public EvrakFiles toEvrakFiles(Long evrakId, String dosyaAdi, boolean silindi, int siraNo) {
        EvrakFiles evrakFiles = new EvrakFiles();
        evrakFiles.setEvrakId(evrakId);
        evrakFiles.setFileName(dosyaAdi);
        evrakFiles.setSilindi(silindi ? "E" : "H");
        evrakFiles.setSiraNo(NumberUtils.toLong(siraNo));

        return evrakFiles;
    }

    public MahkemeSuclarTalep toMahkemeSuclarTalep(Long mahkemeKararTalepId, String sucTipiKodu, String  durumu){

        MahkemeSuclarTalep mahkemeSuclarTalep = new MahkemeSuclarTalep();
        mahkemeSuclarTalep.setMahkemeKararTalepId(mahkemeKararTalepId);
        mahkemeSuclarTalep.setSucTipKodu(sucTipiKodu);
        mahkemeSuclarTalep.setDurumu(durumu);

        return mahkemeSuclarTalep;

    }

    public MahkemeAidiyatTalep toMahkemeAidiyatTalep(Long mahkemeKararTalepId, String aidiyatKodu, String  durumu){

        MahkemeAidiyatTalep mahkemeAidiyatTalep = new MahkemeAidiyatTalep();
        mahkemeAidiyatTalep.setMahkemeKararTalepId(mahkemeKararTalepId);
        mahkemeAidiyatTalep.setAidiyatKod(aidiyatKodu);
        mahkemeAidiyatTalep.setDurumu(durumu);

        return mahkemeAidiyatTalep;

    }

    public MahkemeKararTalep toMahkemeKararTalep(MahkemeKararBilgisi dto, Long evrakId, Long kullaniciID, Date kayitTarihi, String mahkemeAdi) {
        if (dto == null) {
            return null;
        }

        MahkemeKararTalep mahkemeKararTalep = MahkemeKararTalep.builder()
                .kayitTarihi(new Date())
                .kararTip(String.valueOf(dto.getMahkemeKararTipi().getKararKodu()))
                .mahkemeKararNo(dto.getMahkemeKararDetay().getMahkemeKararNo())
                .sorusturmaNo(dto.getMahkemeKararDetay().getSorusturmaNo())
                .mahkemeKodu(dto.getMahkemeKararDetay().getMahkemeKodu())
                .mahkemeIlIlceKodu(dto.getMahkemeKararDetay().getMahkemeIlIlceKodu())
                .aciklama(dto.getMahkemeKararDetay().getAciklama())
                .kullaniciId(kullaniciID)
                .evrakId(evrakId)
                .kayitTarihi(kayitTarihi)
                .mahkemeAdi(mahkemeAdi)
                .build();

        if(dto.getMahkemeKararTipi() == MahkemeKararTip.ADLI_KHK_YAZILI_EMIR){
            mahkemeKararTalep.setKararTip(String.valueOf(MahkemeKararTip.ADLI_YAZILI_EMIR.getKararKodu()));
            String yeniAciklama = !CommonUtils.isNullOrEmpty(mahkemeKararTalep.getAciklama()) ? mahkemeKararTalep.getAciklama() + "--ASIL karar tipi:410" : mahkemeKararTalep.getAciklama();
            mahkemeKararTalep.setAciklama(yeniAciklama);
        }
        else if(dto.getMahkemeKararTipi() == MahkemeKararTip.ADLI_KHK_SONLANDIRMA){
            mahkemeKararTalep.setKararTip(String.valueOf(MahkemeKararTip.ADLI_SONLANDIRMA.getKararKodu()));
            String yeniAciklama = !CommonUtils.isNullOrEmpty(mahkemeKararTalep.getAciklama()) ? mahkemeKararTalep.getAciklama() + "--ASIL karar tipi:730" : mahkemeKararTalep.getAciklama();
            mahkemeKararTalep.setAciklama(yeniAciklama);
        }

        return  mahkemeKararTalep;
    }

    public HtsMahkemeKararTalep toHTSMahkemeKararTalep(MahkemeKararBilgisi dto, Long evrakId, Long kullaniciID, Date kayitTarihi, String mahkemeAdi) {
        if (dto == null) {
            return null;
        }

        HtsMahkemeKararTalep mahkemeKararTalep = HtsMahkemeKararTalep.builder()
                .kayitTarihi(new Date())
                .kararTip(String.valueOf(dto.getMahkemeKararTipi().getKararKodu()))
                .mahkemeKararNo(dto.getMahkemeKararDetay().getMahkemeKararNo())
                .sorusturmaNo(dto.getMahkemeKararDetay().getSorusturmaNo())
                .mahkemeKodu(dto.getMahkemeKararDetay().getMahkemeKodu())
                .mahkemeIli(dto.getMahkemeKararDetay().getMahkemeIlIlceKodu())
                .aciklama(dto.getMahkemeKararDetay().getAciklama())
                .kullaniciId(kullaniciID)
                .evrakId(evrakId)
                .kayitTarihi(kayitTarihi)
                .mahkemeAdi(mahkemeAdi)
                .build();

        return  mahkemeKararTalep;
    }


    public MahkemeKararTalep toMahkemeKararTalep(MahkemeKararDetay dto, MahkemeKararTip kararTipi, Long evrakId, Long kullaniciID, Date kayitTarihi, String mahkemeAdi) {
        if (dto == null) {
            return null;
        }

        MahkemeKararTalep mahkemeKararTalep = MahkemeKararTalep.builder()
                .kayitTarihi(new Date())
                .kararTip(String.valueOf(kararTipi.getKararKodu()))
                .mahkemeKararNo(dto.getMahkemeKararNo())
                .sorusturmaNo(dto.getSorusturmaNo())
                .mahkemeKodu(dto.getMahkemeKodu())
                .mahkemeIlIlceKodu(dto.getMahkemeIlIlceKodu())
                .aciklama(dto.getAciklama())
                .kullaniciId(kullaniciID)
                .evrakId(evrakId)
                .kayitTarihi(kayitTarihi)
                .mahkemeAdi(mahkemeAdi)
                .build();

        if(kararTipi == MahkemeKararTip.ADLI_KHK_YAZILI_EMIR){
            mahkemeKararTalep.setKararTip(String.valueOf(MahkemeKararTip.ADLI_YAZILI_EMIR.getKararKodu()));
            String yeniAciklama = !CommonUtils.isNullOrEmpty(mahkemeKararTalep.getAciklama()) ? mahkemeKararTalep.getAciklama() + "--ASIL karar tipi:410" : mahkemeKararTalep.getAciklama();
            mahkemeKararTalep.setAciklama(yeniAciklama);
        }
        else if(kararTipi == MahkemeKararTip.ADLI_KHK_SONLANDIRMA){
            mahkemeKararTalep.setKararTip(String.valueOf(MahkemeKararTip.ADLI_SONLANDIRMA.getKararKodu()));
            String yeniAciklama = !CommonUtils.isNullOrEmpty(mahkemeKararTalep.getAciklama()) ? mahkemeKararTalep.getAciklama() + "--ASIL karar tipi:730" : mahkemeKararTalep.getAciklama();
            mahkemeKararTalep.setAciklama(yeniAciklama);
        }

        return  mahkemeKararTalep;
    }

    public  HedeflerDetayTalep toHedeflerDetayTalep(HedefWithAdSoyad hedef, Long mahkemeKararTalepId, Long iliskliHedefId){
        HedeflerDetayTalep result = new HedeflerDetayTalep();
        result.setHedefNo(hedef.getHedef().getHedefNo());
        result.setHedefTipi(NumberUtils.toLong(hedef.getHedef().getHedefTip().getHedefKodu()));
        result.setHedefAdi(hedef.getHedefAd());
        result.setHedefSoyadi(hedef.getHedefSoyad());
        result.setMahkemeKararTalepId(mahkemeKararTalepId);
        result.setIliskiliHedefId(iliskliHedefId);
        return result;
    }
  /*
    public HtsMahkemeKararTalep toHTSMahkemeKararTalep(MahkemeKararBilgisi dto, Long evrakId, Long kullaniciID, Date kayitTarihi, String mahkemeAdi) {
        if (dto == null) {
            return null;
        }

        HtsMahkemeKararTalep mahkemeKararTalep = HtsMahkemeKararTalep.builder()
                .kayitTarihi(new Date())
                .kararTip(String.valueOf(dto.getMahkemeKararTipi().getKararKodu()))
                .mahkemeKararNo(dto.getMahkemeKararDetay().getMahkemeKararNo())
                .sorusturmaNo(dto.getMahkemeKararDetay().getSorusturmaNo())
                .mahkemeKodu(dto.getMahkemeKararDetay().getMahkemeKodu())
                .mahkemeIli(dto.getMahkemeKararDetay().getMahkemeIlIlceKodu())
                .aciklama(dto.getMahkemeKararDetay().getAciklama())
                .kullaniciId(kullaniciID)
                .evrakId(evrakId)
                .kayitTarihi(kayitTarihi)
                .mahkemeAdi(mahkemeAdi)
                .build();

        return mahkemeKararTalep;
    }




    public EvrakKayit toEvrakKayit(EvrakDetay dto, String evrakSiraNo, String evrakTipi, Date kayitTarihi, Long kullaniciId) {
        if (dto == null) {
            return null;
        }

        return EvrakKayit.builder()
                .evrakNo(dto.getEvrakNo())
                .girisTarih(new Date())
                .evrakTarihi(CommonUtils.toDate(dto.getEvrakTarihi()))
                .evrakGeldigiKurumKodu(dto.getEvrakKurumKodu())
                .kayKullaniciId(kullaniciId)
                .evrakTipi(dto.getEvrakTuru().name())
                .havaleBirim(dto.getHavaleBirimi())
                .aciklama(dto.getAciklama())
                .geldigiIlIlceKodu(dto.getGeldigiIlIlceKodu())
                .evrakKonusu(dto.getEvrakKonusu())
                .evrakYonu(dto.getEvrakTuru().name())
                .acilmi(dto.isAcilmi() ? "E" : "H")
                .girisTarih(kayitTarihi)
                .evrakTipi(evrakTipi)
                .evrakSiraNo(evrakSiraNo)
                .build();
    }


    public MahkemeKararTalep toMahkemeKararTalep(MahkemeKararBilgisi dto, Long evrakId, Long kullaniciID, Date kayitTarihi, String mahkemeAdi) {
        if (dto == null) {
            return null;
        }

        MahkemeKararTalep mahkemeKararTalep = MahkemeKararTalep.builder()
                .kayitTarihi(new Date())
                .kararTip(String.valueOf(dto.getMahkemeKararTipi().getKararKodu()))
                .mahkemeKararNo(dto.getMahkemeKararDetay().getMahkemeKararNo())
                .sorusturmaNo(dto.getMahkemeKararDetay().getSorusturmaNo())
                .mahkemeKodu(dto.getMahkemeKararDetay().getMahkemeKodu())
                .mahkemeIlIlceKodu(dto.getMahkemeKararDetay().getMahkemeIlIlceKodu())
                .aciklama(dto.getMahkemeKararDetay().getAciklama())
                .kullaniciId(kullaniciID)
                .evrakId(evrakId)
                .kayitTarihi(kayitTarihi)
                .mahkemeAdi(mahkemeAdi)
                .build();

        if (dto.getMahkemeKararTipi() == MahkemeKararTip.ADLI_KHK_YAZILI_EMIR) {
            mahkemeKararTalep.setKararTip(String.valueOf(MahkemeKararTip.ADLI_YAZILI_EMIR.getKararKodu()));
            String yeniAciklama = !CommonUtils.isNullOrEmpty(mahkemeKararTalep.getAciklama()) ? mahkemeKararTalep.getAciklama() + "--ASIL karar tipi:410" : mahkemeKararTalep.getAciklama();
            mahkemeKararTalep.setAciklama(yeniAciklama);
        } else if (dto.getMahkemeKararTipi() == MahkemeKararTip.ADLI_KHK_SONLANDIRMA) {
            mahkemeKararTalep.setKararTip(String.valueOf(MahkemeKararTip.ADLI_SONLANDIRMA.getKararKodu()));
            String yeniAciklama = !CommonUtils.isNullOrEmpty(mahkemeKararTalep.getAciklama()) ? mahkemeKararTalep.getAciklama() + "--ASIL karar tipi:730" : mahkemeKararTalep.getAciklama();
            mahkemeKararTalep.setAciklama(yeniAciklama);
        }

        return mahkemeKararTalep;
    }

    public HtsMahkemeKararTalep toHTSMahkemeKararTalep(MahkemeKararBilgisi dto, Long evrakId, Long kullaniciID, Date kayitTarihi, String mahkemeAdi) {
        if (dto == null) {
            return null;
        }

        HtsMahkemeKararTalep mahkemeKararTalep = HtsMahkemeKararTalep.builder()
                .kayitTarihi(new Date())
                .kararTip(String.valueOf(dto.getMahkemeKararTipi().getKararKodu()))
                .mahkemeKararNo(dto.getMahkemeKararDetay().getMahkemeKararNo())
                .sorusturmaNo(dto.getMahkemeKararDetay().getSorusturmaNo())
                .mahkemeKodu(dto.getMahkemeKararDetay().getMahkemeKodu())
                .mahkemeIli(dto.getMahkemeKararDetay().getMahkemeIlIlceKodu())
                .aciklama(dto.getMahkemeKararDetay().getAciklama())
                .kullaniciId(kullaniciID)
                .evrakId(evrakId)
                .kayitTarihi(kayitTarihi)
                .mahkemeAdi(mahkemeAdi)
                .build();

        return mahkemeKararTalep;
    }

    public EvrakFiles toEvrakFiles(Long evrakId, String dosyaAdi, boolean silindi, int siraNo) {
        EvrakFiles evrakFiles = new EvrakFiles();
        evrakFiles.setEvrakId(evrakId);
        evrakFiles.setFileName(dosyaAdi);
        evrakFiles.setSilindi(silindi ? "E" : "H");
        evrakFiles.setSiraNo(NumberUtils.toLong(siraNo));

        return evrakFiles;
    }

    *
    * */

}
