package iym.makos.model.dto.talepislem;

import iym.common.enums.TalepGuncellemeTuru;
import iym.common.validation.ValidationResult;
import iym.makos.model.MakosRequest;
import iym.makos.validator.custom.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Jacksonized
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@MakosRequestValid
@Slf4j
public class MahkemeKararTalepUpdateRequest implements MakosRequest {

    @NotNull
    private UUID id;

    @NotNull
    @Valid
    private TalepGuncellemeTuru talepGuncellemeTuru;

    @NotNull
    private Long mahkemeKararTalepId;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if MahkemeKararTalepUpdateRequest is valid");
        ValidationResult validationResult = new ValidationResult(true);
        try {
            // TODO
            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

}

