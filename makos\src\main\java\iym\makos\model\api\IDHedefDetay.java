package iym.makos.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.model.api.HedefWithAdSoyad;
import iym.common.enums.SureTip;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class IDHedefDetay {

  @NotNull
  @Valid
  private HedefWithAdSoyad hedefNoAdSoyad;

  @NotNull
  private LocalDateTime baslamaTarihi;

  @NotNull
  private SureTip sureTip;

  @NotNull
  private Integer sure;

  @Schema(description = "Uzatilan/Sonlandirilan Hedefin ilgili Mahkeme Kararı. Sadece uzatma/sonlandirma kararlarinda gerekli")
  private MahkemeKararDetay ilgiliMahkemeKararDetayi;

  @Schema(description = "Uzatma Sayisi. Sadece uzatma kararlarinda gerekli")
  private Integer uzatmaSayisi;

  private List<String> hedefAidiyatKodlari;

  @Schema(description = "Canak numarası. Sadece yeni kararda girilebilir. Zorunlu olmayan alan")
  private String canakNo;

}

