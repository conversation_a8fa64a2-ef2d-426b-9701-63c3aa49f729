package iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mk.id;

import iym.common.model.api.ApiResponseBase;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mk.MahkemeKararIslenecekView;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class IDMahkemeKararIslenecekResponse extends ApiResponseBase {
    private List<MahkemeKararIslenecekView> islenecekKararlar;
} 