package iym.common.model.entity.iym.mk;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for MAHKEME_AIDIYAT_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeAidiyatDetayIslem")
@Table(name = "MAHKEME_AIDIYAT_DETAY_ISLEM")
public class MahkemeAidiyatDetayIslem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAH_AID_DET_ISLEM_SEQ")
    @SequenceGenerator(name = "MAH_AID_DET_ISLEM_SEQ", sequenceName = "MAH_AID_DET_ISLEM_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "ILISKILI_MAHKEME_KARAR_ID")
    private Long iliskiliMahkemeKararId;

    @Column(name = "MAHKEME_KARAR_ID")
    private Long mahkemeKararId;

    @Column(name = "MAHKEME_AIDIYAT_KODU_EKLE", length = 25)
    @Size(max = 25)
    private String mahkemeAidiyatKoduEkle;

    @Column(name = "MAHKEME_AIDIYAT_KODU_CIKAR", length = 25)
    @Size(max = 25)
    private String mahkemeAidiyatKoduCikar;

    @Column(name = "TARIH", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date tarih;

    @Column(name = "DURUM", length = 15)
    @Size(max = 15)
    private String durum;

    @Column(name = "MAHKEME_KARAR_DETAY_ID")
    private Long detayMahkemeKararIslemId;

}
