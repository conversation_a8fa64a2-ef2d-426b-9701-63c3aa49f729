package iym.makos.domain.talepislem.idislemeaktar;

import iym.common.enums.KararTuru;
import iym.common.enums.TalepGuncellemeTuru;


public interface IDMahkemeKararTalepIslemeAktarici {

    String DURUM_ONAYLA = "ONAYLANDI";
    String DURUM_SILINDI = "SILINDI";
    String DURUM_ARSIV = "ARSIV";

    boolean process(Long mahkemeKararTalepId, Long kullaniciId);

    KararTuru getRelatedKararTuru();

    static String toDurum(TalepGuncellemeTuru talepGuncellemeTuru){
        return switch (talepGuncellemeTuru) {
            case ONAYLA -> DURUM_ONAYLA;
            case SIL -> DURUM_SILINDI;
            case ARSIV -> DURUM_ARSIV;
        };
    }
}

