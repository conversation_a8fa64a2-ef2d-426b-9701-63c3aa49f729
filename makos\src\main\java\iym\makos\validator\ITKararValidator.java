package iym.makos.validator;

import iym.common.enums.KararTuru;
import iym.common.service.db.mk.DbMahkemeKararAidiyatService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.validation.ValidationResult;
import iym.makos.model.dto.mahkemekarar.it.ITKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ITKararValidator extends MahkemeKararRequestValidatorBase<ITKararRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeKararAidiyatService dbMahkemeKararAidiyatService;

    @Autowired
    public ITKararValidator(DbMahkemeKararService dbMahkemeKararService,
                            DbMahkemeKararAidiyatService dbMahkemeKararAidiyatService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeKararAidiyatService = dbMahkemeKararAidiyatService;

    }

    @Override
    protected ValidationResult doValidate(ITKararRequest request) {
        try {
            ValidationResult validationResult = new ValidationResult(true);

            // IT kararları için özel validation kuralları
            if (request.getHedefDetayListesi() == null || request.getHedefDetayListesi().isEmpty()) {
                validationResult.addFailedReason("IT kararında hedef detay listesi boş olamaz");
                return validationResult;
            }

            // Her hedef detayı için validation
            for (var hedefDetay : request.getHedefDetayListesi()) {
                // Başlama ve bitiş tarihi kontrolü
                if (hedefDetay.getBaslamaTarihi() == null) {
                    validationResult.addFailedReason("IT kararında başlama tarihi boş olamaz");
                }

                if (hedefDetay.getBitisTarihi() == null) {
                    validationResult.addFailedReason("IT kararında bitiş tarihi boş olamaz");
                }

                if (hedefDetay.getBaslamaTarihi() != null && hedefDetay.getBitisTarihi() != null) {
                    if (hedefDetay.getBaslamaTarihi().isAfter(hedefDetay.getBitisTarihi())) {
                        validationResult.addFailedReason("IT kararında başlama tarihi bitiş tarihinden sonra olamaz");
                    }
                }

                // Tespit türü kontrolü
                if (hedefDetay.getTespitTuru() == null || hedefDetay.getTespitTuru().trim().isEmpty()) {
                    validationResult.addFailedReason("IT kararında tespit türü boş olamaz");
                }

                // Sorgu tipi kontrolü
                if (hedefDetay.getSorguTipi() == null) {
                    validationResult.addFailedReason("IT kararında sorgu tipi boş olamaz");
                }

                // Hedef kontrolü
                if (hedefDetay.getHedef() == null) {
                    validationResult.addFailedReason("IT kararında hedef bilgisi boş olamaz");
                } else {
                    if (hedefDetay.getHedef().getHedefNo() == null || hedefDetay.getHedef().getHedefNo().trim().isEmpty()) {
                        validationResult.addFailedReason("IT kararında hedef numarası boş olamaz");
                    }
                    if (hedefDetay.getHedef().getHedefTip() == null) {
                        validationResult.addFailedReason("IT kararında hedef tipi boş olamaz");
                    }
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_TESPITI;
    }

}

