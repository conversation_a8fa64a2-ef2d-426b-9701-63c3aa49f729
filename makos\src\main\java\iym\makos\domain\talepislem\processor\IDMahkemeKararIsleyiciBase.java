package iym.makos.domain.talepislem.processor;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.db.jpa.dao.EvrakKayitRepo;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Abstract base class for processing İletişimin Denetlenmesi (ID) mahkeme karar talep updates.
 *
 * <p>This class implements the <strong>Template Method design pattern</strong> to ensure consistent
 * processing of mahkeme karar talep updates across different karar types. It defines the core
 * algorithm while allowing subclasses to implement type-specific business logic.</p>
 *
 * <p><strong>Design Pattern:</strong><br>
 * The {@link #process(MahkemeKararTalepUpdateRequest, Long)} method serves as the template method
 * that defines the algorithm skeleton. Subclasses must implement the
 * {@link #updateRelatedTables(MahkemeKararTalepUpdateRequest)} method to provide specific behavior.</p>
 *
 * <p><strong>Transaction Management:</strong><br>
 * This class uses declarative transaction management via {@code @Transactional} on the main
 * process method. All database operations (core updates and subclass-specific updates) are
 * executed within a single transaction to ensure data consistency.</p>
 *
 * <p><strong>Database Operations:</strong><br>
 * The base class handles updates to:</p>
 * <ul>
 *   <li>{@code MAHKEME_KARAR_TALEP} table - main request record</li>
 *   <li>{@code EVRAK_KAYIT} table - related document record</li>
 * </ul>
 *
 * <p>Subclasses handle updates to type-specific tables such as:</p>
 * <ul>
 *   <li>{@code DETAY_MAHKEME_KARAR_TALEP}</li>
 *   <li>{@code HEDEFLER_TALEP} and {@code HEDEFLER_DETAY_TALEP}</li>
 *   <li>{@code MAHKEME_AIDIYAT_TALEP}</li>
 *   <li>{@code MAHKEME_SUCLAR_TALEP}</li>
 * </ul>
 *
 * <p><strong>Subclass Implementation:</strong><br>
 * Concrete implementations must:</p>
 * <ol>
 *   <li>Extend this base class</li>
 *   <li>Be annotated with {@code @Component} for Spring dependency injection</li>
 *   <li>Implement {@link #updateRelatedTables(MahkemeKararTalepUpdateRequest)}</li>
 *   <li>Implement {@link #getRelatedKararTuru()}</li>
 * </ol>
 *
 * <p><strong>Example Subclasses:</strong></p>
 * <ul>
 *   <li>{@code IDYeniKararIsleyici} - for new karar processing</li>
 *   <li>{@code IDMahkemeKararGuncellemeIsleyici} - for karar updates</li>
 *   <li>{@code IDAidiyatBilgisiGuncellemeIsleyici} - for aidiyat updates</li>
 *   <li>{@code IDSonlandirmaKarariIsleyici} - for termination decisions</li>
 * </ul>
 *
 * @see MahkemeKararTalepIsleyici
 * @see MahkemeKararTalepUpdateRequest
 * @see MahkemeKararTalepUpdateResponse
 * @since 1.0
 */
@Slf4j
public abstract class IDMahkemeKararIsleyiciBase implements MahkemeKararTalepIsleyici {

    protected EvrakKayitRepo evrakKayitRepo;
    protected MahkemeKararTalepRepo mahkemeKararTalepRepo;

    /**
     * Sets the EvrakKayitRepo dependency via Spring dependency injection.
     *
     * <p>This method is marked as {@code final} to prevent subclasses from overriding
     * the dependency injection mechanism, ensuring consistent repository access across
     * all implementations.</p>
     *
     * @param evrakKayitRepo the repository for evrak kayit operations
     */
    @Autowired
    public final void setEvrakKayitRepo(EvrakKayitRepo evrakKayitRepo) {
        this.evrakKayitRepo = evrakKayitRepo;
    }

    /**
     * Sets the MahkemeKararTalepRepo dependency via Spring dependency injection.
     *
     * <p>This method is marked as {@code final} to prevent subclasses from overriding
     * the dependency injection mechanism, ensuring consistent repository access across
     * all implementations.</p>
     *
     * @param mahkemeKararTalepRepo the repository for mahkeme karar talep operations
     */
    @Autowired
    public final void setMahkemeKararTalepRepo(MahkemeKararTalepRepo mahkemeKararTalepRepo) {
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
    }

    /**
     * Template method that defines the core algorithm for processing mahkeme karar talep updates.
     *
     * <p><strong>CRITICAL: This method MUST NOT be overridden by subclasses.</strong></p>
     *
     * <p>This method implements the Template Method design pattern and serves as the main entry point
     * for processing mahkeme karar talep updates. It defines a strict sequence of operations that
     * ensures data consistency and transaction integrity:</p>
     *
     * <ol>
     *   <li>Updates the main MahkemeKararTalep record status</li>
     *   <li>Updates the related EvrakKayit record status</li>
     *   <li>Delegates to subclass-specific logic via {@link #updateRelatedTables(MahkemeKararTalepUpdateRequest)}</li>
     * </ol>
     *
     * <p><strong>Transaction Management:</strong><br>
     * This method is annotated with {@code @Transactional} to ensure that all database operations
     * (main record update, evrak update, and subclass-specific updates) are executed within a single
     * transaction. This guarantees atomicity - either all operations succeed or all are rolled back.</p>
     *
     * <p><strong>Why This Method Should Not Be Overridden:</strong></p>
     * <ul>
     *   <li><strong>Transaction Boundary:</strong> Overriding could bypass the transaction boundary,
     *       leading to data inconsistency</li>
     *   <li><strong>Data Integrity:</strong> The sequence of operations is critical for maintaining
     *       referential integrity between MahkemeKararTalep and EvrakKayit tables</li>
     *   <li><strong>Business Logic Consistency:</strong> All subclasses must follow the same core
     *       algorithm to ensure predictable behavior</li>
     *   <li><strong>Error Handling:</strong> The method includes proper error handling and response
     *       building that should be consistent across all implementations</li>
     * </ul>
     *
     * <p><strong>For Subclass Implementers:</strong><br>
     * To add specific business logic, implement the {@link #updateRelatedTables(MahkemeKararTalepUpdateRequest)}
     * abstract method. This method will be called within the same transaction after the core
     * operations are completed successfully.</p>
     *
     * <p><strong>Historical Note:</strong><br>
     * This method was previously marked as {@code final} to enforce the template method pattern
     * at compile time. The {@code final} modifier was removed to resolve Spring AOP proxy issues
     * with {@code @Transactional}, but the design intent remains the same.</p>
     *
     * @param request the mahkeme karar talep update request containing the operation details
     * @param kullaniciId the ID of the user performing the operation (for audit purposes)
     * @return the response indicating success or failure of the operation
     * @throws RuntimeException if any database operation fails (will trigger transaction rollback)
     *
     * @see #updateRelatedTables(MahkemeKararTalepUpdateRequest)
     * @see org.springframework.transaction.annotation.Transactional
     */
    @Override
    @Transactional
    public MahkemeKararTalepUpdateResponse process(MahkemeKararTalepUpdateRequest request, Long kullaniciId) {
        MahkemeKararTalepUpdateResponse processResponse = process(request);
        if (!processResponse.isSuccess()) {
            return processResponse;
        }

        return updateRelatedTables(request);
    }

    /**
     * Abstract method for subclass-specific database operations.
     *
     * <p>This method is called by the {@link #process(MahkemeKararTalepUpdateRequest, Long)} template method
     * after the core operations (main record and evrak updates) have been completed successfully.
     * It executes within the same transaction context as the main process method.</p>
     *
     * <p><strong>Implementation Guidelines:</strong></p>
     * <ul>
     *   <li><strong>Transaction Context:</strong> This method runs within the transaction started by
     *       the {@code process} method. Do not add additional {@code @Transactional} annotations.</li>
     *   <li><strong>Error Handling:</strong> Throw exceptions for any errors that should cause the
     *       entire operation to be rolled back. Return error responses for business logic failures.</li>
     *   <li><strong>Database Operations:</strong> Perform any additional database updates specific
     *       to the karar type (e.g., updating hedefler, aidiyat, suc tipi tables).</li>
     *   <li><strong>Response Building:</strong> Always return a properly constructed response with
     *       appropriate success/failure status and error messages.</li>
     * </ul>
     *
     * <p><strong>Examples of Subclass-Specific Operations:</strong></p>
     * <ul>
     *   <li>Updating DetayMahkemeKararTalep records</li>
     *   <li>Managing HedeflerTalep and HedeflerDetayTalep records</li>
     *   <li>Updating MahkemeAidiyatTalep records</li>
     *   <li>Managing MahkemeSuclarTalep records</li>
     * </ul>
     *
     * @param request the mahkeme karar talep update request containing operation details
     * @return response indicating success or failure of the subclass-specific operations
     * @throws Exception if any database operation fails (will trigger transaction rollback)
     */
    protected abstract MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request);

    /**
     * Private helper method that performs the core database operations.
     *
     * <p>This method handles the standard operations that are common to all ID karar types:</p>
     * <ol>
     *   <li>Updates the MahkemeKararTalep record status</li>
     *   <li>Updates the related EvrakKayit record status</li>
     * </ol>
     *
     * <p>This method is called by the public {@link #process(MahkemeKararTalepUpdateRequest, Long)}
     * template method and executes within the same transaction context.</p>
     *
     * @param request the update request containing the operation details
     * @return success response if operations complete successfully, error response otherwise
     */
    private MahkemeKararTalepUpdateResponse process(MahkemeKararTalepUpdateRequest request) {

        try{

            String durum = MahkemeKararTalepIsleyici.toDurum(request.getTalepGuncellemeTuru());
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(request.getMahkemeKararTalepId());
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new Exception("talep bulunamadı");
            }
            MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepOpt.get();

            //durum degisikligini kaydet
            mahkemeKararTalep.setDurum(durum);
            mahkemeKararTalepRepo.save(mahkemeKararTalep);

            Long evrakId = mahkemeKararTalep.getEvrakId();
            Optional<EvrakKayit> evrakKayitOpt = evrakKayitRepo.findById(evrakId);
            if (evrakKayitOpt.isEmpty()) {
                throw new Exception("Evrak kayıt bulunamadı");
            }
            EvrakKayit evrakKayit = evrakKayitOpt.get();

            //durum degisikligini kaydet
            evrakKayit.setDurumu(durum);
            evrakKayitRepo.save(evrakKayit);

            return MahkemeKararTalepUpdateResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build();
        }catch (Exception ex){
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Mahkeme karar talep veya evrak guncelleme hatası")
                            .build())
                    .build();
        }
    }

}

