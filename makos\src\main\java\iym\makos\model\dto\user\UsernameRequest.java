package iym.makos.model.dto.user;

import iym.common.validation.ValidationResult;
import iym.makos.model.MakosRequest;
import iym.makos.validator.custom.MakosRequestValid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

/**
 * Username request DTO for MAKOS user management
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Jacksonized
@MakosRequestValid
@Slf4j
public class UsernameRequest implements MakosRequest {

    @NotNull
    @NotBlank(message = "Username is required")
    private String username;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if UsernameRequest is valid");
        
        ValidationResult validationResult = new ValidationResult(true);
        
        try {
            if (username == null || username.trim().isEmpty()) {
                validationResult.addFailedReason("Username cannot be null or empty");
            }
        } catch (Exception e) {
            log.error("Validation failed", e);
            validationResult.addFailedReason("Validation error: " + e.getMessage());
        }
        
        return validationResult;
    }
}
