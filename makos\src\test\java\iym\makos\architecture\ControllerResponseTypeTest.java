package iym.makos.architecture;

import com.tngtech.archunit.core.domain.JavaClass;
import com.tngtech.archunit.core.domain.JavaClasses;
import com.tngtech.archunit.core.domain.JavaMethod;
import com.tngtech.archunit.core.importer.ClassFileImporter;
import iym.common.model.api.ApiResponse;
import iym.makos.model.MakosApiResponse;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.fail;

/**
 * Architectural test to ensure that all @RestController methods in the makos module
 * return ResponseEntity with generic types that contain an ApiResponse or MakosApiResponse attribute named 'response'.
 * <p>
 * This test verifies the following architecture rules:
 * 1. All RestController methods should return ResponseEntity<SomeResponseDTO>
 * 2. The SomeResponseDTO class should contain an ApiResponse or MakosApiResponse attribute named 'response'
 * 3. No controller methods should return ResponseEntity<?> or ResponseEntity<String>
 */
class ControllerResponseTypeTest {

    @Test
    void testAllControllerMethodsReturnResponseEntityWithApiResponseAttribute() {
        // Import all classes from the makos controller package
        JavaClasses classes = new ClassFileImporter()
                .importPackages("iym.makos.controller");

        // Find all RestController classes
        Set<JavaClass> controllerClasses = classes.stream()
                .filter(javaClass -> javaClass.isAnnotatedWith(RestController.class))
                .collect(Collectors.toSet());

        assertFalse(controllerClasses.isEmpty(), "No RestController classes found in iym.makos.controller package");

        // Collect all violations instead of failing immediately
        List<String> violations = new ArrayList<>();

        // Check each controller class
        for (JavaClass controllerClass : controllerClasses) {
            System.out.println("Checking controller: " + controllerClass.getName());

            // Get all public methods
            Set<JavaMethod> publicMethods = controllerClass.getMethods().stream()
                    .filter(method -> method.getModifiers().contains(com.tngtech.archunit.core.domain.JavaModifier.PUBLIC))
                    .filter(method -> !method.getName().equals("equals"))
                    .filter(method -> !method.getName().equals("hashCode"))
                    .filter(method -> !method.getName().equals("toString"))
                    .collect(Collectors.toSet());

            for (JavaMethod method : publicMethods) {
                System.out.println("  Checking method: " + method.getName());
                try {
                    checkMethodReturnType(controllerClass, method);
                } catch (AssertionError e) {
                    violations.add(e.getMessage());
                }
            }
        }

        // Report all violations at once
        if (!violations.isEmpty()) {
            StringBuilder errorMessage = new StringBuilder();
            errorMessage.append("Found ").append(violations.size()).append(" architecture violations:\n\n");
            for (int i = 0; i < violations.size(); i++) {
                errorMessage.append(i + 1).append(". ").append(violations.get(i)).append("\n\n");
            }
            fail(errorMessage.toString());
        }
    }

    private void checkMethodReturnType(JavaClass controllerClass, JavaMethod method) {
        com.tngtech.archunit.core.domain.JavaType returnType = method.getReturnType();

        // Check if method returns ResponseEntity
        if (returnType.toErasure().isEquivalentTo(ResponseEntity.class)) {
            checkResponseEntityGenericType(controllerClass, method);
        } else if (!returnType.toErasure().isEquivalentTo(void.class)) {
            // For non-ResponseEntity return types, check if they have ApiResponse attribute
            checkTypeHasApiResponseAttribute(controllerClass, method, returnType.toErasure());
        }
    }

    private void checkResponseEntityGenericType(JavaClass controllerClass, JavaMethod method) {
        try {
            // Use reflection to get the actual generic type information
            Class<?> actualControllerClass = Class.forName(controllerClass.getName());
            java.lang.reflect.Method actualMethod = findMatchingMethod(actualControllerClass, method);

            if (actualMethod == null) {
                throw new AssertionError(String.format("Could not find method %s in controller %s",
                        method.getName(), controllerClass.getSimpleName()));
            }

            Type genericReturnType = actualMethod.getGenericReturnType();

            if (genericReturnType instanceof ParameterizedType parameterizedType) {
                Type[] typeArguments = parameterizedType.getActualTypeArguments();

                if (typeArguments.length > 0) {
                    Type typeArgument = typeArguments[0];

                    // Check for ResponseEntity<?>
                    if (typeArgument instanceof java.lang.reflect.WildcardType) {
                        throw new AssertionError(String.format(
                                "Controller %s method %s returns ResponseEntity<?> which violates architecture rules. " +
                                        "Methods should return ResponseEntity<SomeResponseDTO> where SomeResponseDTO contains an ApiResponse or MakosApiResponse attribute named 'response'.",
                                controllerClass.getSimpleName(),
                                method.getName()
                        ));
                    }
                    // Check for ResponseEntity<String> or other primitive types
                    else if (typeArgument instanceof Class<?> responseType) {
                        if (isPrimitiveOrStringType(responseType)) {
                            throw new AssertionError(String.format(
                                    "Controller %s method %s returns ResponseEntity<%s> which violates architecture rules. " +
                                            "Methods should return ResponseEntity<SomeResponseDTO> where SomeResponseDTO contains an ApiResponse or MakosApiResponse attribute named 'response'.",
                                    controllerClass.getSimpleName(),
                                    method.getName(),
                                    responseType.getSimpleName()
                            ));
                        } else {
                            checkClassHasApiResponseAttribute(controllerClass, method, responseType);
                        }
                    }
                    // Handle parameterized types like ResponseEntity<List<SomeDTO>>
                    else if (typeArgument instanceof ParameterizedType parameterizedTypeArgument) {
                        Type rawType = parameterizedTypeArgument.getRawType();
                        if (rawType instanceof Class<?> responseType) {
                            checkClassHasApiResponseAttribute(controllerClass, method, responseType);
                        }
                    }
                }
            } else {
                throw new AssertionError(String.format(
                        "Controller %s method %s returns raw ResponseEntity without generic type parameter. " +
                                "Methods should return ResponseEntity<SomeResponseDTO> where SomeResponseDTO contains an ApiResponse or MakosApiResponse attribute named 'response'.",
                        controllerClass.getSimpleName(),
                        method.getName()
                ));
            }
        } catch (ClassNotFoundException e) {
            throw new AssertionError(String.format("Could not load controller class %s: %s",
                    controllerClass.getName(), e.getMessage()));
        }
    }

    private void checkTypeHasApiResponseAttribute(JavaClass controllerClass, JavaMethod method, JavaClass returnType) {
        try {
            Class<?> actualReturnType = Class.forName(returnType.getName());
            checkClassHasApiResponseAttribute(controllerClass, method, actualReturnType);
        } catch (ClassNotFoundException e) {
            throw new AssertionError(String.format("Could not load return type class %s for method %s in controller %s: %s",
                    returnType.getName(), method.getName(), controllerClass.getSimpleName(), e.getMessage()));
        }
    }

    private void checkClassHasApiResponseAttribute(JavaClass controllerClass, JavaMethod method, Class<?> responseType) {
        // Look for a field named 'response' of type ApiResponse or MakosApiResponse
        Field responseField = getResponseField(controllerClass, method, responseType);

        // Check if the field is of type ApiResponse or MakosApiResponse
        if (!ApiResponse.class.isAssignableFrom(responseField.getType()) &&
                !MakosApiResponse.class.isAssignableFrom(responseField.getType())) {
            throw new AssertionError(String.format(
                    "Controller %s method %s returns %s which has a 'response' attribute of type %s instead of ApiResponse or MakosApiResponse. " +
                            "The 'response' attribute should be of type ApiResponse or MakosApiResponse.",
                    controllerClass.getSimpleName(),
                    method.getName(),
                    responseType.getSimpleName(),
                    responseField.getType().getSimpleName()
            ));
        }

        System.out.printf("    ✓ %s.%s returns %s with valid ApiResponse/MakosApiResponse attribute%n",
                controllerClass.getSimpleName(), method.getName(), responseType.getSimpleName());
    }

    private static Field getResponseField(JavaClass controllerClass, JavaMethod method, Class<?> responseType) {
        Field responseField = null;
        try {
            responseField = responseType.getDeclaredField("response");
        } catch (NoSuchFieldException e) {
            // Field not found, check if it's inherited
            Class<?> currentClass = responseType;
            while (currentClass != null && currentClass != Object.class) {
                try {
                    responseField = currentClass.getDeclaredField("response");
                    break;
                } catch (NoSuchFieldException ignored) {
                    currentClass = currentClass.getSuperclass();
                }
            }
        }

        if (responseField == null) {
            throw new AssertionError(String.format(
                    "Controller %s method %s returns %s which does not contain an 'ApiResponse' or 'MakosApiResponse' attribute named 'response'. " +
                            "Response DTOs should contain: 'private ApiResponse response;' or 'private MakosApiResponse response;'",
                    controllerClass.getSimpleName(),
                    method.getName(),
                    responseType.getSimpleName()
            ));
        }
        return responseField;
    }

    private java.lang.reflect.Method findMatchingMethod(Class<?> clazz, JavaMethod javaMethod) {
        for (java.lang.reflect.Method method : clazz.getDeclaredMethods()) {
            if (method.getName().equals(javaMethod.getName()) &&
                    method.getParameterCount() == javaMethod.getParameters().size()) {
                return method;
            }
        }
        return null;
    }

    private boolean isPrimitiveOrStringType(Class<?> type) {
        return type.isPrimitive() ||
                type == String.class ||
                type == Integer.class ||
                type == Long.class ||
                type == Double.class ||
                type == Float.class ||
                type == Boolean.class ||
                type == Character.class ||
                type == Byte.class ||
                type == Short.class ||
                java.util.Map.class.isAssignableFrom(type) ||
                java.util.Collection.class.isAssignableFrom(type);
    }
}
