-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEFLER_ISLEM_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MA<PERSON><PERSON>ME_HEDEFLER_AIDIYAT_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_HEDEFLER_AIDIYAT_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

--TODO byte -> null
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_HEDEFLER_AIDIYAT';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_HEDEFLER_AIDIYAT (

       ID NUMBER NOT NULL
     , HEDEF_ID NUMBER NOT NULL
     , <PERSON>DIYAT_KOD VARCHAR2(15) NOT NULL
     , TARIH DATE NOT NULL
     , KULL<PERSON>ICI_ID NUMBER NOT NULL
     , MAHKEME_KARAR_ID NUMBER NOT NULL
     , CONSTRAINT M_HEDEF_AIDIYAT_ID_IDX PRIMARY KEY (ID) ENABLE

  )';

  END IF;
END;
/



COMMIT;
