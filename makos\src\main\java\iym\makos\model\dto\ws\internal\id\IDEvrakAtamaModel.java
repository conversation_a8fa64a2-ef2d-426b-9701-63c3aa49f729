package iym.makos.model.dto.ws.internal.id;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description =  "Evrak Atama History Bilgisi")
public class IDEvrakAtamaModel {

    private Long evrakId;
    private String evrakSiraNo;
    private String evrakNo;

    private String sorusturmaNo;
    private String mahkemeKararNo;

    private Long atayanKullaniciId;
    private String atayanAdiSoyadi;
    private Long atananKullaniciId;
    private String atananAdiSoyadi;
    private String aciklama;

    private String sebebi;
    private String durum;

}

