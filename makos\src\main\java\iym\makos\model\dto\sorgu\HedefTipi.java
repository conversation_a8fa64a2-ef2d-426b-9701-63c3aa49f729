package iym.makos.model.dto.sorgu;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Schema(description = "Hedef Tipi bilgilerini içerir")
public class HedefTipi implements Serializable {

    private Long hedefKodu;
    private String hedefTipi;
    private String durum;

}
