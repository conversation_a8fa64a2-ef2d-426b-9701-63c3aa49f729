package iym.makos.domain.talepislem.idislemeaktar;

import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.mk.DetayMahkemeKararIslem;
import iym.common.model.entity.iym.mk.HedeflerDetayIslem;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.util.ApplicationConstants;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.HedeflerTalepRepo;
import iym.db.jpa.dao.mk.DetayMahkemeKararIslemRepo;
import iym.db.jpa.dao.mk.HedeflerDetayIslemRepo;
import iym.db.jpa.dao.talep.HedeflerDetayTalepRepo;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.makos.mapper.islemtablolari.DetayMahkemeKararIslemMapper;
import iym.makos.mapper.islemtablolari.HedeflerDetayIslemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IDHedefGuncellemeIslemeAktarici extends IDMahkemeKararTalepIslemeAktariciBase {

    private final HedeflerDetayTalepRepo hedeflerDetayTalepRepo;
    private final HedeflerDetayIslemRepo hedeflerDetayIslemRepo;
    private final HedeflerDetayIslemMapper hedeflerDetayIslemMapper;
    private final DetayMahkemeKararTalepRepo detayMahkemeKararTalepRepo;

    private DetayMahkemeKararIslemRepo detayMahkemeKararIslemRepo;
    private DetayMahkemeKararIslemMapper detayMahkemeKararIslemMapper;

    @Autowired
    public IDHedefGuncellemeIslemeAktarici(HedeflerDetayTalepRepo hedeflerDetayTalepRepo
            , HedeflerDetayIslemRepo hedeflerDetayIslemRepo
            , HedeflerDetayIslemMapper hedeflerDetayIslemMapper
            , DetayMahkemeKararTalepRepo detayMahkemeKararTalepRepo
            , HedeflerTalepRepo hedeflerTalepRepo
    ) {
        this.hedeflerDetayTalepRepo = hedeflerDetayTalepRepo;
        this.hedeflerDetayIslemRepo = hedeflerDetayIslemRepo;
        this.hedeflerDetayIslemMapper = hedeflerDetayIslemMapper;
        this.detayMahkemeKararTalepRepo = detayMahkemeKararTalepRepo;
    }

    protected boolean updateRelatedTables(Long mahkemeKararTalepId) {
        boolean result = false;
        try {
            String durum = ApplicationConstants.MKTALEP_DURUM_ISLEMDE;
            CommonUtils.safeList(detayMahkemeKararTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId))
                    .forEach(dMahkemeKararTalep -> {
                        dMahkemeKararTalep.setDurum(durum);
                        detayMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                        DetayMahkemeKararIslem detayMahkemeKararIslem = detayMahkemeKararIslemMapper.fromDetayMahkemeKararTalep(dMahkemeKararTalep);
                        DetayMahkemeKararIslem savedDetayMahkemeKararIslem = detayMahkemeKararIslemRepo.save(detayMahkemeKararIslem);


                        CommonUtils.safeList(hedeflerDetayTalepRepo.findByMahkemeKararTalepId(dMahkemeKararTalep.getId()))
                                .forEach(hedeflerDetayTalep -> {
                                    hedeflerDetayTalep.setDurumu(durum);
                                    hedeflerDetayTalepRepo.save(hedeflerDetayTalep);

                                    //işlem tablosuna aktar
                                    HedeflerDetayIslem hedeflerDetayIslem = hedeflerDetayIslemMapper.fromDetayMahkemeKararTalep(hedeflerDetayTalep);
                                    hedeflerDetayIslem.setMahkemeKararTalepId(mahkemeKararTalepId);
                                    hedeflerDetayIslem.setDetayMahkemeKararIslemId(savedDetayMahkemeKararIslem.getId());

                                    hedeflerDetayIslemRepo.save(hedeflerDetayIslem);

                                });
                    });

            result = true;
        } catch (Exception ex) {
            log.error("IDHedefGuncellemeIslemeAktarici process failed,  mahkemeKararTalepId:{}", mahkemeKararTalepId, ex);
        }
        return result;
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME;
    }

}

