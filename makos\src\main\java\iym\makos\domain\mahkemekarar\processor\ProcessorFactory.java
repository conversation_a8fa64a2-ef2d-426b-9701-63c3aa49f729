package iym.makos.domain.mahkemekarar.processor;


import iym.makos.model.dto.mahkemekarar.MahkemeKararRequest;
import iym.makos.model.dto.mahkemekarar.MahkemeKararResponse;
import iym.makos.model.dto.mahkemekarar.id.IDHedefGuncellemeRequest;
import iym.makos.model.dto.mahkemekarar.id.IDHedefGuncellemeResponse;
import iym.makos.model.dto.mahkemekarar.id.IDSonlandirmaKarariResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ProcessorFactory {

    private final Map<Class<? extends MahkemeKararRequest>, Map<Class<? extends MahkemeKararResponse>, IMakosRequestProcessor<?, ?>>> processorsByRequest = new HashMap<>();

    @Autowired
    public ProcessorFactory(List<IMakosRequestProcessor<?, ?>> requestProcessors) {
        for (IMakosRequestProcessor<?, ?> processor : requestProcessors) {
            processorsByRequest.computeIfAbsent(processor.getRelatedRequestType(), map -> new HashMap<>())
                    .put(processor.getRelatedResponseType(), processor);
        }
    }

    @SuppressWarnings("unchecked")
    public <T extends MahkemeKararRequest, R extends MahkemeKararResponse> IMakosRequestProcessor<T, R> getProcessor(T request, Class<R> responseType) {
        Map<Class<? extends MahkemeKararResponse>, IMakosRequestProcessor<?, ?>> requestProcessorMap = processorsByRequest.get(request.getClass());
        if (requestProcessorMap != null){
            IMakosRequestProcessor<?, ?> processor = requestProcessorMap.get(responseType);
            if (processor != null)
                return (IMakosRequestProcessor<T, R>) requestProcessorMap.get(responseType);
        }

        throw new RuntimeException("No processor found for <" + request.getClass().getSimpleName() + ", " + responseType.getSimpleName() + ">");
    }

    @SuppressWarnings("unchecked")
    public <T extends MahkemeKararRequest, R extends MahkemeKararResponse> IMakosRequestProcessor<T, R> getProcessor(Class<T> requestType, Class<R> responseType) {
        Map<Class<? extends MahkemeKararResponse>, IMakosRequestProcessor<?, ?>> requestProcessorMap = processorsByRequest.get(requestType);
        if (requestProcessorMap != null){
            IMakosRequestProcessor<?, ?> processor = requestProcessorMap.get(responseType);
            if (processor != null)
                return (IMakosRequestProcessor<T, R>) requestProcessorMap.get(responseType);
        }

        throw new RuntimeException("No processor found for <" + requestType.getSimpleName() + ", " + responseType.getSimpleName() + ">");
    }

    public static void main(String[] args) {
        List<IMakosRequestProcessor<?, ?>> requestProcessors = new ArrayList<>();
        requestProcessors.add(new IDHedefGuncellemeRequestProcessor());

        ProcessorFactory factory = new ProcessorFactory(requestProcessors);

        IMakosRequestProcessor<IDHedefGuncellemeRequest, IDHedefGuncellemeResponse> processor = factory.getProcessor(IDHedefGuncellemeRequest.class, IDHedefGuncellemeResponse.class);
        System.out.println(processor.getClass());

        IDHedefGuncellemeRequest request = new IDHedefGuncellemeRequest();
        processor = factory.getProcessor(request, IDHedefGuncellemeResponse.class);
        System.out.println(processor.getClass());

    }
}