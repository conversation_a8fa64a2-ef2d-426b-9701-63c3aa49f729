package iym.makos.mapper;

import iym.common.model.entity.iym.Gorevler2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.model.dto.db.Gorevler2DTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class Gorevler2MapperTest {

    private Gorevler2Mapper gorevler2Mapper;
    private Gorevler2 gorevler2;
    private Gorevler2DTO gorevler2DTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        gorevler2Mapper = new Gorevler2Mapper();
        testDate = new Date();

        gorevler2 = Gorevler2.builder()
                .gorev("Yönetici")
                .gorevKodu(1L)
                .gorevImzaAdi("Yönetici İmza")
                .imzaYetki("E")
                .silindi(0L)
                .gorevKodu2("YON")
                .gorevTipi("Yönetim")
                .oncelik(1L)
                .baslamaTarihi(testDate)
                .bitisTarihi(testDate)
                .build();

        gorevler2DTO = Gorevler2DTO.builder()
                .gorev("Yönetici")
                .gorevKodu(1L)
                .gorevImzaAdi("Yönetici İmza")
                .imzaYetki("E")
                .silindi(0L)
                .gorevKodu2("YON")
                .gorevTipi("Yönetim")
                .oncelik(1L)
                .baslamaTarihi(testDate)
                .bitisTarihi(testDate)
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        Gorevler2DTO result = gorevler2Mapper.toDto(gorevler2);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getGorev()).isEqualTo(gorevler2.getGorev());
        assertThat(result.getGorevKodu()).isEqualTo(gorevler2.getGorevKodu());
        assertThat(result.getGorevImzaAdi()).isEqualTo(gorevler2.getGorevImzaAdi());
        assertThat(result.getImzaYetki()).isEqualTo(gorevler2.getImzaYetki());
        assertThat(result.getSilindi()).isEqualTo(gorevler2.getSilindi());
        assertThat(result.getGorevKodu2()).isEqualTo(gorevler2.getGorevKodu2());
        assertThat(result.getGorevTipi()).isEqualTo(gorevler2.getGorevTipi());
        assertThat(result.getOncelik()).isEqualTo(gorevler2.getOncelik());
        assertThat(result.getBaslamaTarihi()).isEqualTo(gorevler2.getBaslamaTarihi());
        assertThat(result.getBitisTarihi()).isEqualTo(gorevler2.getBitisTarihi());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        Gorevler2DTO result = gorevler2Mapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        Gorevler2 result = gorevler2Mapper.toEntity(gorevler2DTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getGorev()).isEqualTo(gorevler2DTO.getGorev());
        assertThat(result.getGorevKodu()).isEqualTo(gorevler2DTO.getGorevKodu());
        assertThat(result.getGorevImzaAdi()).isEqualTo(gorevler2DTO.getGorevImzaAdi());
        assertThat(result.getImzaYetki()).isEqualTo(gorevler2DTO.getImzaYetki());
        assertThat(result.getSilindi()).isEqualTo(gorevler2DTO.getSilindi());
        assertThat(result.getGorevKodu2()).isEqualTo(gorevler2DTO.getGorevKodu2());
        assertThat(result.getGorevTipi()).isEqualTo(gorevler2DTO.getGorevTipi());
        assertThat(result.getOncelik()).isEqualTo(gorevler2DTO.getOncelik());
        assertThat(result.getBaslamaTarihi()).isEqualTo(gorevler2DTO.getBaslamaTarihi());
        assertThat(result.getBitisTarihi()).isEqualTo(gorevler2DTO.getBitisTarihi());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        Gorevler2 result = gorevler2Mapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        Gorevler2 existingEntity = Gorevler2.builder()
                .gorev("Yönetici")
                .gorevKodu(1L)
                .gorevImzaAdi("Yönetici İmza")
                .imzaYetki("E")
                .silindi(0L)
                .gorevKodu2("YON")
                .gorevTipi("Yönetim")
                .oncelik(1L)
                .baslamaTarihi(testDate)
                .bitisTarihi(testDate)
                .build();

        Gorevler2DTO updatedDto = Gorevler2DTO.builder()
                .gorev("Müdür") // Should not be updated
                .gorevKodu(2L) // Should not be updated
                .gorevImzaAdi("Müdür İmza")
                .imzaYetki("H")
                .silindi(1L)
                .gorevKodu2("MUD")
                .gorevTipi("Müdürlük")
                .oncelik(2L)
                .baslamaTarihi(new Date(testDate.getTime() + 86400000)) // +1 day
                .bitisTarihi(new Date(testDate.getTime() + 172800000)) // +2 days
                .build();

        // When
        Gorevler2 result = gorevler2Mapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getGorev()).isEqualTo("Yönetici"); // Should not be updated
        assertThat(result.getGorevKodu()).isEqualTo(1L); // Should not be updated
        assertThat(result.getGorevImzaAdi()).isEqualTo("Müdür İmza");
        assertThat(result.getImzaYetki()).isEqualTo("H");
        assertThat(result.getSilindi()).isEqualTo(1L);
        assertThat(result.getGorevKodu2()).isEqualTo("MUD");
        assertThat(result.getGorevTipi()).isEqualTo("Müdürlük");
        assertThat(result.getOncelik()).isEqualTo(2L);
        assertThat(result.getBaslamaTarihi()).isEqualTo(updatedDto.getBaslamaTarihi());
        assertThat(result.getBitisTarihi()).isEqualTo(updatedDto.getBitisTarihi());
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        Gorevler2 existingEntity = Gorevler2.builder()
                .gorev("Yönetici")
                .gorevKodu(1L)
                .gorevImzaAdi("Yönetici İmza")
                .imzaYetki("E")
                .silindi(0L)
                .gorevKodu2("YON")
                .gorevTipi("Yönetim")
                .oncelik(1L)
                .baslamaTarihi(testDate)
                .bitisTarihi(testDate)
                .build();

        // When
        Gorevler2 result = gorevler2Mapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        Gorevler2 result = gorevler2Mapper.updateEntityFromDto(null, gorevler2DTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<Gorevler2> entityList = Arrays.asList(gorevler2, gorevler2);

        // When
        List<Gorevler2DTO> result = gorevler2Mapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getGorev()).isEqualTo(gorevler2.getGorev());
        assertThat(result.get(1).getGorev()).isEqualTo(gorevler2.getGorev());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<Gorevler2DTO> result = gorevler2Mapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<Gorevler2DTO> dtoList = Arrays.asList(gorevler2DTO, gorevler2DTO);

        // When
        List<Gorevler2> result = gorevler2Mapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getGorev()).isEqualTo(gorevler2DTO.getGorev());
        assertThat(result.get(1).getGorev()).isEqualTo(gorevler2DTO.getGorev());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<Gorevler2> result = gorevler2Mapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
