-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HTS_HEDEFLER_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HTS_HEDEFLER_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HTS_HEDEFLER_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create HTS_HEDEFLER_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HTS_HEDEFLER_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.HTS_HEDEFLER_TALEP (
      ID NUMBER NOT NULL,
      MAHKEME_KARAR_ID NUMBER NOT NULL,
      HEDEF_NO VARCHAR2(100 BYTE) NOT NULL,
      KARSI_HEDEF_NO VARCHAR2(100 BYTE),
      SORGU_TIPI VARCHAR2(100 BYTE) NOT NULL,
      BASLANGIC_TARIHI DATE,
      BITIS_TARIHI DATE,
      TESPIT_TURU VARCHAR2(100 BYTE) NOT NULL,
      KULLANICI_ID VARCHAR2(10 BYTE) NOT NULL,
      DURUMU VARCHAR2(100 BYTE),
      CONSTRAINT PK_HTS_HEDEF_ID PRIMARY KEY (ID) ENABLE
    )';
    
    -- Create indexes
    EXECUTE IMMEDIATE 'CREATE INDEX iym.HTS_HEDEF_NO_IDX ON iym.HTS_HEDEFLER_TALEP (HEDEF_NO ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.HTS_MAHKEME_KARAR_ID_IDX ON iym.HTS_HEDEFLER_TALEP (MAHKEME_KARAR_ID ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.HTS_HEDEFLER_TALEP;
  IF row_count = 0 THEN
    -- Make sure we have hts_mahkeme_karar_talep records
    DECLARE
      hts_mahkeme_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO hts_mahkeme_count FROM iym.HTS_MAHKEME_KARAR_TALEP;
      
      IF hts_mahkeme_count > 0 THEN
        -- Get the hts_mahkeme_karar_talep records
        FOR hts_mahkeme_rec IN (
          SELECT h.ID as mahkeme_id
          FROM iym.HTS_MAHKEME_KARAR_TALEP h
        ) LOOP
          -- Sample data 1 - Telefon hedefi
          INSERT INTO iym.HTS_HEDEFLER_TALEP (
            ID, MAHKEME_KARAR_ID, HEDEF_NO, KARSI_HEDEF_NO, SORGU_TIPI,
            BASLANGIC_TARIHI, BITIS_TARIHI, TESPIT_TURU, KULLANICI_ID, DURUMU
          ) VALUES (
            iym.HTS_HEDEFLER_TALEP_SEQ.NEXTVAL, hts_mahkeme_rec.mahkeme_id, '5551234567', NULL, 'ARAMA',
            SYSDATE - 30, SYSDATE, 'DETAYLI', '1', 'AKTIF'
          );
          
          -- Sample data 2 - Telefon hedefi (karşı numara ile)
          INSERT INTO iym.HTS_HEDEFLER_TALEP (
            ID, MAHKEME_KARAR_ID, HEDEF_NO, KARSI_HEDEF_NO, SORGU_TIPI,
            BASLANGIC_TARIHI, BITIS_TARIHI, TESPIT_TURU, KULLANICI_ID, DURUMU
          ) VALUES (
            iym.HTS_HEDEFLER_TALEP_SEQ.NEXTVAL, hts_mahkeme_rec.mahkeme_id, '5551234567', '5559876543', 'ARAMA',
            SYSDATE - 30, SYSDATE, 'DETAYLI', '1', 'AKTIF'
          );
          
          -- Sample data 3 - Telefon hedefi (SMS)
          INSERT INTO iym.HTS_HEDEFLER_TALEP (
            ID, MAHKEME_KARAR_ID, HEDEF_NO, KARSI_HEDEF_NO, SORGU_TIPI,
            BASLANGIC_TARIHI, BITIS_TARIHI, TESPIT_TURU, KULLANICI_ID, DURUMU
          ) VALUES (
            iym.HTS_HEDEFLER_TALEP_SEQ.NEXTVAL, hts_mahkeme_rec.mahkeme_id, '5551234567', NULL, 'SMS',
            SYSDATE - 30, SYSDATE, 'DETAYLI', '1', 'AKTIF'
          );
        END LOOP;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
