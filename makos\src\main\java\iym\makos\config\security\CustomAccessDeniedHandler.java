package iym.makos.config.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.util.EnvironmentUtil;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.mahkemekarar.MahkemeKararResponse;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;

@Component
@Slf4j
public class CustomAccessDeniedHandler implements AccessDeniedHandler {

    private final EnvironmentUtil environmentUtil;

    public CustomAccessDeniedHandler(EnvironmentUtil environmentUtil) {
        this.environmentUtil = environmentUtil;
    }

    @Override
    public void handle(HttpServletRequest request,
                       HttpServletResponse response,
                       AccessDeniedException accessDeniedException)
            throws IOException, ServletException {

        response.setStatus(HttpServletResponse.SC_FORBIDDEN); // 403
        response.setContentType("application/json");

        String message = "Access Denied: You do not have the required role to access this resource";

        // Create MahkemeKararResponse for consistent response format
        MahkemeKararResponse responseBody = MahkemeKararResponse.builder()
                .requestId(UUID.randomUUID())
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.FAILED)
                        .responseMessage(message)
                        .build())
                .build();

        // Always log full details for debugging
        log.error("Access Denied at filter level. uri:{}, user:{}, error:{}",
                request.getServletPath(),
                getCurrentUser(request),
                accessDeniedException.getMessage(),
                accessDeniedException);

        final ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(response.getOutputStream(), responseBody);
        response.getOutputStream().flush();
    }

    /**
     * Get current authenticated user for logging purposes
     */
    private String getCurrentUser(HttpServletRequest request) {
        try {
            return org.springframework.security.core.context.SecurityContextHolder
                    .getContext()
                    .getAuthentication()
                    .getName();
        } catch (Exception e) {
            return "anonymous";
        }
    }
}
