package iym.common.testcontainer;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Test class for SqlPlusToJdbcConverter
 */
class SqlPlusToJdbcConverterTest {

    private SqlPlusToJdbcConverter converter;

    @BeforeEach
    void setUp() {
        converter = new SqlPlusToJdbcConverter();
    }

    @Test
    @DisplayName("Should parse CREATE SEQUENCE statements as separate statements")
    void shouldParseCreateSequenceStatementsAsSeparateStatements() {
        // Given - SQL content with multiple CREATE SEQUENCE statements
        String sqlContent = """
            BEGIN
              DBMS_OUTPUT.PUT_LINE('=== STARTING: 01.00_create_schema.sql ===');
              DBMS_OUTPUT.PUT_LINE('Creating tablespace, user, and granting privileges...');
            END;
            /
            CREATE SEQUENCE iym.EVRAK_KAYIT_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            CREATE SEQUENCE iym.KULLANICILAR_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            CREATE SEQUENCE iym.KULLANICI_GOREV2_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            CREATE SEQUENCE iym.EVRAK_GELEN_KURUMLAR_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            CREATE SEQUENCE iym.KULLANICI_KURUM_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            BEGIN
              DBMS_OUTPUT.PUT_LINE('=== COMPLETED: 01.00_create_schema.sql ===');
              DBMS_OUTPUT.PUT_LINE('Schema, user, and sequences created successfully');
            END;
            /
            """;

        // When - Parse the SQL content
        List<String> statements = converter.convertToJdbcStatements(sqlContent);

        // Then - Should have 7 statements (1 initial BEGIN block + 5 CREATE SEQUENCE + 1 final BEGIN block)
        assertThat(statements).hasSize(7);

        // Verify the first statement is the initial BEGIN block
        assertThat(statements.get(0)).contains("DBMS_OUTPUT.PUT_LINE('=== STARTING: 01.00_create_schema.sql ===')");

        // Verify each CREATE SEQUENCE statement is separate
        assertThat(statements.get(1)).startsWith("CREATE SEQUENCE iym.EVRAK_KAYIT_SEQ");
        assertThat(statements.get(1)).contains("NOCYCLE");
        assertThat(statements.get(1)).doesNotContain("CREATE SEQUENCE iym.KULLANICILAR_SEQ");

        assertThat(statements.get(2)).startsWith("CREATE SEQUENCE iym.KULLANICILAR_SEQ");
        assertThat(statements.get(2)).contains("NOCYCLE");
        assertThat(statements.get(2)).doesNotContain("CREATE SEQUENCE iym.KULLANICI_GOREV2_SEQ");

        assertThat(statements.get(3)).startsWith("CREATE SEQUENCE iym.KULLANICI_GOREV2_SEQ");
        assertThat(statements.get(3)).contains("NOCYCLE");
        assertThat(statements.get(3)).doesNotContain("CREATE SEQUENCE iym.EVRAK_GELEN_KURUMLAR_SEQ");

        assertThat(statements.get(4)).startsWith("CREATE SEQUENCE iym.EVRAK_GELEN_KURUMLAR_SEQ");
        assertThat(statements.get(4)).contains("NOCYCLE");
        assertThat(statements.get(4)).doesNotContain("CREATE SEQUENCE iym.KULLANICI_KURUM_SEQ");

        assertThat(statements.get(5)).startsWith("CREATE SEQUENCE iym.KULLANICI_KURUM_SEQ");
        assertThat(statements.get(5)).contains("NOCYCLE");
        assertThat(statements.get(5)).doesNotContain("BEGIN");

        // Verify the last statement is the final BEGIN block
        assertThat(statements.get(6)).contains("DBMS_OUTPUT.PUT_LINE('=== COMPLETED: 01.00_create_schema.sql ===')");
    }

    @Test
    @DisplayName("Should handle simple CREATE SEQUENCE statements")
    void shouldHandleSimpleCreateSequenceStatements() {
        // Given - Simple CREATE SEQUENCE statements
        String sqlContent = """
            CREATE SEQUENCE seq1 START WITH 1;
            CREATE SEQUENCE seq2 START WITH 10;
            """;

        // When - Parse the SQL content
        List<String> statements = converter.convertToJdbcStatements(sqlContent);

        // Then - Should have 2 separate statements
        assertThat(statements).hasSize(2);
        assertThat(statements.get(0)).isEqualTo("CREATE SEQUENCE seq1 START WITH 1");
        assertThat(statements.get(1)).isEqualTo("CREATE SEQUENCE seq2 START WITH 10");
    }
}
