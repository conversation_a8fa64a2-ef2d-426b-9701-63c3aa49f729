package iym.makos.service;

import iym.common.enums.MakosUserAuditType;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Transaction Test Service for Oracle TestContainer Integration Tests
 * <p>
 * This service demonstrates and tests transactional behavior using both:
 * 1. Built-in JPA repository methods (save, findById, etc.)
 * 2. Custom dynamic query methods (insertMahkemeKararTalepLog, etc.)
 * <p>
 * Test Scenarios:
 * - SUCCESS: Both JPA and custom query operations succeed
 * - FAILURE: JPA succeeds, custom query fails -> rollback
 * - EXPLICIT ROLLBACK: Manual rollback testing
 * - MULTI-TABLE: Complex multi-table transaction scenarios
 * <p>
 * Uses real Oracle database via TestContainers for authentic transactional behavior.
 *
 * <AUTHOR> Team
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionTestService {

    private final MahkemeKararTalepRepo mahkemeKararTalepRepo;

    /**
     * SUCCESS SCENARIO: Both JPA save and custom insert succeed
     *
     * @param testData Test entity data
     * @return Saved entity ID
     */
    @Transactional
    public Long successScenario(MahkemeKararTalep testData) {
        log.info("🟢 SUCCESS SCENARIO starting...");

        // 1. Use built-in JPA method (save)
        MahkemeKararTalep saved = mahkemeKararTalepRepo.save(testData);
        log.info("✅ JPA save successful - ID: {}", saved.getId());

        // 2. Use custom dynamic query method (insertMahkemeKararTalepLog)
        Long logId = mahkemeKararTalepRepo.testInsertUserAuditLog(MakosUserAuditType.LOGOUT);
        log.info("✅ Custom insert successful - Log ID: {}", logId);

        log.info("🟢 SUCCESS SCENARIO completed - Entity ID: {}, Log ID: {}", saved.getId(), logId);
        return saved.getId();
    }

    /**
     * FAILURE SCENARIO: JPA save succeeds, custom insert fails -> rollback
     *
     * @param testData Test entity data
     * @throws RuntimeException Simulated database error
     */
    @Transactional
    public void failureScenario(MahkemeKararTalep testData) {
        log.info("🔴 FAILURE SCENARIO starting...");

        // 1. Use built-in JPA method (save) - SUCCEEDS
        MahkemeKararTalep saved = mahkemeKararTalepRepo.save(testData);
        log.info("✅ JPA save successful - ID: {}", saved.getId());

        // 2. Use custom dynamic query method - SUCCEEDS
        Long logId = mahkemeKararTalepRepo.testInsertUserAuditLog(MakosUserAuditType.LOGOUT);
        log.info("✅ Custom insert successful - Log ID: {}", logId);

        // 3. Simulate database error - This should roll back everything
        log.error("💥 Simulating database error...");
        throw new RuntimeException("💥 FAILURE SCENARIO - Simulated database error!");
    }

    /**
     * EXPLICIT ROLLBACK SCENARIO: Manual rollback testing
     *
     * @param testData Test entity data
     * @throws RuntimeException Explicit rollback trigger
     */
    @Transactional(rollbackFor = Exception.class)
    public void explicitRollbackScenario(MahkemeKararTalep testData) {
        log.info("🔄 EXPLICIT ROLLBACK SCENARIO starting...");

        // 1. Use built-in JPA method
        MahkemeKararTalep saved = mahkemeKararTalepRepo.save(testData);
        log.info("✅ JPA save successful - ID: {}", saved.getId());

        // 2. Use custom dynamic query method
        Long logId = mahkemeKararTalepRepo.testInsertUserAuditLog(MakosUserAuditType.LOGOUT);
        log.info("✅ Custom insert successful - Log ID: {}", logId);

        // 3. Trigger explicit rollback
        log.warn("🔄 Triggering explicit rollback...");
        throw new RuntimeException("🔄 EXPLICIT ROLLBACK - Manual rollback test");
    }

    /**
     * MULTI-TABLE SCENARIO: Complex multi-table transaction
     *
     * @param testDataList List of test entities
     * @return Number of successfully processed entities
     */
    @Transactional
    public int multiTableScenario(List<MahkemeKararTalep> testDataList) {
        log.info("📦 MULTI-TABLE SCENARIO starting - {} entities", testDataList.size());

        int successCount = 0;
        for (MahkemeKararTalep testData : testDataList) {
            // 1. Use built-in JPA method
            MahkemeKararTalep saved = mahkemeKararTalepRepo.save(testData);
            log.debug("✅ JPA save successful - ID: {}", saved.getId());

            // 2. Use custom dynamic query method
            Long logId = mahkemeKararTalepRepo.testInsertUserAuditLog(MakosUserAuditType.LOGOUT);
            log.debug("✅ Custom insert successful - Log ID: {}", logId);

            successCount++;
        }

        log.info("📦 MULTI-TABLE SCENARIO completed - {} entities processed", successCount);
        return successCount;
    }

    /**
     * READ-ONLY SCENARIO: Test read-only transaction behavior
     *
     * @param entityId Entity ID to read
     * @return Information about the entity
     */
    @Transactional(readOnly = true)
    public String readOnlyScenario(Long entityId) {
        log.info("📖 READ-ONLY SCENARIO starting - Entity ID: {}", entityId);

        // 1. Use built-in JPA method (findById)
        boolean entityExists = mahkemeKararTalepRepo.findById(entityId).isPresent();
        log.info("📖 Entity exists: {}", entityExists);

        // 2. Count operations
        long totalCount = mahkemeKararTalepRepo.count();
        log.info("📖 Total entity count: {}", totalCount);

        String result = String.format("Entity ID %d exists: %s, Total count: %d",
                entityId, entityExists, totalCount);
        log.info("📖 READ-ONLY SCENARIO completed: {}", result);
        return result;
    }

    /**
     * Create test data helper method
     *
     * @return Test entity
     */
    public MahkemeKararTalep createTestData() {
        long timestamp = System.currentTimeMillis();

        // Ensure mahkemeKodu is between 1-10 characters (not empty)
        String mahkemeKodu = "MK_" + (int) (Math.random() * 9999999); //  Max 10 chars total

        return MahkemeKararTalep.builder()
                .evrakId(1L) // Using existing evrak from init script
                .kullaniciId(1L) // Using existing user from init script
                .kayitTarihi(new Date())
                .durum("TEST")
                .hukukBirim("SULH CEZA")
                .kararTip("300")
                .mahkemeIlIlceKodu("0600")
                .sorusturmaNo("TX-TEST-" + mahkemeKodu + "-" + (timestamp % 100000))
                .mahkemeKararNo("TX-" + mahkemeKodu + "-" + (timestamp % 100000))
                .mahkemeKodu(mahkemeKodu)
                .mahkemeAdi("Transaction Test Mahkeme " + mahkemeKodu)
                .aciklama("Transaction test data for " + mahkemeKodu + " scenario")
                .build();
    }

    /**
     * Create multiple test data entities
     *
     * @param count Number of entities to create
     * @return List of test entities
     */
    public List<MahkemeKararTalep> createMultipleTestData(int count) {
        return java.util.stream.IntStream.range(1, count + 1)
                .mapToObj(i -> createTestData())
                .collect(java.util.stream.Collectors.toList());
    }
}
