<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>backend</artifactId>

    <parent>
        <groupId>iym</groupId>
        <artifactId>iym</artifactId>
        <version>1.0</version>
    </parent>

    <properties>
        <jjwt.version>0.11.2</jjwt.version>
        <project.main.class>iym.backend.Application</project.main.class>
        <generated-sources-path>${project.build.directory}/generated-sources/openapi</generated-sources-path>
        <generated-sources-java-path>src/main/java</generated-sources-java-path>
    </properties>

    <dependencies>
        <dependency>
            <groupId>io.github.perplexhub</groupId>
            <artifactId>rsql-jpa-spring-boot-starter</artifactId>
            <version>6.0.21</version>
        </dependency>
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jjwt.version}</version>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>3.1.5</version>
        </dependency>

        <!--
		The Swagger UI page is available at http://server:port/context-path/swagger-ui/index.html
		The OpenAPI description is available at http://server:port/context-path/v3/api-docs (json format)
		The OpenAPI description is available at http://server:port/context-path/v3/api-docs.yaml (yaml format)
		-->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>iym</groupId>
            <artifactId>common</artifactId>
            <version>1.0</version>
            <scope>compile</scope>
        </dependency>


        <!-- PostgreSQL Driver for Multi-Database Support -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Flyway (DB migration) -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>

        <!-- H2 Database for PostgreSQL Testing -->
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- MapStruct for PostgreSQL Entity-DTO Mapping -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>1.5.5.Final</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>1.5.5.Final</version>
            <scope>provided</scope>
        </dependency>

        <!-- OpenAPI Generator Dependencies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>0.2.6</version>
        </dependency>

        <!-- Apache HttpClient 5 dependencies for RestUtils -->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.core5</groupId>
            <artifactId>httpcore5</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- OpenAPI Generator Maven Plugin -->
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi-generator.version}</version>
                <executions>
                    <execution>
                        <id>generate-makos-api-client</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <skipIfSpecIsUnchanged>false</skipIfSpecIsUnchanged>
                            <inputSpec>${project.basedir}/src/main/resources/makos-api.yaml</inputSpec>
                            <output>${generated-sources-path}</output>
                            <generatorName>java</generatorName>
                            <library>resttemplate</library>

                            <apiPackage>iym.makos.api.client.gen.api</apiPackage>
                            <modelPackage>iym.makos.api.client.gen.model</modelPackage>
                            <invokerPackage>iym.makos.api.client.gen.handler</invokerPackage>

                            <generateApis>true</generateApis>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <generateApiTests>false</generateApiTests>
                            <generateModels>true</generateModels>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateModelTests>false</generateModelTests>
                            <generateSupportingFiles>true</generateSupportingFiles>
                            <addCompileSourceRoot>false</addCompileSourceRoot>

                            <configOptions>
                                <sourceFolder>src/main/java</sourceFolder>
                                <openApiNullable>false</openApiNullable>
                                <hideGenerationTimestamp>true</hideGenerationTimestamp>
                                <useJakartaEe>true</useJakartaEe>
                                <dateLibrary>java8-localdatetime</dateLibrary>
                            </configOptions>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Resources Plugin - Generated kodları src/main/java/generated'a kopyalamak için -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <executions>
                    <execution>
                        <id>copy-generated-sources</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.basedir}/src/main/generated</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${generated-sources-path}/${generated-sources-java-path}</directory>
                                    <includes>
                                        <include>**/*.java</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Build Helper Maven Plugin - Generated source'ları compile path'e eklemek için -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.4.0</version>
                <executions>
                    <execution>
                        <id>add-generated-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.basedir}/src/main/generated</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- maven-antrun-plugin - Generated source'ları target dizininden silmek için (fazlalıkları) -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>delete-openapi-temp</id>
                        <phase>compile</phase> <!-- veya prepare-package -->
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <echo message="Deleting generated OpenAPI temp directory: ${generated-sources-path}"/>
                                <delete dir="${generated-sources-path}" failonerror="false" />
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


            <!-- Maven Compiler Plugin with Annotation Processors -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.38</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.5.5.Final</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>3.0.5</version>
                <configuration>
                    <mainClass>${project.main.class}</mainClass>
                </configuration>

                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Flyway Maven Plugin -->
            <plugin>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-maven-plugin</artifactId>
                <configuration>
                    <url>****************************************</url>
                    <user>demo_user</user>
                    <password>demo_password</password>
                    <locations>
                        <location>filesystem:src/main/resources/db/migration</location>
                    </locations>
                    <baselineOnMigrate>true</baselineOnMigrate>
                    <validateOnMigrate>true</validateOnMigrate>
                    <cleanDisabled>true</cleanDisabled>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.postgresql</groupId>
                        <artifactId>postgresql</artifactId>
                        <version>42.5.4</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>