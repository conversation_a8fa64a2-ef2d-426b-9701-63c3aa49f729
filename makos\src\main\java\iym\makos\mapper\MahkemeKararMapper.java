package iym.makos.mapper;

import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.makos.model.dto.db.MahkemeKararDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for MahkemeKarar entity and DTO
 */
@Component
public class MahkemeKararMapper {

    /**
     * Convert entity to DTO
     * @param entity MahkemeKararTalep entity
     * @return MahkemeKararTalepDTO
     */
    public MahkemeKararDTO toDto(MahkemeKarar entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeKararDTO.builder()
                .id(entity.getId())
                .evrakId(entity.getEvrakId())
                .kullaniciId(entity.getKullaniciId())
                .kayitTarihi(entity.getKayitTarihi())
                .durum(entity.getDurum())
                .hukukBirim(entity.getHukukBirim())
                .kararTip(entity.getKararTip())
                .mahKararBasTar(entity.getMahKararBasTar())
                .mahKararBitisTar(entity.getMahKararBitisTar())
                .mahkemeAdi(entity.getMahkemeAdi())
                .mahkemeKararNo(entity.getMahkemeKararNo())
                .mahkemeIlIlceKodu(entity.getMahkemeIlIlceKodu())
                .aciklama(entity.getAciklama())
                .hakimSicilNo(entity.getHakimSicilNo())
                .sorusturmaNo(entity.getSorusturmaNo())
                .mahkemeKodu(entity.getMahkemeKodu())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto MahkemeKararTalepDTO
     * @return MahkemeKararTalep entity
     */
    public MahkemeKarar toEntity(MahkemeKararDTO dto) {
        if (dto == null) {
            return null;
        }

        return MahkemeKarar.builder()
                .id(dto.getId())
                .evrakId(dto.getEvrakId())
                .kullaniciId(dto.getKullaniciId())
                .kayitTarihi(dto.getKayitTarihi())
                .durum(dto.getDurum())
                .hukukBirim(dto.getHukukBirim())
                .kararTip(dto.getKararTip())
                .mahKararBasTar(dto.getMahKararBasTar())
                .mahKararBitisTar(dto.getMahKararBitisTar())
                .mahkemeAdi(dto.getMahkemeAdi())
                .mahkemeKararNo(dto.getMahkemeKararNo())
                .mahkemeIlIlceKodu(dto.getMahkemeIlIlceKodu())
                .aciklama(dto.getAciklama())
                .hakimSicilNo(dto.getHakimSicilNo())
                .sorusturmaNo(dto.getSorusturmaNo())
                .mahkemeKodu(dto.getMahkemeKodu())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity MahkemeKararTalep entity to update
     * @param dto MahkemeKararTalepDTO with new values
     * @return Updated MahkemeKararTalep entity
     */
    public MahkemeKarar updateEntityFromDto(MahkemeKarar entity, MahkemeKararDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setEvrakId(dto.getEvrakId());
        entity.setKullaniciId(dto.getKullaniciId());
        entity.setKayitTarihi(dto.getKayitTarihi());
        entity.setDurum(dto.getDurum());
        entity.setHukukBirim(dto.getHukukBirim());
        entity.setKararTip(dto.getKararTip());
        entity.setMahKararBasTar(dto.getMahKararBasTar());
        entity.setMahKararBitisTar(dto.getMahKararBitisTar());
        entity.setMahkemeAdi(dto.getMahkemeAdi());
        entity.setMahkemeKararNo(dto.getMahkemeKararNo());
        entity.setMahkemeIlIlceKodu(dto.getMahkemeIlIlceKodu());
        entity.setAciklama(dto.getAciklama());
        entity.setHakimSicilNo(dto.getHakimSicilNo());
        entity.setSorusturmaNo(dto.getSorusturmaNo());
        entity.setMahkemeKodu(dto.getMahkemeKodu());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entities List of MahkemeKararTalep entities
     * @return List of MahkemeKararTalepDTO
     */
    public List<MahkemeKararDTO> toDtoList(List<MahkemeKarar> entities) {
        if (entities == null) {
            return List.of();
        }

        return entities.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
