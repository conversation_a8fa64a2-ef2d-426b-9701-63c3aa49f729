package iym.makos.validator;

import iym.common.enums.HedefTip;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.api.Hedef;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.testdata.TestDataBuilder;
import iym.makos.model.api.HedefGuncellemeAlan;
import iym.makos.model.api.HedefGuncellemeBilgi;
import iym.makos.model.api.HedefGuncellemeDetay;
import iym.makos.model.api.HedefGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mahkemekarar.id.IDHedefGuncellemeRequest;
import iym.makos.validator.MahkemeKararRequestCommonValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDHedefGuncellemeValidator.
 *
 * Tests validation logic for ID hedef güncelleme (target update) requests.
 * Uses LENIENT mock settings for flexibility.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
class IDHedefGuncellemeValidatorTest extends BaseDomainUnitTest {

    @Mock
    private DbMahkemeKararService mockDbMahkemeKararService;

    @Mock
    private DbHedeflerService mockDbHedeflerService;

    @Mock
    private MahkemeKararRequestCommonValidator mockCommonValidator;

    @InjectMocks
    private IDHedefGuncellemeValidator validator;

    private IDHedefGuncellemeRequest validRequest;
    private MahkemeKararDetay validMahkemeKararDetay;
    private HedefGuncellemeDetay validHedefGuncellemeDetay;
    private MahkemeKarar mockMahkemeKarar;
    private Hedefler mockHedefler;

    @BeforeEach
    void setUp() throws Exception {
        // Setup valid test data
        validMahkemeKararDetay = MahkemeKararDetay.builder()
                .mahkemeIlIlceKodu("34001")
                .mahkemeKodu("001")
                .mahkemeKararNo("2023/123")
                .sorusturmaNo("2023/456")
                .build();

        validHedefGuncellemeDetay = HedefGuncellemeDetay.builder()
                .hedef(Hedef.builder()
                        .hedefNo("H001")
                        .hedefTip(HedefTip.GSM)
                        .build())
                .hedefGuncellemeBilgiListesi(Arrays.asList(
                        HedefGuncellemeBilgi.builder()
                                .hedefGuncellemeAlan(HedefGuncellemeAlan.AD)
                                .yeniDegeri("Yeni Ad")
                                .build()
                ))
                .build();

        validRequest = createTestRequest(Arrays.asList(validHedefGuncellemeDetay));

        // Setup mock entities
        mockMahkemeKarar = new MahkemeKarar();
        mockMahkemeKarar.setId(1L);

        mockHedefler = new Hedefler();
        mockHedefler.setId(1L);
        mockHedefler.setHedefAdi("Eski Ad");
        mockHedefler.setHedefSoyadi("Eski Soyad");
        mockHedefler.setCanakNo("ESKI_CANAK");
        mockHedefler.setTcKimlikNo("11111111111");

        // Setup common validator mock
        when(mockCommonValidator.validate(any())).thenReturn(new ValidationResult(true));
    }

    @Test
    @DisplayName("Should pass validation when request is valid")
    void shouldPassValidation_whenRequestIsValid() throws Exception {
        // Given
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertTrue(result.isValid());
        assertThat(result.getReasons()).isEmpty();
    }

    @Test
    @DisplayName("Should fail validation when related mahkeme karar not found")
    void shouldFailValidation_whenRelatedMahkemeKararNotFound() throws Exception {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.empty());

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("Mahkeme Karar Bulunamadı: Mahkeme İl/İlçe Kodu: 34001 Mahkeme Kodu: 001, Karar No: 2023/123, Soruşturma No :2023/456");
    }

    @Test
    @DisplayName("Should fail validation when hedef not found")
    void shouldFailValidation_whenHedefNotFound() throws Exception {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKarar));
        when(mockDbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(anyLong(), anyString(), any(HedefTip.class)))
                .thenReturn(Optional.empty());

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("H001 numaralı hedef  ilişkli mahkeme kararda bulunamadı.");
    }

    @Test
    @DisplayName("Should fail validation when no update fields provided")
    void shouldFailValidation_whenNoUpdateFieldsProvided() throws Exception {
        // Given
        HedefGuncellemeDetay emptyUpdateDetay = HedefGuncellemeDetay.builder()
                .hedef(Hedef.builder()
                        .hedefNo("H001")
                        .hedefTip(HedefTip.GSM)
                        .build())
                .hedefGuncellemeBilgiListesi(Collections.emptyList())
                .build();

        validRequest = createTestRequest(Arrays.asList(emptyUpdateDetay));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("H001 için en az bir güncelleme  alanı olmalıdır. ");
    }

    @Test
    @DisplayName("Should fail validation when AD field has same value")
    void shouldFailValidation_whenAdFieldHasSameValue() throws Exception {
        // Given
        HedefGuncellemeDetay sameAdDetay = HedefGuncellemeDetay.builder()
                .hedef(Hedef.builder()
                        .hedefNo("H001")
                        .hedefTip(HedefTip.GSM)
                        .build())
                .hedefGuncellemeBilgiListesi(Arrays.asList(
                        HedefGuncellemeBilgi.builder()
                                .hedefGuncellemeAlan(HedefGuncellemeAlan.AD)
                                .yeniDegeri("Eski Ad") // Same as existing
                                .build()
                ))
                .build();

        validRequest = createTestRequest(Arrays.asList(sameAdDetay));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("H001 numaralı hedefin ad bilgisi eskisi ile aynı.");
    }

    @Test
    @DisplayName("Should fail validation when SOYAD field has same value")
    void shouldFailValidation_whenSoyadFieldHasSameValue() throws Exception {
        // Given
        HedefGuncellemeDetay sameSoyadDetay = HedefGuncellemeDetay.builder()
                .hedef(Hedef.builder()
                        .hedefNo("H001")
                        .hedefTip(HedefTip.GSM)
                        .build())
                .hedefGuncellemeBilgiListesi(Arrays.asList(
                        HedefGuncellemeBilgi.builder()
                                .hedefGuncellemeAlan(HedefGuncellemeAlan.SOYAD)
                                .yeniDegeri("Eski Soyad") // Same as existing
                                .build()
                ))
                .build();

        validRequest = createTestRequest(Arrays.asList(sameSoyadDetay));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("H001 numaralı hedefin soyad bilgisi eskisi ile aynı.");
    }

    @Test
    @DisplayName("Should fail validation when CANAK_NO field has same value")
    void shouldFailValidation_whenCanakNoFieldHasSameValue() throws Exception {
        // Given
        HedefGuncellemeDetay sameCanakDetay = HedefGuncellemeDetay.builder()
                .hedef(Hedef.builder()
                        .hedefNo("H001")
                        .hedefTip(HedefTip.GSM)
                        .build())
                .hedefGuncellemeBilgiListesi(Arrays.asList(
                        HedefGuncellemeBilgi.builder()
                                .hedefGuncellemeAlan(HedefGuncellemeAlan.CANAK_NO)
                                .yeniDegeri("11111111111") // Same as existing TC (bug in implementation)
                                .build()
                ))
                .build();

        validRequest = createTestRequest(Arrays.asList(sameCanakDetay));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("H001 numaralı hedefin CANAK numarası eskisi ile aynı.");
    }

    @Test
    @DisplayName("Should fail validation when TCKIMlIKNO field has same value")
    void shouldFailValidation_whenTcKimlikNoFieldHasSameValue() throws Exception {
        // Given
        HedefGuncellemeDetay sameTcDetay = HedefGuncellemeDetay.builder()
                .hedef(Hedef.builder()
                        .hedefNo("H001")
                        .hedefTip(HedefTip.GSM)
                        .build())
                .hedefGuncellemeBilgiListesi(Arrays.asList(
                        HedefGuncellemeBilgi.builder()
                                .hedefGuncellemeAlan(HedefGuncellemeAlan.TCKIMlIKNO)
                                .yeniDegeri("11111111111") // Same as existing
                                .build()
                ))
                .build();

        validRequest = createTestRequest(Arrays.asList(sameTcDetay));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("H001 numaralı hedefin TC Kimlik numarası eskisi ile aynı.");
    }

    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() throws Exception {
        // When
        KararTuru result = validator.getRelatedKararTuru();

        // Then
        assertEquals(KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME, result);
    }

    @Test
    @DisplayName("Should handle validation exception gracefully")
    void shouldHandleValidationException_gracefully() throws Exception {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }

    private void setupValidMocks() {
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKarar));
        when(mockDbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(anyLong(), anyString(), any(HedefTip.class)))
                .thenReturn(Optional.of(mockHedefler));
    }

    private IDHedefGuncellemeRequest createTestRequest(List<HedefGuncellemeDetay> hedefGuncellemeDetayListesi) {
        return IDHedefGuncellemeRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME)
                .evrakDetay(TestDataBuilder.createValidEvrakDetay())
                .mahkemeKararBilgisi(iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.HEDEF_BILGI_DEGISTIRME)
                        .mahkemeKararDetay(TestDataBuilder.createValidMahkemeKararDetay())
                        .build())
                .hedefGuncellemeKararDetayListesi(Arrays.asList(
                        HedefGuncellemeKararDetay.builder()
                                .mahkemeKararDetay(validMahkemeKararDetay)
                                .hedefGuncellemeDetayListesi(hedefGuncellemeDetayListesi)
                                .build()
                ))
                .build();
    }
}
