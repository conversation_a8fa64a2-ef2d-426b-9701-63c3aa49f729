package iym.makos.model.dto.audit;

import iym.common.model.api.ApiResponseBase;
import iym.common.model.entity.makos.MakosUserAuditLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class GetMakosUserAuditLogResponse extends ApiResponseBase {
    private MakosUserAuditLog auditLog;
} 