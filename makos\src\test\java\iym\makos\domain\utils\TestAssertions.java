package iym.makos.domain.utils;

import iym.common.validation.ValidationResult;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.mahkemekarar.MahkemeKararResponse;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Custom assertion utilities for MAKOS domain tests.
 * 
 * This class provides domain-specific assertion methods
 * to make tests more readable and maintainable.
 * 
 * Usage:
 * - Import static methods for fluent assertions
 * - Use domain-specific assertion methods
 * - Combine with AssertJ for comprehensive testing
 * 
 * <AUTHOR> Team
 */
public class TestAssertions {
    
    /**
     * Asserts that a ValidationResult is valid
     */
    public static void assertValidationSuccess(ValidationResult result) {
        assertThat(result).isNotNull();
        assertThat(result.isValid()).isTrue();
        assertThat(result.getReasons()).isEmpty();
    }

    /**
     * Creates a valid ValidationResult
     */
    public static ValidationResult validResult() {
        return new ValidationResult(true);
    }
    
    /**
     * Asserts that a ValidationResult is invalid with specific reason
     */
    public static void assertValidationFailure(ValidationResult result, String expectedReason) {
        assertThat(result).isNotNull();
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons()).contains(expectedReason);
    }
    
    /**
     * Asserts that a ValidationResult is invalid with any reason
     */
    public static void assertValidationFailure(ValidationResult result) {
        assertThat(result).isNotNull();
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons()).isNotEmpty();
    }
    
    /**
     * Asserts that a MahkemeKararResponse has success status
     */
    public static void assertResponseSuccess(MahkemeKararResponse response) {
        assertThat(response).isNotNull();
        assertThat(response.getResponse()).isNotNull();
        assertThat(response.getResponse().getResponseCode()).isEqualTo(MakosResponseCode.SUCCESS);
    }
    
    /**
     * Asserts that a MahkemeKararResponse has specific error status
     */
    public static void assertResponseError(MahkemeKararResponse response, MakosResponseCode expectedCode) {
        assertThat(response).isNotNull();
        assertThat(response.getResponse()).isNotNull();
        assertThat(response.getResponse().getResponseCode()).isEqualTo(expectedCode);
    }
    
    /**
     * Asserts that a MahkemeKararResponse has invalid request status
     */
    public static void assertResponseInvalidRequest(MahkemeKararResponse response) {
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
    }
    
    /**
     * Asserts that a MakosApiResponse has success status
     */
    public static void assertApiResponseSuccess(MakosApiResponse response) {
        assertThat(response).isNotNull();
        assertThat(response.getResponseCode()).isEqualTo(MakosResponseCode.SUCCESS);
    }
    
    /**
     * Asserts that a MakosApiResponse has specific error status
     */
    public static void assertApiResponseError(MakosApiResponse response, MakosResponseCode expectedCode) {
        assertThat(response).isNotNull();
        assertThat(response.getResponseCode()).isEqualTo(expectedCode);
    }
    
    /**
     * Asserts that an evrak ID is valid (positive number)
     */
    public static void assertValidEvrakId(Long evrakId) {
        assertThat(evrakId).isNotNull();
        assertThat(evrakId).isPositive();
    }
    
    /**
     * Asserts that a processor type matches expected class
     */
    public static void assertProcessorType(Object processor, Class<?> expectedType) {
        assertThat(processor).isNotNull();
        assertThat(processor).isInstanceOf(expectedType);
    }
    
    /**
     * Asserts that a validator type matches expected class
     */
    public static void assertValidatorType(Object validator, Class<?> expectedType) {
        assertThat(validator).isNotNull();
        assertThat(validator).isInstanceOf(expectedType);
    }
    
    /**
     * Asserts that a DB handler type matches expected class
     */
    public static void assertDbHandlerType(Object handler, Class<?> expectedType) {
        assertThat(handler).isNotNull();
        assertThat(handler).isInstanceOf(expectedType);
    }
}
