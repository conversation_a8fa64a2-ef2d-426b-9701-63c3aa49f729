package iym.makos.domain.mahkemekarar.processor;

import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.model.dto.mahkemekarar.id.GenelEvrakRequest;
import iym.makos.model.dto.mahkemekarar.it.GenelKararResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

import java.util.Arrays;
import java.util.List;

import static iym.makos.domain.utils.TestAssertions.assertProcessorType;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

/**
 * Unit tests for ProcessorFactory.
 * 
 * Tests the factory pattern implementation for processor creation.
 * Verifies correct processor selection based on request and response types.
 * 
 * <AUTHOR> Team
 */
@DisplayName("ProcessorFactory Unit Tests")
class ProcessorFactoryTest extends BaseDomainUnitTest {
    
    @Mock
    private IMakosRequestProcessor<GenelEvrakRequest, GenelKararResponse> mockGenelProcessor;
    
    @Mock
    private IMakosRequestProcessor<GenelEvrakRequest, GenelKararResponse> mockAnotherProcessor;
    
    private ProcessorFactory processorFactory;
    
    @BeforeEach
    void setUp() {
        // Setup will be done in individual test methods as needed
    }
    
    @Test
    @DisplayName("Should return correct processor when request and response types match")
    void shouldReturnCorrectProcessor_whenRequestAndResponseTypesMatch() {
        // Given - Mock processor configurations
        when(mockGenelProcessor.getRelatedRequestType()).thenReturn(GenelEvrakRequest.class);
        when(mockGenelProcessor.getRelatedResponseType()).thenReturn(GenelKararResponse.class);

        // Create factory with mock processors
        List<IMakosRequestProcessor<?, ?>> processors = Arrays.asList(mockGenelProcessor);
        processorFactory = new ProcessorFactory(processors);

        // When
        IMakosRequestProcessor<GenelEvrakRequest, GenelKararResponse> result =
            processorFactory.getProcessor(GenelEvrakRequest.class, GenelKararResponse.class);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(mockGenelProcessor);
        assertProcessorType(result, IMakosRequestProcessor.class);
    }
    
    @Test
    @DisplayName("Should throw RuntimeException when no processor found for request type")
    void shouldThrowRuntimeException_whenNoProcessorFoundForRequestType() {
        // Given - Create empty factory
        ProcessorFactory emptyFactory = new ProcessorFactory(Arrays.asList());

        // When & Then
        assertThatThrownBy(() ->
            emptyFactory.getProcessor(GenelEvrakRequest.class, GenelKararResponse.class))
            .isInstanceOf(RuntimeException.class)
            .hasMessageContaining("No processor found for")
            .hasMessageContaining("GenelEvrakRequest")
            .hasMessageContaining("GenelKararResponse");
    }
    
    @Test
    @DisplayName("Should throw RuntimeException when no processor found for response type")
    void shouldThrowRuntimeException_whenNoProcessorFoundForResponseType() {
        // Given - Create empty factory
        ProcessorFactory emptyFactory = new ProcessorFactory(Arrays.asList());

        // When & Then
        assertThatThrownBy(() ->
            emptyFactory.getProcessor(GenelEvrakRequest.class, GenelKararResponse.class))
            .isInstanceOf(RuntimeException.class)
            .hasMessageContaining("No processor found for")
            .hasMessageContaining("GenelEvrakRequest");
    }
    
    @Test
    @DisplayName("Should handle multiple processors with same request type but different response types")
    void shouldHandleMultipleProcessors_withSameRequestTypeDifferentResponseTypes() {
        // Given - Multiple processors for same request type
        @SuppressWarnings("unchecked")
        IMakosRequestProcessor<GenelEvrakRequest, GenelKararResponse> secondProcessor = 
            (IMakosRequestProcessor<GenelEvrakRequest, GenelKararResponse>) mockAnotherProcessor;
        
        when(secondProcessor.getRelatedRequestType()).thenReturn(GenelEvrakRequest.class);
        when(secondProcessor.getRelatedResponseType()).thenReturn(GenelKararResponse.class);
        
        List<IMakosRequestProcessor<?, ?>> processors = Arrays.asList(mockGenelProcessor, secondProcessor);
        ProcessorFactory multiProcessorFactory = new ProcessorFactory(processors);
        
        // When
        IMakosRequestProcessor<GenelEvrakRequest, GenelKararResponse> result = 
            multiProcessorFactory.getProcessor(GenelEvrakRequest.class, GenelKararResponse.class);
        
        // Then - Should return one of the processors (last one wins in current implementation)
        assertThat(result).isNotNull();
        assertThat(result).isIn(mockGenelProcessor, secondProcessor);
    }
    
    @Test
    @DisplayName("Should initialize factory with empty processor list")
    void shouldInitializeFactory_withEmptyProcessorList() {
        // Given
        List<IMakosRequestProcessor<?, ?>> emptyProcessors = Arrays.asList();

        // When
        ProcessorFactory emptyFactory = new ProcessorFactory(emptyProcessors);

        // Then - Should not throw exception during initialization
        assertThat(emptyFactory).isNotNull();

        // But should throw when trying to get a processor
        assertThatThrownBy(() ->
            emptyFactory.getProcessor(GenelEvrakRequest.class, GenelKararResponse.class))
            .isInstanceOf(RuntimeException.class)
            .hasMessageContaining("No processor found for");
    }
}
