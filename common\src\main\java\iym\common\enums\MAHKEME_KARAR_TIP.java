package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum MAHKEME_KARAR_TIP {
    ONLEYICI_HAKIM_KARARI(100),
    SINYAL_BILGI_DEGERLENDIRME_KARARI(150),
    ABONE_KUTUK_BILGILERI_KARARI(151),
    ONLEYICI_YAZILI_EMIR(200),
    ADLI_HAKIM_KARARI(300),
    ADLI_HAKIM_HTS_KARARI(350),
    ADLI_YAZILI_EMIR(400),
    ADLI_KHK_YAZILI_EMIR(410),
    ADLI_SAVCILIK_HTS_KARARI(450),
    HEDEF_AD_SOYAD_DEGISTIRME(510),
    MAHKEME_KODU_DEGISTIRME(520),
    MAHKEME_AIDIYAT_DEGISTIRME(530),
    HEDEF_CANAK_DEGISTIRME(599),
    ONLE<PERSON><PERSON><PERSON>_SONLANDIRMA(600),
    ADLI_SONLANDIRMA(700),
    ADLI_SAVCILIK_SONLANDIRMA(710),
    ADLI_KHK_SONLANDIRMA(730),
    ADLI_ASKERI_HAKIM_KARARI(800),
    ADLI_ASKERI_SONLANDIRMA(900);

    private final int kararKodu;

    MAHKEME_KARAR_TIP(int kararKodu) {
        this.kararKodu = kararKodu;
    }

    @JsonValue
    public int getKararKodu() {
        return this.kararKodu;
    }

    @JsonCreator
    public static MAHKEME_KARAR_TIP fromName(String name) {
        for (MAHKEME_KARAR_TIP b : MAHKEME_KARAR_TIP.values()) {
            if (b.name().equals(name)) {
                return b;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararTip: " + name + "'");
    }

    //@JsonCreator
    public static MAHKEME_KARAR_TIP fromValue(int kararKodu) {
        for (MAHKEME_KARAR_TIP b : MAHKEME_KARAR_TIP.values()) {
            if (b.kararKodu == kararKodu) {
                return b;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararKodu: '" + kararKodu + "'");
    }
}
