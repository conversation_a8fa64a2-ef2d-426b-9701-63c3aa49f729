package iym.makos.domain.talepislem.idislemeaktar;

import iym.common.model.entity.iym.mk.MahkemeKararIslem;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.util.ApplicationConstants;
import iym.db.jpa.dao.EvrakKayitRepo;
import iym.db.jpa.dao.mk.MahkemeKararIslemRepo;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.makos.mapper.islemtablolari.MahkemeKararIslemMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Slf4j
public abstract class IDMahkemeKararTalepIslemeAktariciBase implements IDMahkemeKararTalepIslemeAktarici {

    protected EvrakKayitRepo evrakKayitRepo;
    protected MahkemeKararTalepRepo mahkemeKararTalepRepo;
    protected MahkemeKararIslemRepo mahkemeKararIslemRepo;
    protected MahkemeKararIslemMapper mahkemeKararIslemMapper;

    @Autowired
    public final void setEvrakKayitRepo(EvrakKayitRepo evrakKayitRepo) {
        this.evrakKayitRepo = evrakKayitRepo;
    }

    @Autowired
    public final void setMahkemeKararTalepRepo(MahkemeKararTalepRepo mahkemeKararTalepRepo) {
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
    }

    @Autowired
    public final void setMahkemeKararIslemRepo(MahkemeKararIslemRepo mahkemeKararIslemRepo) {
        this.mahkemeKararIslemRepo = mahkemeKararIslemRepo;
    }

    @Autowired
    public void setMahkemeKararIslemMapper(MahkemeKararIslemMapper mahkemeKararIslemMapper) {
        this.mahkemeKararIslemMapper = mahkemeKararIslemMapper;
    }


    @Override
    @Transactional
    public boolean process(Long mahkemeKararTalepId, Long kullaniciId) {
        boolean processResponse = process(mahkemeKararTalepId);
        if (!processResponse) {
            return processResponse;
        }

        return updateRelatedTables(mahkemeKararTalepId);
    }

    protected abstract boolean updateRelatedTables(Long mahkemeKararTalepId);

    private boolean process(Long mahkemeKararTalepId) {
        String  durum = ApplicationConstants.MKTALEP_DURUM_ISLEMDE;
        try{
            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(mahkemeKararTalepId);
            if (mahkemeKararTalepOpt.isEmpty()) {
                throw new Exception(String.format("Mahkeme Karar Talep %s bulunamadı", mahkemeKararTalepId));
            }
            MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepOpt.get();

            //Mahkeme Karar Talebin durum degisikligini kaydet
            mahkemeKararTalep.setDurum(durum);
            mahkemeKararTalepRepo.save(mahkemeKararTalep);

            //Mahkeme Karar Talebi İşlem tablosuna Aktar.
            MahkemeKararIslem mahkemeKararIslem = mahkemeKararIslemMapper.fromMahkemeKararIslem(mahkemeKararTalep);
            mahkemeKararIslemRepo.save(mahkemeKararIslem);

            return true;
        }catch (Exception ex){
            log.error("IDMahkemeKararTalepIslemeAktariciBase process failed, mahkemeKararTalepId:{}", mahkemeKararTalepId, ex);
            return  false;
        }
    }

}

