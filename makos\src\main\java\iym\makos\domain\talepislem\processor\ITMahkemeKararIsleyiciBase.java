package iym.makos.domain.talepislem.processor;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.db.jpa.dao.EvrakKayitRepo;
import iym.db.jpa.dao.talep.HtsMahkemeKararTalepRepo;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

@Slf4j
public abstract class ITMahkemeKararIsleyiciBase implements MahkemeKararTalepIsleyici {

    protected EvrakKayitRepo evrakKayitReporo;
    protected HtsMahkemeKararTalepRepo htsMahkemeKararTalepRepo;

    @Autowired
    public final void setEvrakKayitReporo(EvrakKayitRepo evrakKayitReporo) {
        this.evrakKayitReporo = evrakKayitReporo;
    }

    @Autowired
    public final void setHtsMahkemeKararTalepRepo(HtsMahkemeKararTalepRepo htsMahkemeKararTalepRepo) {
        this.htsMahkemeKararTalepRepo = htsMahkemeKararTalepRepo;
    }

    @Override
    public final MahkemeKararTalepUpdateResponse process(MahkemeKararTalepUpdateRequest request, Long kullaniciId) {

        try {
            String durum = MahkemeKararTalepIsleyici.toDurum(request.getTalepGuncellemeTuru());

            Optional<HtsMahkemeKararTalep> htsMahkemeKararTalepOpt = htsMahkemeKararTalepRepo.findById(request.getMahkemeKararTalepId());
            if (htsMahkemeKararTalepOpt.isEmpty()) {
                throw new Exception("talep bulunamadı");
            }

            HtsMahkemeKararTalep htsMahkemeKararTalep = htsMahkemeKararTalepOpt.get();

            //durum degisikligini kaydet
            htsMahkemeKararTalep.setDurum(durum);
            htsMahkemeKararTalepRepo.save(htsMahkemeKararTalep);

            Long evrakId = htsMahkemeKararTalep.getEvrakId();
            Optional<EvrakKayit> evrakKayitOpt = evrakKayitReporo.findById(evrakId);
            if (evrakKayitOpt.isEmpty()) {
                throw new Exception("Evrak kayıt bulunamadı");
            }
            EvrakKayit evrakKayit = evrakKayitOpt.get();

            //durum degisikligini kaydet
            evrakKayit.setDurumu(durum);
            evrakKayitReporo.save(evrakKayit);

            return updateRelatedTables(request);

        } catch (Exception ex) {
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Mahkeme karar talep veya evrak guncelleme hatası")
                            .build())
                    .build();
        }
    }

    protected abstract MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request);


}

