package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for EvrakFiles entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Evrak dosya bilgilerini içerir")
public class EvrakFilesDTO {

    @Schema(description = "Evrak File Id")
    private Long id;

    @Schema(description = "Evrak Id")
    private Long evrakId;

    @Schema(description = "File Name")
    private String fileName;

    @Schema(description = "Sıra No")
    private Long siraNo;

    @Schema(description = "Silindi")
    private boolean silindi;


}
