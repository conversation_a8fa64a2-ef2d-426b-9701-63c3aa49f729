package iym.makos.domain.testdata;

import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.api.Hedef;
import iym.common.model.api.HedefWithAdSoyad;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.api.HedefGuncellemeAlan;
import iym.makos.model.api.HedefGuncellemeBilgi;
import iym.makos.model.api.HedefGuncellemeDetay;
import iym.makos.model.api.HedefGuncellemeKararDetay;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.MahkemeKararGuncellemeAlan;
import iym.makos.model.api.MahkemeKararGuncellemeBilgi;
import iym.makos.model.api.MahkemeKararGuncellemeDetay;
import iym.makos.model.dto.mahkemekarar.id.GenelEvrakRequest;
import iym.makos.model.dto.mahkemekarar.id.IDHedefGuncellemeRequest;
import iym.makos.model.dto.mahkemekarar.id.IDMahkemeKararGuncellemeRequest;
import iym.makos.model.dto.mahkemekarar.id.IDYeniKararRequest;
import iym.makos.model.dto.mahkemekarar.id.IDUzatmaKarariRequest;
import iym.makos.model.dto.mahkemekarar.id.IDSonlandirmaKarariRequest;
import iym.makos.model.dto.mahkemekarar.id.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.model.dto.mahkemekarar.id.IDSucTipiGuncellemeRequest;
import iym.makos.model.dto.mahkemekarar.it.ITKararRequest;
import iym.makos.model.api.ITHedefDetay;
import iym.makos.model.api.AidiyatGuncellemeDetay;
import iym.makos.model.api.AidiyatGuncellemeKararDetay;
import iym.makos.model.api.SucTipiGuncellemeDetay;
import iym.makos.model.api.SucTipiGuncellemeKararDetay;
import iym.common.enums.GuncellemeTip;
import iym.common.enums.SorguTipi;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Test data builder utility class for creating test objects.
 * 
 * This class provides builder methods for creating test data objects
 * used across MAKOS domain unit tests.
 * 
 * Usage:
 * - Use static methods to create test objects
 * - Chain builder methods to customize objects
 * - Use default values for quick test setup
 * 
 * <AUTHOR> Team
 */
public class TestDataBuilder {
    
    // Test constants
    public static final String DEFAULT_EVRAK_NO = "2024/TEST/001";
    public static final String DEFAULT_KURUM_KODU = "01"; // MIT kurum kodu
    public static final String DEFAULT_IL_ILCE_KODU = "0600";
    public static final Long DEFAULT_USER_ID = 12345L;
    public static final String DEFAULT_USERNAME = "test_user";
    
    /**
     * Creates a valid UserDetailsImpl for testing
     */
    public static UserDetailsImpl createTestUser() {
        return createTestUser(DEFAULT_USER_ID, DEFAULT_USERNAME);
    }
    
    /**
     * Creates a UserDetailsImpl with custom values
     */
    public static UserDetailsImpl createTestUser(Long userId, String username) {
        return new UserDetailsImpl(userId, username, "test_password",
            iym.common.enums.MakosUserRoleType.ROLE_ADMIN,
            iym.common.enums.KullaniciKurum.BTK);
    }
    
    /**
     * Creates a valid EvrakDetay for testing
     */
    public static EvrakDetay createValidEvrakDetay() {
        return EvrakDetay.builder()
                .evrakNo(DEFAULT_EVRAK_NO)
                .evrakKurumKodu(DEFAULT_KURUM_KODU)
                .geldigiIlIlceKodu(DEFAULT_IL_ILCE_KODU)
                .evrakTarihi(LocalDate.now().atStartOfDay())
                .build();
    }
    
    /**
     * Creates a valid MahkemeKararBilgisi for testing
     */
    public static iym.makos.model.api.MahkemeKararBilgisi createValidMahkemeKararBilgisi() {
        return iym.makos.model.api.MahkemeKararBilgisi.builder()
                .mahkemeKararTipi(MahkemeKararTip.ADLI_HAKIM_KARARI)
                .mahkemeKararDetay(createValidMahkemeKararDetay())
                .build();
    }

    /**
     * Creates a valid MahkemeKararBilgisi for aidiyat güncelleme testing
     */
    public static iym.makos.model.api.MahkemeKararBilgisi createValidMahkemeKararBilgisiForAidiyatGuncelleme() {
        return iym.makos.model.api.MahkemeKararBilgisi.builder()
                .mahkemeKararTipi(MahkemeKararTip.MAHKEME_AIDIYAT_DEGISTIRME)
                .mahkemeKararDetay(createValidMahkemeKararDetay())
                .build();
    }

    /**
     * Creates a valid MahkemeKararBilgisi for suç tipi güncelleme testing
     */
    public static iym.makos.model.api.MahkemeKararBilgisi createValidMahkemeKararBilgisiForSucTipiGuncelleme() {
        return iym.makos.model.api.MahkemeKararBilgisi.builder()
                .mahkemeKararTipi(MahkemeKararTip.MAHKEME_SUCTIPI_DEGISTIRME)
                .mahkemeKararDetay(createValidMahkemeKararDetay())
                .build();
    }

    /**
     * Creates a valid MahkemeKararDetay for testing
     */
    public static iym.makos.model.api.MahkemeKararDetay createValidMahkemeKararDetay() {
        return iym.makos.model.api.MahkemeKararDetay.builder()
                .mahkemeKodu("001")
                .mahkemeIlIlceKodu(DEFAULT_IL_ILCE_KODU)
                .mahkemeKararNo("2024/001")
                .sorusturmaNo("2024/SOR/001")
                .aciklama("Test açıklaması")
                .build();
    }

    /**
     * Creates a valid IDHedefDetay for testing
     */
    public static IDHedefDetay createValidIDHedefDetay() {
        return IDHedefDetay.builder()
                .hedefNoAdSoyad(createValidHedefWithAdSoyad())
                .baslamaTarihi(LocalDateTime.now())
                .sureTip(iym.common.enums.SureTip.GUN)
                .sure(15)
                .hedefAidiyatKodlari(Arrays.asList("********"))
                .build();
    }

    /**
     * Creates a valid HedefWithAdSoyad for testing
     */
    public static HedefWithAdSoyad createValidHedefWithAdSoyad() {
        return HedefWithAdSoyad.builder()
                .hedef(Hedef.builder()
                        .hedefNo("H001")
                        .hedefTip(iym.common.enums.HedefTip.GSM)
                        .build())
                .hedefAd("Test")
                .hedefSoyad("User")
                .tcKimlikNo("12345678901")
                .build();
    }

    /**
     * Creates a valid GenelEvrakRequest for testing
     */
    public static GenelEvrakRequest createValidGenelEvrakRequest() {
        return GenelEvrakRequest.builder()
                .kararTuru(KararTuru.GENEL_EVRAK)
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(createValidMahkemeKararBilgisi())
                .build();
    }

    /**
     * Creates a valid IDAidiyatBilgisiGuncellemeRequest for testing
     */
    public static IDAidiyatBilgisiGuncellemeRequest createValidIDAidiyatBilgisiGuncellemeRequest() {
        return IDAidiyatBilgisiGuncellemeRequest.builder()
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME)
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(createValidMahkemeKararBilgisiForAidiyatGuncelleme())
                .aidiyatGuncellemeKararDetayListesi(Arrays.asList(
                        AidiyatGuncellemeKararDetay.builder()
                                .mahkemeKararDetay(MahkemeKararDetay.builder()
                                        .mahkemeIlIlceKodu("34001")
                                        .mahkemeKodu("34001")
                                        .mahkemeKararNo("2024/001")
                                        .sorusturmaNo("2024/001")
                                        .build())
                                .aidiyatGuncellemeDetayListesi(Arrays.asList(
                                        AidiyatGuncellemeDetay.builder()
                                                .aidiyatKodu("MT12345")
                                                .guncellemeTip(GuncellemeTip.EKLE)
                                                .build()
                                ))
                                .build()
                ))
                .build();
    }

    /**
     * Creates a valid IDSucTipiGuncellemeRequest for testing
     */
    public static IDSucTipiGuncellemeRequest createValidIDSucTipiGuncellemeRequest() {
        return IDSucTipiGuncellemeRequest.builder()
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME)
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(createValidMahkemeKararBilgisiForSucTipiGuncelleme())
                .sucTipiGuncellemeKararDetayListesi(Arrays.asList(
                        SucTipiGuncellemeKararDetay.builder()
                                .mahkemeKararDetay(MahkemeKararDetay.builder()
                                        .mahkemeIlIlceKodu("34001")
                                        .mahkemeKodu("34001")
                                        .mahkemeKararNo("2024/001")
                                        .sorusturmaNo("2024/001")
                                        .build())
                                .sucTipiGuncellemeDetayListesi(Arrays.asList(
                                        SucTipiGuncellemeDetay.builder()
                                                .sucTipiKodu("SUC001")
                                                .guncellemeTip(GuncellemeTip.EKLE)
                                                .build()
                                ))
                                .build()
                ))
                .build();
    }

    /**
     * Creates a valid ITHedefDetay for testing
     */
    public static ITHedefDetay createValidITHedefDetay() {
        return ITHedefDetay.builder()
                .sorguTipi(SorguTipi.TELEFON_GORUSME)
                .hedef(Hedef.builder()
                        .hedefNo("H001")
                        .hedefTip(iym.common.enums.HedefTip.GSM)
                        .build())
                .baslamaTarihi(LocalDateTime.now().minusDays(1))
                .bitisTarihi(LocalDateTime.now().plusDays(30))
                .tespitTuru("10")
                .tespitTuruDetay("3")
                .aciklama("Test IT hedef detayı")
                .build();
    }

    /**
     * Creates a valid ITKararRequest for testing
     */
    public static ITKararRequest createValidITKararRequest() {
        return ITKararRequest.builder()
                .kararTuru(KararTuru.ILETISIMIN_TESPITI)
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(createValidMahkemeKararBilgisi())
                .hedefDetayListesi(Arrays.asList(createValidITHedefDetay()))
                .build();
    }
    
    /**
     * Builder class for GenelEvrakRequest
     */
    public static class GenelEvrakRequestBuilder {
        private GenelEvrakRequest request;
        
        public GenelEvrakRequestBuilder() {
            this.request = createValidGenelEvrakRequest();
        }
        
        public GenelEvrakRequestBuilder withKararTuru(KararTuru kararTuru) {
            this.request.setKararTuru(kararTuru);
            return this;
        }
        
        public GenelEvrakRequestBuilder withEvrakNo(String evrakNo) {
            this.request.getEvrakDetay().setEvrakNo(evrakNo);
            return this;
        }
        
        public GenelEvrakRequestBuilder withKurumKodu(String kurumKodu) {
            this.request.getEvrakDetay().setEvrakKurumKodu(kurumKodu);
            return this;
        }
        
        public GenelEvrakRequestBuilder withMahkemeKararTipi(MahkemeKararTip tip) {
            this.request.getMahkemeKararBilgisi().setMahkemeKararTipi(tip);
            return this;
        }
        
        public GenelEvrakRequest build() {
            return this.request;
        }
    }
    
    /**
     * Creates a GenelEvrakRequest builder
     */
    public static GenelEvrakRequestBuilder genelEvrakRequest() {
        return new GenelEvrakRequestBuilder();
    }

    /**
     * Creates a valid IDUzatmaKarariRequest for testing
     */
    public static IDUzatmaKarariRequest createValidIDUzatmaKarariRequest() {
        return IDUzatmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI)
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ADLI_HAKIM_KARARI)
                        .mahkemeKararDetay(createValidMahkemeKararDetay())
                        .build())
                .build();
    }

    /**
     * Creates a valid IDSonlandirmaKarariRequest for testing
     */
    public static IDSonlandirmaKarariRequest createValidIDSonlandirmaKarariRequest() {
        return IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ADLI_HAKIM_KARARI)
                        .mahkemeKararDetay(createValidMahkemeKararDetay())
                        .build())
                .build();
    }

    /**
     * Creates a valid IDYeniKararRequest for testing.
     *
     * @return IDYeniKararRequest with valid test data
     */
    public static IDYeniKararRequest createValidIDYeniKararRequest() {
        return IDYeniKararRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR)
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ADLI_HAKIM_KARARI)
                        .mahkemeKararDetay(createValidMahkemeKararDetay())
                        .build())
                .hedefDetayListesi(Arrays.asList(createValidIDHedefDetay()))
                .mahkemeAidiyatKodlari(Arrays.asList("MT12345")) // MIT kurum kodu için geçerli aidiyat
                .mahkemeSucTipiKodlari(Arrays.asList("001")) // Geçerli suç tipi kodu
                .build();
    }

    /**
     * Creates a valid IDHedefGuncellemeRequest for testing.
     *
     * @return IDHedefGuncellemeRequest with valid test data
     */
    public static IDHedefGuncellemeRequest createValidIDHedefGuncellemeRequest() {
        return IDHedefGuncellemeRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME)
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.HEDEF_BILGI_DEGISTIRME)
                        .mahkemeKararDetay(createValidMahkemeKararDetay())
                        .build())
                .hedefGuncellemeKararDetayListesi(Arrays.asList(createValidHedefGuncellemeKararDetay()))
                .build();
    }

    /**
     * Creates a valid IDMahkemeKararGuncellemeRequest for testing.
     *
     * @return IDMahkemeKararGuncellemeRequest with valid test data
     */
    public static IDMahkemeKararGuncellemeRequest createValidIDMahkemeKararGuncellemeRequest() {
        return IDMahkemeKararGuncellemeRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME)
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.MAHKEME_KARAR_BILGI_DEGISTIRME)
                        .mahkemeKararDetay(createValidMahkemeKararDetay())
                        .build())
                .mahkemeKararGuncellemeDetayListesi(Arrays.asList(createValidMahkemeKararGuncellemeDetay()))
                .build();
    }

    /**
     * Creates a valid MahkemeKararGuncellemeDetay for testing.
     *
     * @return MahkemeKararGuncellemeDetay with valid test data
     */
    public static MahkemeKararGuncellemeDetay createValidMahkemeKararGuncellemeDetay() {
        return MahkemeKararGuncellemeDetay.builder()
                .mahkemeKararDetay(createValidMahkemeKararDetay())
                .mahkemeKararGuncellemeBilgiListesi(Arrays.asList(
                        createMahkemeKararGuncellemeBilgi(MahkemeKararGuncellemeAlan.MAHKEME_KODU, "002")
                ))
                .build();
    }

    /**
     * Creates a MahkemeKararGuncellemeBilgi for testing.
     *
     * @param alan The field to update
     * @param yeniDeger The new value
     * @return MahkemeKararGuncellemeBilgi with test data
     */
    public static MahkemeKararGuncellemeBilgi createMahkemeKararGuncellemeBilgi(
            MahkemeKararGuncellemeAlan alan, String yeniDeger) {
        return MahkemeKararGuncellemeBilgi.builder()
                .mahkemeKararGuncellemeAlan(alan)
                .yeniDegeri(yeniDeger)
                .build();
    }

    /**
     * Creates a valid HedefGuncellemeKararDetay for testing.
     *
     * @return HedefGuncellemeKararDetay with valid test data
     */
    public static HedefGuncellemeKararDetay createValidHedefGuncellemeKararDetay() {
        return HedefGuncellemeKararDetay.builder()
                .mahkemeKararDetay(createValidMahkemeKararDetay())
                .hedefGuncellemeDetayListesi(Arrays.asList(createValidHedefGuncellemeDetay()))
                .build();
    }

    /**
     * Creates a valid HedefGuncellemeDetay for testing.
     *
     * @return HedefGuncellemeDetay with valid test data
     */
    public static HedefGuncellemeDetay createValidHedefGuncellemeDetay() {
        return HedefGuncellemeDetay.builder()
                .hedef(iym.common.model.api.Hedef.builder()
                        .hedefNo("H001")
                        .hedefTip(iym.common.enums.HedefTip.GSM)
                        .build())
                .hedefGuncellemeBilgiListesi(Arrays.asList(createValidHedefGuncellemeBilgi()))
                .build();
    }

    /**
     * Creates a valid HedefGuncellemeBilgi for testing.
     *
     * @return HedefGuncellemeBilgi with valid test data
     */
    public static HedefGuncellemeBilgi createValidHedefGuncellemeBilgi() {
        return HedefGuncellemeBilgi.builder()
                .hedefGuncellemeAlan(HedefGuncellemeAlan.AD)
                .yeniDegeri("Yeni Ad")
                .build();
    }

    /**
     * Creates a list of HedefGuncellemeBilgi for testing different update scenarios.
     *
     * @return List of HedefGuncellemeBilgi with different update types
     */
    public static List<HedefGuncellemeBilgi> createHedefGuncellemeBilgiList() {
        return Arrays.asList(
                HedefGuncellemeBilgi.builder()
                        .hedefGuncellemeAlan(HedefGuncellemeAlan.AD)
                        .yeniDegeri("Yeni Ad")
                        .build(),
                HedefGuncellemeBilgi.builder()
                        .hedefGuncellemeAlan(HedefGuncellemeAlan.SOYAD)
                        .yeniDegeri("Yeni Soyad")
                        .build()
        );
    }
}
