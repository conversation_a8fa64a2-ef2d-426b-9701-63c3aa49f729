# Flyway Configuration File
# Database connection settings
flyway.url=****************************************
flyway.user=demo_user
flyway.password=demo_password

# Migration settings
flyway.locations=filesystem:src/main/resources/db/migration
flyway.baselineOnMigrate=true
flyway.validateOnMigrate=true
flyway.cleanDisabled=true

# Schema settings
flyway.defaultSchema=public

# Encoding
flyway.encoding=UTF-8

# Placeholders (optional)
flyway.placeholderReplacement=true
flyway.placeholders.database_name=demo_db

# Validation settings
flyway.outOfOrder=false
flyway.ignoreMissingMigrations=false
flyway.ignoreIgnoredMigrations=false
flyway.ignorePendingMigrations=false
flyway.ignoreFutureMigrations=true

# Baseline settings
flyway.baselineVersion=1
flyway.baselineDescription=Initial baseline

# Table settings
flyway.table=flyway_schema_history
