package iym.makos.controller;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.enums.ResponseCode;
import iym.common.model.api.ApiResponse;
import iym.common.service.db.DbIllerService;
import iym.common.service.file.FilePersisterService;
import iym.common.util.CommonUtils;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.domain.mahkemekarar.service.MahkemeKararProcessService;
import iym.makos.domain.talepislem.service.TalepIslemService;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.mahkemekarar.id.*;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mktalep.IDMahkemeKararTalepIslenecekRequest;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mktalep.IDMahkemeKararTalepIslenecekResponse;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mktalep.IDMahkemeKararTalepSorgulamaRequest;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mktalep.IDMahkemeKararTalepSorgulamaResponse;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mktalep.MahkemeKararTalepIslenecekView;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mktalep.MahkemeKararTalepSorguView;
import iym.makos.model.dto.mahkemekarar.it.ITKararRequest;
import iym.makos.model.dto.mahkemekarar.it.ITKararResponse;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;
import iym.makos.service.db.MahkemeKararTalepService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.List;

@RestController
@RequestMapping("/mahkemeKararTalep")
@Slf4j
public class MahkemeKararTalepController {

    public final static String MAHKEME_KARAR_DOSYA_PART = "mahkemeKararDosyasi";
    public final static String MAHKEME_KARAR_DETAY_PART = "mahkemeKararDetay";

    @Autowired
    private DbIllerService illerService;

    @Autowired
    private MahkemeKararTalepService mahkemeKararTalepService;

    @Autowired
    private MahkemeKararProcessService mahkemeKararProcessService;

    @Autowired
    private TalepIslemService talepIslemService;

    private final FilePersisterService filePersisterService = FilePersisterService.getInstance();

    /* ****************************************************************************/
    /* ********************* MULTIPART OPERATIONS START HERE **********************/
    /* ****************************************************************************/

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/yeniKararID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/yeniKararID", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDYeniKararResponse> yeniKararID(
            //@Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_DOSYA_PART) MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "ID Mahkeme Karar Detaylari", schema = @Schema(implementation = IDYeniKararRequest.class, type = "string"))
            @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_PART) IDYeniKararRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDYeniKararRequest received:{}, user:{}", request, user.getUsername());

            // TODO update here if necessary
            String filePath = getFullEvrakSavePath(request.getId().toString(), mahkemeKararDosyasiID.getOriginalFilename());
            boolean saved = filePersisterService.saveFileToDisk(filePath, mahkemeKararDosyasiID.getBytes());
            if (saved){
                request.setFileName(filePath);
            } else{
                log.error("IDYeniKararRequest file save failed. id:{}", request.getId());
                throw new RuntimeException("Dosya saklama hatasi");
            }

            IDYeniKararResponse response = mahkemeKararProcessService.process(request, IDYeniKararResponse.class, user);
            log.info("IDYeniKararRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);

            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDYeniKararRequest failed. id:{}", request.getId(), e);
            IDYeniKararResponse response = IDYeniKararResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/uzatmaKarariID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/uzatmaKarariID", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDUzatmaKarariResponse> uzatmaKarariID(
            //@Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_DOSYA_PART) MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "ID Mahkeme Karar Detaylari", schema = @Schema(implementation = IDUzatmaKarariRequest.class, type = "string"))
            @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_PART) IDUzatmaKarariRequest request,
            Authentication authentication
    ) {
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDUzatmaKarariRequest received:{}, user:{}", request, user.getUsername());
            IDUzatmaKarariResponse response = mahkemeKararProcessService.process(request, IDUzatmaKarariResponse.class, user);
            log.info("IDUzatmaKarariRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDYeniKararRequest failed. id:{}", request.getId(), e);
            IDUzatmaKarariResponse response = IDUzatmaKarariResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/sonlandirmaKarariID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/sonlandirmaKarariID", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDSonlandirmaKarariResponse> sonlandirmaKarariID(
            //@Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = MAHKEME_KARAR_DOSYA_PART) MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "ID Mahkeme Karar Detaylari", schema = @Schema(implementation = IDSonlandirmaKarariRequest.class, type = "string"))
            @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_PART) IDSonlandirmaKarariRequest request,
            Authentication authentication
    ) {
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDSonlandirmaKarariRequest received:{}, user:{}", request, user.getUsername());
            IDSonlandirmaKarariResponse response = mahkemeKararProcessService.process(request, IDSonlandirmaKarariResponse.class, user);
            log.info("IDSonlandirmaKarariRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDSonlandirmaKarariRequest failed. id:{}", request.getId(), e);
            IDSonlandirmaKarariResponse response = IDSonlandirmaKarariResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/kararGonderID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/yenikararIT", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ITKararResponse> yenikararIT(

            @NotNull @RequestPart(value = MAHKEME_KARAR_DOSYA_PART) MultipartFile mahkemeKararDosyasiIT,

            @Parameter(required = true, description = "IT Mahkeme Karar Detaylari", schema = @Schema(implementation = ITKararRequest.class, type = "string"))
            @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_PART) ITKararRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("ITKararRequest received:{}, user:{}", request, user.getUsername());
            ITKararResponse response = mahkemeKararProcessService.process(request, ITKararResponse.class, user);
            log.info("ITKararRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("ITKararRequest failed. id:{}", request.getId(), e);
            ITKararResponse response = ITKararResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @PostMapping(path = "/aidiyatBilgisiGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDAidiyatBilgisiGuncellemeResponse> aidiyatBilgisiGuncelle(

            @NotNull @RequestPart(value = MAHKEME_KARAR_DOSYA_PART) MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDAidiyatBilgisiGuncellemeRequest.class, type = "string"))
            @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_PART) IDAidiyatBilgisiGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDAidiyatBilgisiGuncellemeRequest received:{}, user:{}", request, user.getUsername());
            IDAidiyatBilgisiGuncellemeResponse response = mahkemeKararProcessService.process(request, IDAidiyatBilgisiGuncellemeResponse.class, user);
            log.info("IDAidiyatBilgisiGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDAidiyatBilgisiGuncellemeRequest failed. id:{}", request.getId(), e);
            IDAidiyatBilgisiGuncellemeResponse response = IDAidiyatBilgisiGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping(path = "/sucTipiGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDSucTipiGuncellemeResponse> sucTipiGuncelle(

            @NotNull @RequestPart(value = MAHKEME_KARAR_DOSYA_PART) MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDSucTipiGuncellemeRequest.class, type = "string"))
            @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_PART) IDSucTipiGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDSucTipiGuncellemeRequest received:{}, user:{}", request, user.getUsername());
            IDSucTipiGuncellemeResponse response = mahkemeKararProcessService.process(request, IDSucTipiGuncellemeResponse.class, user);
            log.info("IDSucTipiGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDSucTipiGuncellemeRequest failed. id:{}", request.getId(), e);
            IDSucTipiGuncellemeResponse response = IDSucTipiGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping(path = "/hedefBilgisiGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDHedefGuncellemeResponse> hedefBilgisiGuncelle(

            @NotNull @RequestPart(value = MAHKEME_KARAR_DOSYA_PART) MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDHedefGuncellemeRequest.class, type = "string"))
            @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_PART) IDHedefGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDHedefBilgiGuncellemeRequest received:{}, user:{}", request, user.getUsername());
            IDHedefGuncellemeResponse response = mahkemeKararProcessService.process(request, IDHedefGuncellemeResponse.class, user);
            log.info("IDHedefBilgiGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDHedefBilgiGuncellemeRequest failed. id:{}", request.getId(), e);
            IDHedefGuncellemeResponse response = IDHedefGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    /* ****************************************************************************/
    /* ********************* MULTIPART OPERATIONS END HERE ************************/
    /* ****************************************************************************/

    @PostMapping(path = "/mahkemeBilgisiGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDMahkemeKararGuncellemeResponse> mahkemeBilgisiGuncelle(

            @NotNull @RequestPart(value = MAHKEME_KARAR_DOSYA_PART) MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDMahkemeKararGuncellemeRequest.class, type = "string"))
            @Valid @RequestPart(value = MAHKEME_KARAR_DETAY_PART) IDMahkemeKararGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDMahkemeBilgiGuncellemeRequest received:{}, user:{}", request, user.getUsername());
            IDMahkemeKararGuncellemeResponse response = mahkemeKararProcessService.process(request, IDMahkemeKararGuncellemeResponse.class, user);
            log.info("IDMahkemeBilgiGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDMahkemeBilgiGuncellemeRequest failed. id:{}", request.getId(), e);
            IDMahkemeKararGuncellemeResponse response = IDMahkemeKararGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/islenecekKararListele")
    public ResponseEntity<IDMahkemeKararTalepIslenecekResponse> islenecekKararListele(
            @Valid @RequestBody IDMahkemeKararTalepIslenecekRequest request,
            Authentication authentication) {
        try{
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            List<MahkemeKararTalepIslenecekView> islenecekKararListesi = mahkemeKararTalepService.islenecekMahkemeKararTalepleri(user);

            return ResponseEntity.ok(IDMahkemeKararTalepIslenecekResponse.builder()
                    .islenecekKararlar(islenecekKararListesi)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("İşlenecek Karar Listesi Başarılı")
                            .build())
                    .build());
        }catch (Exception ex){
            log.error("islenecekKararlariListele process failed, requestId:{}", request.hashCode(), ex);
            return ResponseEntity.ok(IDMahkemeKararTalepIslenecekResponse.builder()
                    .islenecekKararlar(null)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("İşlenecek Karar Listesi Başarısız")
                            .build())
                    .build());
        }
    }

    @PostMapping("/mahkemeKararTalepSorgu")
    public ResponseEntity<IDMahkemeKararTalepSorgulamaResponse> mahkemeKararTalepSorgu(
            @Valid @RequestBody IDMahkemeKararTalepSorgulamaRequest sorguParam, Authentication authentication) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            List<MahkemeKararTalepSorguView> sonucListesi = mahkemeKararTalepService.mahkemeKararTalepSorgu(user, sorguParam);

            return ResponseEntity.ok(IDMahkemeKararTalepSorgulamaResponse.builder()
                    .mahkemeKararTalepSorguViewListesi(sonucListesi)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Sorgu Başarılı")
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("mahkemeKararTalepSorgu process failed, requestId:{}", sorguParam.hashCode(), ex);
            IDMahkemeKararTalepSorgulamaResponse response = IDMahkemeKararTalepSorgulamaResponse.builder()
                    .mahkemeKararTalepSorguViewListesi(null)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Sorgu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }

    @PostMapping("talepGuncelle")
    @PreAuthorize("hasAnyRole('ADMIN', 'KURUM_TEMSILCISI')")
    public ResponseEntity<MahkemeKararTalepUpdateResponse> talepGuncelle(
            @Valid @RequestBody MahkemeKararTalepUpdateRequest request, Authentication authentication) {
        log.debug("TalepGuncelle request received. id:{}", request.getId());

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            log.info("TalepGuncelle Request received:{}, user:{}", request, user.getUsername());

            MahkemeKararTalepUpdateResponse response = talepIslemService.process(request, user.getId());
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        }catch (Exception ex){
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            MahkemeKararTalepUpdateResponse response = MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private String getFullEvrakSavePath(String folderName, String fileName){
        String baseFolder = CommonUtils.appendSubFolder(CommonUtils.getEvrakBasePath(), folderName);
        return CommonUtils.appendFileToPath(baseFolder, fileName);
    }
}