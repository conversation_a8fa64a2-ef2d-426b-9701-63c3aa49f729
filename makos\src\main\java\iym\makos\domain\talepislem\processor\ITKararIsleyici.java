package iym.makos.domain.talepislem.processor;

import iym.common.enums.KararTuru;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.HtsHedeflerTalepRepo;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ITKararIsleyici extends ITMahkemeKararIsleyiciBase {

    private final HtsHedeflerTalepRepo htsHedeflerTalepRepo;

    @Autowired
    public ITKararIsleyici(HtsHedeflerTalepRepo htsHedeflerTalepRepo) {
        this.htsHedeflerTalepRepo = htsHedeflerTalepRepo;
    }

    protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {

        try {
            String durum = MahkemeKararTalepIsleyici.toDurum(request.getTalepGuncellemeTuru());
            CommonUtils.safeList(htsHedeflerTalepRepo.findByMahkemeKararId(request.getMahkemeKararTalepId()))
                    .forEach(hedeflerDetayTalep -> {
                        hedeflerDetayTalep.setDurumu(durum);
                        htsHedeflerTalepRepo.save(hedeflerDetayTalep);
                    });

            return MahkemeKararTalepUpdateResponse.builder()
                    .requestId(request.getId())
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .build())
                    .build();

        } catch (Exception ex) {
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Iliskili veritabanı guncelleme hatası")
                            .build())
                    .build();
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_TESPITI;
    }

}

