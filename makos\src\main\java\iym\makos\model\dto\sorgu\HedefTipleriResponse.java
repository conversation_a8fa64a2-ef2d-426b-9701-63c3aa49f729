package iym.makos.model.dto.sorgu;

import iym.common.model.api.ApiResponse;
import iym.common.model.api.ApiResponseBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class HedefTipleriResponse extends ApiResponseBase {
    private List<HedefTipi> hedefTipleri;
}
