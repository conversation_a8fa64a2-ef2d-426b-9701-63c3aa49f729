# Oracle Testcontainer Test Profile Configuration for Database Module
# This profile is used for integration tests with Oracle Testcontainer
# The actual connection properties are set dynamically by AbstractOracleTestContainer

# Profile identification
spring.profiles.active=testcontainers-oracle

# JPA/Hibernate configuration for Oracle
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.properties.hibernate.default_schema=iym
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# Connection pool configuration optimized for testing
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5
spring.datasource.hikari.minimumIdle=2
spring.datasource.hikari.idleTimeout=300000
spring.datasource.hikari.maxLifetime=1200000
spring.datasource.hikari.leakDetectionThreshold=60000

# SQL initialization - disabled since we use init script in container
spring.sql.init.mode=never
spring.jpa.defer-datasource-initialization=false

# Logging configuration for tests - MAXIMUM DETAIL FOR DEBUGGING
logging.level.root=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.hibernate.engine.transaction=TRACE
logging.level.org.hibernate.resource.transaction=TRACE
logging.level.org.springframework.jdbc.core=DEBUG
logging.level.org.springframework.jdbc.datasource=TRACE
logging.level.org.springframework.transaction=TRACE
logging.level.org.springframework.orm.jpa=TRACE
logging.level.org.springframework.test=DEBUG
logging.level.org.testcontainers=TRACE
logging.level.org.testcontainers.containers=TRACE
logging.level.org.testcontainers.dockerclient=TRACE
logging.level.org.testcontainers.utility=TRACE
logging.level.com.github.dockerjava=DEBUG
logging.level.oracle.jdbc=TRACE
logging.level.oracle.net=TRACE
logging.level.oracle.security=DEBUG
logging.level.iym=DEBUG
logging.level.com.zaxxer.hikari=TRACE
logging.level.com.zaxxer.hikari.HikariConfig=TRACE
logging.level.com.zaxxer.hikari.HikariDataSource=TRACE
logging.level.com.zaxxer.hikari.pool=TRACE

# Test-specific configurations
spring.test.database.replace=none
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# HikariCP configuration for testcontainers
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.maximum-pool-size=3
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=600000
spring.datasource.hikari.leak-detection-threshold=30000

# Disable banner for cleaner test output
spring.main.banner-mode=off

# Transaction configuration for tests
spring.jpa.properties.hibernate.connection.autocommit=false
spring.transaction.default-timeout=30

# Oracle-specific configurations
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true

# Database module specific configurations
spring.application.name=database-test
