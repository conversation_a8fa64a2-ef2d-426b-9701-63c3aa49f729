package iym.makos.validator;

import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.Iller;
import iym.common.model.entity.iym.EvrakGelenKurumlar;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbEvrakGelenKurumlarService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.model.dto.mahkemekarar.id.GenelEvrakRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Optional;

import static iym.makos.domain.testdata.TestDataBuilder.createValidGenelEvrakRequest;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit tests for MahkemeKararRequestCommonValidator.
 *
 * Tests the common validation logic shared across all mahkeme karar requests.
 * Verifies common business rules and validation patterns.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("MahkemeKararRequestCommonValidator Unit Tests")
class MahkemeKararRequestCommonValidatorTest extends BaseDomainUnitTest {

    @Mock
    private DbIllerService dbIllerService;

    @Mock
    private DbEvrakGelenKurumlarService dbEvrakGelenKurumlarService;

    @Mock
    private DbMahkemeBilgiService dbMahkemeBilgiService;

    @InjectMocks
    private MahkemeKararRequestCommonValidator commonValidator;

    private GenelEvrakRequest testRequest;

    @BeforeEach
    void setUp() {
        testRequest = createValidGenelEvrakRequest();
    }

    private void setupSuccessfulMocks() {
        // Setup default mock behaviors for successful validation
        when(dbIllerService.getByIlIlceKodu(anyString()))
            .thenReturn(Optional.of(createMockIller()));
        when(dbEvrakGelenKurumlarService.findByKurumKod(anyString()))
            .thenReturn(Optional.of(createMockEvrakGelenKurumlar()));
        when(dbMahkemeBilgiService.findByMahkemeKodu(anyString()))
            .thenReturn(Optional.of(createMockMahkemeBilgi()));
    }

    private Iller createMockIller() {
        Iller iller = new Iller();
        iller.setIlKod("34");
        iller.setIlAdi("İstanbul");
        iller.setIlceAdi("Kadıköy");
        return iller;
    }

    private EvrakGelenKurumlar createMockEvrakGelenKurumlar() {
        EvrakGelenKurumlar kurum = new EvrakGelenKurumlar();
        kurum.setKurumKod("BTK");
        kurum.setKurumAdi("Bilgi Teknolojileri ve İletişim Kurumu");
        return kurum;
    }

    private MahkemeBilgi createMockMahkemeBilgi() {
        MahkemeBilgi mahkeme = new MahkemeBilgi();
        mahkeme.setMahkemeKodu("34001");
        mahkeme.setMahkemeAdi("İstanbul 1. Asliye Ceza Mahkemesi");
        return mahkeme;
    }
    
    @Test
    @DisplayName("Should pass validation when request has all required fields")
    void shouldPassValidation_whenRequestHasAllRequiredFields() {
        // Given
        setupSuccessfulMocks();

        // When
        ValidationResult result = commonValidator.validate(testRequest);

        // Then
        assertValidationSuccess(result);
    }
    
    @Test
    @DisplayName("Should fail validation when evrak detay is null")
    void shouldFailValidation_whenEvrakDetayIsNull() {
        // Given
        testRequest.setEvrakDetay(null);

        // When
        ValidationResult result = commonValidator.validate(testRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
            reason.contains("Validation failed. Internal error"));
    }

    @Test
    @DisplayName("Should fail validation when mahkeme karar bilgisi is null")
    void shouldFailValidation_whenMahkemeKararBilgisiIsNull() {
        // Given
        testRequest.setMahkemeKararBilgisi(null);

        // When
        ValidationResult result = commonValidator.validate(testRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
            reason.contains("Validation failed. Internal error"));
    }

    @Test
    @DisplayName("Should handle null request gracefully")
    void shouldHandleNullRequest_gracefully() {
        // When
        ValidationResult result = commonValidator.validate(null);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
            reason.contains("Validation failed. Internal error"));
    }
    
    @Test
    @DisplayName("Should validate evrak no format correctly")
    void shouldValidateEvrakNoFormat_correctly() {
        // Given
        setupSuccessfulMocks();

        // Test valid evrak no formats
        String[] validEvrakNos = {
            "2024/001",
            "2024/TEST/001",
            "E.2024/123",
            "K.2024/456"
        };

        for (String evrakNo : validEvrakNos) {
            // Given
            testRequest.getEvrakDetay().setEvrakNo(evrakNo);

            // When
            ValidationResult result = commonValidator.validate(testRequest);

            // Then
            assertValidationSuccess(result);
        }
    }

    @Test
    @DisplayName("Should validate kurum kodu format correctly")
    void shouldValidateKurumKoduFormat_correctly() {
        // Given
        setupSuccessfulMocks();

        // Test valid kurum kodu formats
        String[] validKurumKodlari = {
            "BTK",
            "EMNIYET",
            "JANDARMA",
            "MİT"
        };

        for (String kurumKodu : validKurumKodlari) {
            // Given
            testRequest.getEvrakDetay().setEvrakKurumKodu(kurumKodu);

            // When
            ValidationResult result = commonValidator.validate(testRequest);

            // Then
            assertValidationSuccess(result);
        }
    }

    @Test
    @DisplayName("Should fail validation when il/ilce kodu not found in database")
    void shouldFailValidation_whenIlIlceKoduNotFound() {
        // Given
        when(dbIllerService.getByIlIlceKodu(anyString()))
            .thenReturn(Optional.empty());
        when(dbEvrakGelenKurumlarService.findByKurumKod(anyString()))
            .thenReturn(Optional.of(createMockEvrakGelenKurumlar()));
        when(dbMahkemeBilgiService.findByMahkemeKodu(anyString()))
            .thenReturn(Optional.of(createMockMahkemeBilgi()));

        // When
        ValidationResult result = commonValidator.validate(testRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
            reason.contains("evrak il/ilçe verisi sistemde bulunamadı"));
    }

    @Test
    @DisplayName("Should fail validation when mahkeme kodu not found in database")
    void shouldFailValidation_whenMahkemeKoduNotFound() {
        // Given
        when(dbIllerService.getByIlIlceKodu(anyString()))
            .thenReturn(Optional.of(createMockIller()));
        when(dbEvrakGelenKurumlarService.findByKurumKod(anyString()))
            .thenReturn(Optional.of(createMockEvrakGelenKurumlar()));
        when(dbMahkemeBilgiService.findByMahkemeKodu(anyString()))
            .thenReturn(Optional.empty());

        // When
        ValidationResult result = commonValidator.validate(testRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
            reason.contains("kodu ile kayıtlı bir mahkeme bilgisi bulunamamıştır"));
    }

    @Test
    @DisplayName("Should fail validation when kurum kodu not found in database")
    void shouldFailValidation_whenKurumKoduNotFound() {
        // Given
        when(dbIllerService.getByIlIlceKodu(anyString()))
            .thenReturn(Optional.of(createMockIller()));
        when(dbEvrakGelenKurumlarService.findByKurumKod(anyString()))
            .thenReturn(Optional.empty());
        when(dbMahkemeBilgiService.findByMahkemeKodu(anyString()))
            .thenReturn(Optional.of(createMockMahkemeBilgi()));

        // When
        ValidationResult result = commonValidator.validate(testRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
            reason.contains("evrak kurum kodu sistemde bulunamadı"));
    }
    
    @Test
    @DisplayName("Should accumulate multiple validation errors")
    void shouldAccumulateMultipleValidationErrors() {
        // Given - Setup mocks to return empty for database lookups (multiple failures)
        when(dbIllerService.getByIlIlceKodu(anyString()))
            .thenReturn(Optional.empty());
        when(dbEvrakGelenKurumlarService.findByKurumKod(anyString()))
            .thenReturn(Optional.empty());
        when(dbMahkemeBilgiService.findByMahkemeKodu(anyString()))
            .thenReturn(Optional.empty());

        // When
        ValidationResult result = commonValidator.validate(testRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).hasSizeGreaterThan(1);
    }

    @Test
    @DisplayName("Should return early when request.isValid() fails")
    void shouldReturnEarly_whenRequestIsValidFails() {
        // Given - Create a request with invalid kararTuru to make isValid() fail
        testRequest.setKararTuru(KararTuru.ILETISIMIN_TESPITI); // Wrong type for GenelEvrakRequest

        // When
        ValidationResult result = commonValidator.validate(testRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
            reason.contains("Karar türü: " + KararTuru.GENEL_EVRAK.name() + " olmalıdır"));

        // Verify that database services were never called since validation returned early
        verify(dbIllerService, never()).getByIlIlceKodu(anyString());
        verify(dbEvrakGelenKurumlarService, never()).findByKurumKod(anyString());
        verify(dbMahkemeBilgiService, never()).findByMahkemeKodu(anyString());
    }
}
