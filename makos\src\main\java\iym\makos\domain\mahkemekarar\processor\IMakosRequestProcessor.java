package iym.makos.domain.mahkemekarar.processor;

import iym.makos.config.security.UserDetailsImpl;
import iym.makos.model.dto.mahkemekarar.MahkemeKararRequest;
import iym.makos.model.dto.mahkemekarar.MahkemeKararResponse;

public interface IMakosRequestProcessor<T extends MahkemeKararRequest, R extends MahkemeKararResponse> {

    public R process(T request, UserDetailsImpl islemYapanKullanici);

    public Class<T> getRelatedRequestType();

    public Class<R> getRelatedResponseType();

}
