package iym.makos.model.dto.sorgu;

import iym.common.model.api.ApiResponseBase;
import iym.makos.model.dto.db.SorguTipiDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class SorguTipiListResponse extends ApiResponseBase {

    private List<SorguTipiDTO> sorguTipleri;
} 