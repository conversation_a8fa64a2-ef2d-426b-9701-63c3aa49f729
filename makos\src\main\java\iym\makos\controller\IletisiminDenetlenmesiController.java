package iym.makos.controller;

import iym.common.enums.ResponseCode;
import iym.common.model.api.ApiResponse;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.mahkemekarar.id.IDMahkemeKararAtamaRequest;
import iym.makos.model.dto.mahkemekarar.id.IDMahkemeKararAtamaResponse;
import iym.makos.model.dto.mahkemekarar.id.IDMahkemeKararOnaylamaResponse;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mk.id.IDMahkemeKararIslenecekRequest;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mk.id.IDMahkemeKararSorgulamaRequest;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.reqres.mk.id.IDMahkemeKararSorgulamaResponse;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mk.MahkemeKararSorguView;
import iym.makos.service.db.MahkemeKararService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/iletisiminDenetlenmesi")
@Slf4j
@Validated
public class IletisiminDenetlenmesiController {

    @Autowired
    private MahkemeKararService mahkemeKararService;

    @PostMapping("/islenecekKararListesi")
    public ResponseEntity<IDMahkemeKararSorgulamaResponse> islenecekKararListesi(
            @Valid @RequestBody IDMahkemeKararIslenecekRequest sorguParam, Authentication authentication) {
        //sorguParam'dan sadece uuid geliyor. loglama amacli
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            List<MahkemeKararSorguView> sonucListesi = mahkemeKararService.islenecekKararListesi(user);

            return ResponseEntity.ok(IDMahkemeKararSorgulamaResponse.builder()
                    .kararlar(sonucListesi)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Başarılı")
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("islenecekKararListesi process failed, ReqId:{}", sorguParam.getId(), ex);
            IDMahkemeKararSorgulamaResponse response = IDMahkemeKararSorgulamaResponse.builder()
                    .kararlar(null)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("İşlenecek Karar Liste Sorgusu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }


    @PostMapping("/mahkemeKararSorgu")
    public ResponseEntity<IDMahkemeKararSorgulamaResponse> mahkemeKararTalepSorgu(
            @Valid @RequestBody IDMahkemeKararSorgulamaRequest sorguParam, Authentication authentication) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            List<MahkemeKararSorguView> sonucListesi = mahkemeKararService.mahkemeKararSorgu(user, sorguParam.getSorguParam());

            return ResponseEntity.ok(IDMahkemeKararSorgulamaResponse.builder()
                    .kararlar(sonucListesi)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Sorgu Başarılı")
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("mahkemeKararSorgu process failed, requestId:{}", sorguParam.hashCode(), ex);
            IDMahkemeKararSorgulamaResponse response = IDMahkemeKararSorgulamaResponse.builder()
                    .kararlar(null)
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.FAILED)
                            .responseMessage("Sorgu Başarısız")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }


    @PostMapping("/kararAtama")
    public ResponseEntity<IDMahkemeKararAtamaResponse> mahkemeKararAtama(
            @Valid @RequestBody IDMahkemeKararAtamaRequest request, Authentication authentication) {
        log.debug("mahkemeKararAtama request received. id:{}", request.getId());

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            log.info("mahkemeKararAtama Request received:{}, user:{}", request, user.getUsername());

            IDMahkemeKararAtamaResponse response = null;//talepIslemService.process(request, user.getId());
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        }catch (Exception ex){
            log.error("mahkemeKararAtama  failed, requestId:{}, evrakId:{}", request.getId(), request.getEvrakId(), ex);
            IDMahkemeKararAtamaResponse response = IDMahkemeKararAtamaResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/kararOnaylama")
    public ResponseEntity<IDMahkemeKararOnaylamaResponse> mahkemeKararOnaylama(
            @Valid @RequestBody IDMahkemeKararAtamaRequest request, Authentication authentication) {
        log.debug("mahkemeKararOnaylama request received. id:{}", request.getId());

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();
            log.info("mahkemeKararOnaylama Request received:{}, user:{}", request, user.getUsername());

            IDMahkemeKararOnaylamaResponse response = null;//talepIslemService.process(request, user.getId());
            String aciklama = "";

            return ResponseEntity.ok(IDMahkemeKararOnaylamaResponse.builder()
                    .aciklama(aciklama)
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage("Onaylama Başarılı")
                            .build())
                    .build());


        }catch (Exception ex){
            log.error("kararOnaylama request failed, requestId:{}, evrakId:{}", request.getId(), request.getEvrakId(), ex);
            IDMahkemeKararOnaylamaResponse response = IDMahkemeKararOnaylamaResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}
