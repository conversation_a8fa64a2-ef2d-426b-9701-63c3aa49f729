package iym.backend;

import iym.backend.kullanici.dto.KullaniciDto;
import iym.backend.kullanici.entity.Kullanici;
import iym.backend.kullanici.service.KullaniciService;
import iym.backend.kullanici.repository.KullaniciRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for KullaniciService using PostgreSQL (H2 in-memory for testing)
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {"app.seed-data=false"})
@Transactional
class KullaniciServiceTest {

    @Autowired
    private KullaniciService kullaniciService;

    @Autowired
    private KullaniciRepository kullaniciRepository;

    @Test
    void testCreateAndFindKullanici() {
        // Given
        KullaniciDto kullaniciDto = KullaniciDto.builder()
                .kullaniciAdi("testuser")
                .email("<EMAIL>")
                .ad("Test")
                .soyad("User")
                .tcno("12345678901")
                .status("AKTIF")
                .kullaniciGrupIdList(java.util.Collections.emptyList())
                .build();

        // When
        KullaniciDto savedKullanici = kullaniciService.save(kullaniciDto);

        // Then
        assertNotNull(savedKullanici);
        assertNotNull(savedKullanici.getId());
        assertEquals("testuser", savedKullanici.getKullaniciAdi());
        assertEquals("<EMAIL>", savedKullanici.getEmail());
        assertEquals("Test", savedKullanici.getAd());
        assertEquals("User", savedKullanici.getSoyad());
        // Note: Entity default status is SIFRE_DEGISTIRMELI, but service should set the provided status
        assertEquals("AKTIF", savedKullanici.getStatus());
    }

    @Test
    void testFindByKullaniciAdi() {
        // Given
        KullaniciDto kullaniciDto = KullaniciDto.builder()
                .kullaniciAdi("finduser")
                .email("<EMAIL>")
                .ad("Find")
                .soyad("User")
                .tcno("12345678902")
                .status("AKTIF")
                .kullaniciGrupIdList(java.util.Collections.emptyList())
                .build();

        kullaniciService.save(kullaniciDto);

        // When
        var foundKullanici = kullaniciRepository.findByKullaniciAdi("finduser");

        // Then
        assertTrue(foundKullanici.isPresent());
        assertEquals("finduser", foundKullanici.get().getKullaniciAdi());
        assertEquals("<EMAIL>", foundKullanici.get().getEmail());
    }

    @Test
    void testFindAll() {
        // Given
        KullaniciDto kullanici1 = KullaniciDto.builder()
                .kullaniciAdi("user1")
                .email("<EMAIL>")
                .ad("User")
                .soyad("One")
                .tcno("12345678903")
                .status("AKTIF")
                .kullaniciGrupIdList(java.util.Collections.emptyList())
                .build();

        KullaniciDto kullanici2 = KullaniciDto.builder()
                .kullaniciAdi("user2")
                .email("<EMAIL>")
                .ad("User")
                .soyad("Two")
                .tcno("12345678904")
                .status("AKTIF")
                .kullaniciGrupIdList(java.util.Collections.emptyList())
                .build();

        kullaniciService.save(kullanici1);
        kullaniciService.save(kullanici2);

        // When
        var allKullanicilar = kullaniciService.findAll();

        // Then
        assertNotNull(allKullanicilar);
        assertTrue(allKullanicilar.size() >= 2);
    }
}

