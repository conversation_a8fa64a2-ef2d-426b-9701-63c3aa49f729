package iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mktalep;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description =  "Mahkeme Karar Talep Listeleme  Bilgisi")
public class MahkemeKararTalepSorguView {

    @Schema(description = "Mahkeme Karar Talep Id")
    private Long mahkemeKararTalepId;

    @Schema(description = "Evrak Id")
    private Long evrakId;

    @Schema(description = "Evrak Sıra No")
    private String evrakSiraNo;

    @Schema(description = "Kurum Evrak No")
    private String kurumEvrakNo;

    @Schema(description = "Kurum Evrak Ta<PERSON>hi")
    private Date kurumEvrakTarihi;

    @Schema(description = "Kurum Evrak No")
    private Date kararKayitTarihi;

    @Schema(description = "Mahkeme İl/İlçe Kodu")
    private String mahkemeIlIlceKodu;

    @Schema(description = "Mahkeme İl/İlçe Adı")
    private String mahkemeIlIlceAdi;

    @Schema(description = "Kaydeden Kullanıcı Id")
    private Long kaydedenKullaniciId;

    @Schema(description = "Kaydeden KullanıcıAdi")
    private String kaydedenKullaniciAdi;

    @Schema(description = "Kaydeden Adı/Soyadı")
    private String kaydedenAdiSoyadi;

    @Schema(description = "Soruşturma No")
    private String sorusturmaNo;

    @Schema(description = "Mahkeme Karar No")
    private String mahkemeKararNo;

    @Schema(description = "Açıklama")
    private String aciklama;

    @Schema(description = "Durumu")
    private String durumu;


}

