package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum SureTip {
	GUN(1),
	AY(2),
	HICBIRI(0);

	private final int value;

	SureTip(int value){
		this.value = value;
	}

	@JsonValue
	public int getValue(){
		return this.value;
	}

	@JsonCreator
	public static SureTip fromName(String name) {
		for (SureTip sureTip : SureTip.values()) {
			if (sureTip.name().equals(name)) {
				return sureTip;
			}
		}
		throw new IllegalArgumentException("Gecersiz sureTip: '" + name + "'");
	}

	//@JsonCreator
	public static SureTip fromValue(int value) {
		for (SureTip sureTip : SureTip.values()) {
			if (sureTip.value == value) {
				return sureTip;
			}
		}
		throw new IllegalArgumentException("Gecersiz sureTip: '" + value + "'");
	}
}
