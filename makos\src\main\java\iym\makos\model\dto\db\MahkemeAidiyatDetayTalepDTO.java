package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for MahkemeAidiyatDetayTalep entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Mahkeme Aidiyat Detay Talep bilgilerini içerir")
public class MahkemeAidiyatDetayTalepDTO {

    @Schema(description = "Mahkeme aidiyat detay talep ID")
    private Long id;

    @Schema(description = "İlişkili mahkeme karar ID")
    private Long iliskiliMahkemeKararId;

    @Schema(description = "Mahkeme karar ID")
    private Long mahkemeKararId;

    @Schema(description = "Mahkeme aidiyat kodu ekle", example = "AIDIYAT-EKLE")
    @Size(max = 25, message = "Mahkeme aidiyat kodu ekle 25 karakterden fazla olamaz")
    private String mahkemeAidiyatKoduEkle;

    @Schema(description = "Mahkeme aidiyat kodu çıkar", example = "AIDIYAT-CIKAR")
    @Size(max = 25, message = "Mahkeme aidiyat kodu çıkar 25 karakterden fazla olamaz")
    private String mahkemeAidiyatKoduCikar;

    @Schema(description = "Tarih", example = "2023-01-01T00:00:00")
    @NotNull(message = "Tarih boş olamaz")
    private Date tarih;

    @Schema(description = "Durum", example = "AKTIF")
    @Size(max = 15, message = "Durum 15 karakterden fazla olamaz")
    private String durum;

    @Schema(description = "Mahkeme karar detay ID")
    private Long mahkemeKararDetayId;
}
