package iym.common.util;

import org.springframework.context.ApplicationContext;

public class AppContextProvider {

    private static ApplicationContext applicationContext;

    public AppContextProvider(ApplicationContext context) {
        applicationContext = context;
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    public static Object getBean(String bean) {
        return getApplicationContext().getBean(bean);
    }

    public static <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }
}
