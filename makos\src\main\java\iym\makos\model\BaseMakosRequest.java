package iym.makos.model;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.UUID;
@NoArgsConstructor
@Data
@SuperBuilder
@ToString
@EqualsAndHashCode
public abstract  class BaseMakosRequest implements MakosRequest {

    @NotNull
    protected UUID id;

}
