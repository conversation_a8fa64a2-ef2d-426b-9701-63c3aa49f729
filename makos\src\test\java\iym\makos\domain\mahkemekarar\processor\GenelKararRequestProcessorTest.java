package iym.makos.domain.mahkemekarar.processor;

import iym.common.validation.ValidationResult;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mahkemekarar.dbhandler.MahkemeKararDBSaveHandler;
import iym.makos.model.dto.mahkemekarar.id.GenelEvrakRequest;
import iym.makos.model.dto.mahkemekarar.it.GenelKararResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.validator.IMahkemeKararRequestValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Date;

import static iym.makos.domain.testdata.TestDataBuilder.*;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Unit tests for GenelKararRequestProcessor.
 *
 * Tests the processor implementation for general evrak requests.
 * Verifies validation, processing, and error handling scenarios.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("GenelKararRequestProcessor Unit Tests")
class GenelKararRequestProcessorTest extends BaseDomainUnitTest {
    
    @Mock
    private IMahkemeKararRequestValidator<GenelEvrakRequest> mockValidator;
    
    @Mock
    private MahkemeKararDBSaveHandler<GenelEvrakRequest> mockSaver;
    
    @InjectMocks
    private GenelKararRequestProcessor processor;
    
    private GenelEvrakRequest testRequest;
    private UserDetailsImpl testUser;
    
    @BeforeEach
    void setUp() {
        testRequest = createValidGenelEvrakRequest();
        testUser = createTestUser();
    }
    
    @Test
    @DisplayName("Should return success response when validation passes and save succeeds")
    void shouldReturnSuccessResponse_whenValidationPassesAndSaveSucceeds() throws Exception {
        // Given
        Long expectedEvrakId = 12345L;
        when(mockValidator.validate(testRequest)).thenReturn(new ValidationResult(true));
        when(mockSaver.kaydet(eq(testRequest), any(Date.class), eq(testUser.getId())))
            .thenReturn(expectedEvrakId);

        // When
        GenelKararResponse response = processor.process(testRequest, testUser);

        // Then
        assertThat(response).isNotNull();
        assertResponseSuccess(response);
        assertThat(response.getEvrakId()).isEqualTo(expectedEvrakId);

        // Verify interactions
        verify(mockValidator).validate(testRequest);
        verify(mockSaver).kaydet(eq(testRequest), any(Date.class), eq(testUser.getId()));
    }
    
    @Test
    @DisplayName("Should return invalid request response when validation fails")
    void shouldReturnInvalidRequestResponse_whenValidationFails() throws Exception {
        // Given
        String validationError = "Evrak numarası boş olamaz";
        ValidationResult invalidResult = new ValidationResult(Arrays.asList(validationError));
        when(mockValidator.validate(testRequest)).thenReturn(invalidResult);
        
        // When
        GenelKararResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertThat(response).isNotNull();
        assertResponseInvalidRequest(response);
        assertThat(response.getResponse().getResponseMessage()).contains(validationError);
        assertThat(response.getEvrakId()).isNull();
        
        // Verify validator was called but saver was not
        verify(mockValidator).validate(testRequest);
        verify(mockSaver, never()).kaydet(any(), any(), any());
    }
    
    @Test
    @DisplayName("Should return error response when save operation fails")
    void shouldReturnErrorResponse_whenSaveOperationFails() throws Exception {
        // Given
        when(mockValidator.validate(testRequest)).thenReturn(new ValidationResult(true));
        when(mockSaver.kaydet(eq(testRequest), any(Date.class), eq(testUser.getId())))
            .thenThrow(new RuntimeException("Database connection failed"));

        // When
        GenelKararResponse response = processor.process(testRequest, testUser);

        // Then
        assertThat(response).isNotNull();
        assertResponseError(response, MakosResponseCode.FAILED);
        // The current implementation returns "INTERNAL ERROR", so we test for that
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("INTERNAL ERROR");
        assertThat(response.getEvrakId()).isNull();

        // Verify both validator and saver were called
        verify(mockValidator).validate(testRequest);
        verify(mockSaver).kaydet(eq(testRequest), any(Date.class), eq(testUser.getId()));
    }
    
    @Test
    @DisplayName("Should handle null request gracefully")
    void shouldHandleNullRequest_gracefully() throws Exception {
        // When
        GenelKararResponse response = processor.process(null, testUser);

        // Then
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("Request cannot be null");

        // Verify validator and saver were never called due to early null check
        verify(mockValidator, never()).validate(any());
        verify(mockSaver, never()).kaydet(any(), any(), any());
    }
    
    @Test
    @DisplayName("Should handle null user gracefully")
    void shouldHandleNullUser_gracefully() throws Exception {
        // When
        GenelKararResponse response = processor.process(testRequest, null);

        // Then
        assertThat(response).isNotNull();
        assertResponseError(response, MakosResponseCode.INVALID_REQUEST);
        assertThat(response.getResponse().getResponseMessage()).isEqualTo("User cannot be null");

        // Verify validator and saver were never called due to early null check
        verify(mockValidator, never()).validate(any());
        verify(mockSaver, never()).kaydet(any(), any(), any());
    }
    
    @Test
    @DisplayName("Should return correct request and response types")
    void shouldReturnCorrectRequestAndResponseTypes() throws Exception {
        // When
        Class<GenelEvrakRequest> requestType = processor.getRelatedRequestType();
        Class<GenelKararResponse> responseType = processor.getRelatedResponseType();
        
        // Then
        assertThat(requestType).isEqualTo(GenelEvrakRequest.class);
        assertThat(responseType).isEqualTo(GenelKararResponse.class);
    }
    
    @Test
    @DisplayName("Should handle validation with multiple errors")
    void shouldHandleValidation_withMultipleErrors() throws Exception {
        // Given
        ValidationResult multipleErrorsResult = new ValidationResult(Arrays.asList(
            "Evrak numarası boş olamaz",
            "Mahkeme kodu geçersiz",
            "Karar tarihi gelecekte olamaz"
        ));
        when(mockValidator.validate(testRequest)).thenReturn(multipleErrorsResult);
        
        // When
        GenelKararResponse response = processor.process(testRequest, testUser);
        
        // Then
        assertThat(response).isNotNull();
        assertResponseInvalidRequest(response);
        assertThat(response.getResponse().getResponseMessage())
            .contains("Evrak numarası boş olamaz")
            .contains("Mahkeme kodu geçersiz")
            .contains("Karar tarihi gelecekte olamaz");
        
        verify(mockValidator).validate(testRequest);
        verify(mockSaver, never()).kaydet(any(), any(), any());
    }
}
