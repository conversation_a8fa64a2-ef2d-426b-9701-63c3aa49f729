package iym.makos.validator;

import iym.common.enums.EvrakKurum;
import iym.common.enums.GuncellemeTip;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.mk.MahkemeKararSucTipleri;
import iym.common.model.entity.iym.SucTipi;
import iym.common.service.db.mk.DbMahkemeKararSucTipleriService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbSucTipiService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.dto.mahkemekarar.id.IDSucTipiGuncellemeRequest;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.SucTipiGuncellemeDetay;
import iym.makos.model.api.SucTipiGuncellemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class IDSucTipiGuncellemeValidator extends MahkemeKararRequestValidatorBase<IDSucTipiGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbSucTipiService dbSucTipiService;
    private DbMahkemeKararSucTipleriService dBMahkemeKararSucTipleriService;


    @Autowired
    public IDSucTipiGuncellemeValidator(DbMahkemeKararService dbMahkemeKararService
            , DbMahkemeKararSucTipleriService dBMahkemeKararSucTipleriService
             , DbSucTipiService dbSucTipiService
    ) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dBMahkemeKararSucTipleriService = dBMahkemeKararSucTipleriService;
        this.dbSucTipiService = dbSucTipiService;
    }

    @Override
    protected ValidationResult doValidate(IDSucTipiGuncellemeRequest request) {

        try {
            ValidationResult validationResult = new ValidationResult(true);

            MahkemeKararTip mahkemeKararTip = request.getMahkemeKararBilgisi().getMahkemeKararTipi();
            String evrakGelenKurumKodu = request.getEvrakDetay().getEvrakKurumKodu();
            EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

            for (SucTipiGuncellemeKararDetay sucTipiGuncellemeKararDetay : request.getSucTipiGuncellemeKararDetayListesi()) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = sucTipiGuncellemeKararDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("İlişkili mahkeme karar boş olamaz.");
                } else {
                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format(MakosResponseErrorCodes.MK_BULUNAMADI, iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu(), iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {
                        MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();
                        for (SucTipiGuncellemeDetay sucTipiGuncellemeDetay : CommonUtils.safeList(sucTipiGuncellemeKararDetay.getSucTipiGuncellemeDetayListesi())) {
                            String sucTipiKodu = sucTipiGuncellemeDetay.getSucTipiKodu();

                            if (sucTipiGuncellemeDetay.getGuncellemeTip() == GuncellemeTip.CIKAR) {
                                //Çıkarılacak suç tipi ilgili kararda var mı?
                                Optional<MahkemeKararSucTipleri> existingMahkemeKararSucOpt = dBMahkemeKararSucTipleriService.findByMahkemeKararIdAndSucTipKodu(iliskiliMahkemeKarar.getId(), sucTipiKodu);
                                if(existingMahkemeKararSucOpt.isEmpty()){
                                    String errMsg = String.format(MakosResponseErrorCodes.MK_SUCTIPI_BULUNAMADI, iliskiliMahkemeKarar.getId(), sucTipiKodu);
                                    validationResult.addFailedReason(errMsg);
                                }
                            }
                            else{
                                //Eklenecek suç tipi var mı?
                                Optional<SucTipi> sucTipiOpt = dbSucTipiService.findBySucTipiKodu(sucTipiKodu);
                                if(sucTipiOpt.isEmpty()){
                                    String errMsg = String.format(MakosResponseErrorCodes.SUCTIPI_BULUNAMADI, sucTipiKodu);
                                    validationResult.addFailedReason(errMsg);
                                }
                            }
                        }
                    }
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME;
    }
}

