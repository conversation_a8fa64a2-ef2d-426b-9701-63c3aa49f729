package iym.makos.model.api;

import iym.common.model.api.Hedef;
import iym.common.enums.SorguTipi;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.time.LocalDateTime;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class ITHedefDetay {

  /*
  <SORGU_TIPI>530</SORGU_TIPI>
  <HEDEF_NO>9050516</HEDEF_NO>
  <KARSI_HEDEF_NO />
  <BASLANGIC_TARIHI>10/01/2020 12:00:00</BASLANGIC_TARIHI>
  <BITIS_TARIHI>20/01/2020 12:00:00</BITIS_TARIHI>
  <TESPIT_TURU>10</TESPIT_TURU>
  <TESPIT_TURU_DETAY>3</TESPIT_TURU_DETAY>
  <ACIKLAMA />
   */

  @NotNull
  private SorguTipi sorguTipi;

  @NotNull
  @Valid
  private Hedef hedef;

  @Valid
  private Hedef karsiHedef;

  @NotNull
  private LocalDateTime baslamaTarihi;

  @NotNull
  private LocalDateTime bitisTarihi;

  // TODO: create an enum???
  @NotNull
  private String tespitTuru;

  private String tespitTuruDetay;

  private String aciklama;

}

