package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for EvrakGelenKurumlar entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Evrak gelen kurumlar bilgilerini içerir")
public class EvrakGelenKurumlarDTO {

    @Schema(description = "Evrak gelen kurum ID", example = "1")
    private Long id;

    @Schema(description = "Kurum kodu", example = "01")
    @NotNull(message = "Kurum kodu boş olamaz")
    @Size(max = 10, message = "Kurum kodu 10 karakterden fazla olamaz")
    private String kurumKod;

    @Schema(description = "Kurum adı", example = "ADALET BAKANLIĞI")
    @NotNull(message = "Kurum adı boş olamaz")
    @Size(max = 50, message = "Kurum adı 50 karakterden fazla olamaz")
    private String kurumAdi;

    @Schema(description = "Kurum", example = "ADALET BAKANLIĞI")
    @Size(max = 64, message = "Kurum 64 karakterden fazla olamaz")
    private String kurum;

    @Schema(description = "Sıra numarası", example = "1")
    private Long idx;
}
