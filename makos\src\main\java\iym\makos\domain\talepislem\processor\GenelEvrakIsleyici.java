package iym.makos.domain.talepislem.processor;

import iym.common.enums.KararTuru;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GenelEvrakIsleyici extends IDMahkemeKararIsleyiciBase {

    @Override
    protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {
        return MahkemeKararTalepUpdateResponse.builder()
                .requestId(request.getId())
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.SUCCESS)
                        .build())
                .build();
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.GENEL_EVRAK;
    }

}

