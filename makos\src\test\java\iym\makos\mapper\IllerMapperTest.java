package iym.makos.mapper;

import iym.common.model.entity.iym.Iller;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.model.dto.db.IllerDTO;

import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class IllerMapperTest {

    private IllerMapper illerMapper;
    private Iller iller;
    private IllerDTO illerDTO;

    @BeforeEach
    void setUp() {
        illerMapper = new IllerMapper();

        iller = Iller.builder()
                .ilKod("0600")
                .ilAdi("ANKARA")
                .ilceAdi("MERKEZ")
                .build();

        illerDTO = IllerDTO.builder()
                .ilKod("0600")
                .ilAdi("ANKARA")
                .ilceAdi("MERKEZ")
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        IllerDTO result = illerMapper.toDto(iller);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getIlKod()).isEqualTo(iller.getIlKod());
        assertThat(result.getIlAdi()).isEqualTo(iller.getIlAdi());
        assertThat(result.getIlceAdi()).isEqualTo(iller.getIlceAdi());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        IllerDTO result = illerMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        Iller result = illerMapper.toEntity(illerDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getIlKod()).isEqualTo(illerDTO.getIlKod());
        assertThat(result.getIlAdi()).isEqualTo(illerDTO.getIlAdi());
        assertThat(result.getIlceAdi()).isEqualTo(illerDTO.getIlceAdi());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        Iller result = illerMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        Iller existingEntity = Iller.builder()
                .ilKod("0600")
                .ilAdi("ANKARA")
                .ilceAdi("MERKEZ")
                .build();

        IllerDTO updatedDto = IllerDTO.builder()
                .ilKod("0601") // Should not be updated
                .ilAdi("ANKARA UPDATED")
                .ilceAdi("ÇANKAYA")
                .build();

        // When
        Iller result = illerMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getIlKod()).isEqualTo("0600"); // Should not be updated
        assertThat(result.getIlAdi()).isEqualTo("ANKARA UPDATED");
        assertThat(result.getIlceAdi()).isEqualTo("ÇANKAYA");
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        Iller existingEntity = Iller.builder()
                .ilKod("0600")
                .ilAdi("ANKARA")
                .ilceAdi("MERKEZ")
                .build();

        // When
        Iller result = illerMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        Iller result = illerMapper.updateEntityFromDto(null, illerDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<Iller> entityList = Arrays.asList(iller, iller);

        // When
        List<IllerDTO> result = illerMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getIlKod()).isEqualTo(iller.getIlKod());
        assertThat(result.get(1).getIlKod()).isEqualTo(iller.getIlKod());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<IllerDTO> result = illerMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<IllerDTO> dtoList = Arrays.asList(illerDTO, illerDTO);

        // When
        List<Iller> result = illerMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getIlKod()).isEqualTo(illerDTO.getIlKod());
        assertThat(result.get(1).getIlKod()).isEqualTo(illerDTO.getIlKod());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<Iller> result = illerMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
