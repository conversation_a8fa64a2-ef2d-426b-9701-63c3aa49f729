-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAH_KARAR_TIPLERI if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'XML_ISLEM_LOG_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE IYM.XML_ISLEM_LOG_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAH_KARAR_TIPLERI table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'XML_ISLEM_LOG';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE IYM.XML_ISLEM_LOG (
      ID NUMBER NOT NULL,
      KU<PERSON>ANICI_ID NUMBER NOT NULL,
      IP VARCHAR2(100 BYTE),
      TARIH DATE,
      ISLEM VARCHAR2(4000 BYTE),
      ISLEM_TABLO VARCHAR2(100 BYTE),
      ISLEM_TABLO_REF_ID NUMBER,
      ISLEM_ID NUMBER,
      KULLANICI_ADI VARCHAR2(100 BYTE),
      CONSTRAINT XML_ISLEM_LOG PRIMARY KEY(ID) ENABLE
    )';

  END IF;
END;
/



COMMIT;
