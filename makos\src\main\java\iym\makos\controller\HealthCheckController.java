package iym.makos.controller;


import iym.common.enums.ResponseCode;
import iym.common.model.api.ApiResponse;
import iym.makos.model.dto.healthcheck.HealthCheckResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/check")
@Slf4j
@Validated
public class HealthCheckController {

    public static final String API_ALIVE_MESSAGE = "Makos API is alive";

    @GetMapping("healthCheck")
    public ResponseEntity<HealthCheckResponse> healthCheck() {
        HealthCheckResponse response = HealthCheckResponse.builder()
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage(API_ALIVE_MESSAGE)
                        .build())
                .build();
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @GetMapping("healthCheckAuthorized")
    public ResponseEntity<HealthCheckResponse> healthCheckAuthorized() {
        HealthCheckResponse response = HealthCheckResponse.builder()
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage(API_ALIVE_MESSAGE)
                        .build())
                .build();
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('ADMIN')")
    @GetMapping("healthCheckAdmin")
    public ResponseEntity<HealthCheckResponse> healthCheckAdmin() {
        HealthCheckResponse response = HealthCheckResponse.builder()
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage(API_ALIVE_MESSAGE)
                        .build())
                .build();
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    @PreAuthorize("hasRole('ROLE_QUERY_ADMIN')")
    @GetMapping("healthCheckQueryAdmin")
    public ResponseEntity<HealthCheckResponse> healthCheckQueryAdmin() {
        HealthCheckResponse response = HealthCheckResponse.builder()
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage(API_ALIVE_MESSAGE)
                        .build())
                .build();
        return new ResponseEntity<>(response, HttpStatus.OK);
    }
}
