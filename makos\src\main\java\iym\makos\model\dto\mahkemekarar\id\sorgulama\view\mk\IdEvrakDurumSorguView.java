package iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mk;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description =  "ID ")
public class IdEvrakDurumSorguView {

    @Schema(description = "Evrak Id")
    private Long evrakId;

    @Schema(description = "Evrak Sıra No")
    private String evrakSiraNo;

    @Schema(description = "Kurum Evrak No")
    private String kurumEvrakNo;

    @Schema(description = "Evrak Giriş Tarihi")
    private Date girisTarihi;

    @Schema(description = "Evrak OnayTarihi")
    private Date onayTarihi;

    @Schema(description = "Durumu")
    private String durumu;


}

