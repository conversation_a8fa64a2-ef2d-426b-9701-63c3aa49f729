package iym.makos.model.dto.mahkemekarar.it;

import iym.makos.model.dto.mahkemekarar.MahkemeKararResponse;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class GenelKararResponse extends MahkemeKararResponse {

  @NotNull
  private Long evrakId;

}

