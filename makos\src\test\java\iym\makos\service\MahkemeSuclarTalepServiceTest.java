package iym.makos.service;

import iym.common.model.entity.iym.talep.MahkemeSuclarTalep;
import iym.common.service.db.DbMahkemeSuclarTalepService;
import iym.makos.mapper.MahkemeSuclarTalepMapper;
import iym.makos.service.db.MahkemeSuclarTalepService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.model.dto.db.MahkemeSuclarTalepDTO;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MahkemeSuclarTalepServiceTest {

    @Mock
    private DbMahkemeSuclarTalepService dbMahkemeSuclarTalepService;

    @Mock
    private MahkemeSuclarTalepMapper mahkemeSuclarTalepMapper;

    @InjectMocks
    private MahkemeSuclarTalepService mahkemeSuclarTalepService;

    private MahkemeSuclarTalep mahkemeSuclarTalep;
    private MahkemeSuclarTalepDTO mahkemeSuclarTalepDTO;
    private List<MahkemeSuclarTalep> mahkemeSuclarTalepList;
    private List<MahkemeSuclarTalepDTO> mahkemeSuclarTalepDTOList;

    @BeforeEach
    void setUp() {
        mahkemeSuclarTalep = MahkemeSuclarTalep.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .sucTipKodu("01")
                .durumu("AKTIF")
                .build();

        mahkemeSuclarTalepDTO = MahkemeSuclarTalepDTO.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .sucTipKodu("01")
                .durumu("AKTIF")
                .build();

        mahkemeSuclarTalepList = Arrays.asList(
                mahkemeSuclarTalep,
                MahkemeSuclarTalep.builder()
                        .id(2L)
                        .mahkemeKararTalepId(200L)
                        .sucTipKodu("02")
                        .durumu("PASIF")
                        .build()
        );

        mahkemeSuclarTalepDTOList = Arrays.asList(
                mahkemeSuclarTalepDTO,
                MahkemeSuclarTalepDTO.builder()
                        .id(2L)
                        .mahkemeKararTalepId(200L)
                        .sucTipKodu("02")
                        .durumu("PASIF")
                        .build()
        );
    }

    @Test
    void findAll_shouldReturnAllMahkemeSuclarTalep() {
        // Given
        when(dbMahkemeSuclarTalepService.findAll()).thenReturn(mahkemeSuclarTalepList);
        when(mahkemeSuclarTalepMapper.toDtoList(mahkemeSuclarTalepList)).thenReturn(mahkemeSuclarTalepDTOList);

        // When
        List<MahkemeSuclarTalepDTO> result = mahkemeSuclarTalepService.findAll();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(mahkemeSuclarTalepDTOList, result);
        verify(dbMahkemeSuclarTalepService).findAll();
        verify(mahkemeSuclarTalepMapper).toDtoList(mahkemeSuclarTalepList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfMahkemeSuclarTalep() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<MahkemeSuclarTalep> mahkemeSuclarTalepPage = new PageImpl<>(mahkemeSuclarTalepList, pageable, mahkemeSuclarTalepList.size());
        
        when(dbMahkemeSuclarTalepService.findAll(pageable)).thenReturn(mahkemeSuclarTalepPage);
        when(mahkemeSuclarTalepMapper.toDtoList(mahkemeSuclarTalepList)).thenReturn(mahkemeSuclarTalepDTOList);

        // When
        Page<MahkemeSuclarTalepDTO> result = mahkemeSuclarTalepService.findAll(pageable);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
        assertEquals(mahkemeSuclarTalepDTOList, result.getContent());
        verify(dbMahkemeSuclarTalepService).findAll(pageable);
        verify(mahkemeSuclarTalepMapper).toDtoList(mahkemeSuclarTalepList);
    }

    @Test
    void findById_shouldReturnMahkemeSuclarTalep_whenExists() {
        // Given
        when(dbMahkemeSuclarTalepService.findById(1L)).thenReturn(Optional.of(mahkemeSuclarTalep));
        when(mahkemeSuclarTalepMapper.toDto(mahkemeSuclarTalep)).thenReturn(mahkemeSuclarTalepDTO);

        // When
        MahkemeSuclarTalepDTO result = mahkemeSuclarTalepService.findById(1L);

        // Then
        assertNotNull(result);
        assertEquals(mahkemeSuclarTalepDTO, result);
        verify(dbMahkemeSuclarTalepService).findById(1L);
        verify(mahkemeSuclarTalepMapper).toDto(mahkemeSuclarTalep);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        when(dbMahkemeSuclarTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> mahkemeSuclarTalepService.findById(1L));
        verify(dbMahkemeSuclarTalepService).findById(1L);
        verify(mahkemeSuclarTalepMapper, never()).toDto(any());
    }

    @Test
    void findByMahkemeKararId_shouldReturnMahkemeSuclarTalepList() {
        // Given
        when(dbMahkemeSuclarTalepService.findByMahkemeKararTalepId(100L)).thenReturn(mahkemeSuclarTalepList);
        when(mahkemeSuclarTalepMapper.toDtoList(mahkemeSuclarTalepList)).thenReturn(mahkemeSuclarTalepDTOList);

        // When
        List<MahkemeSuclarTalepDTO> result = mahkemeSuclarTalepService.findByMahkemeKararId(100L);

        // Then
        assertNotNull(result);
        assertEquals(mahkemeSuclarTalepDTOList, result);
        verify(dbMahkemeSuclarTalepService).findByMahkemeKararTalepId(100L);
        verify(mahkemeSuclarTalepMapper).toDtoList(mahkemeSuclarTalepList);
    }




    @Test
    void findByMahkemeKararIdAndMahkemeSucTipKod_shouldReturnMahkemeSuclarTalep_whenExists() {
        // Given
        when(dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01"))
                .thenReturn(Optional.of(mahkemeSuclarTalep));
        when(mahkemeSuclarTalepMapper.toDto(mahkemeSuclarTalep)).thenReturn(mahkemeSuclarTalepDTO);

        // When
        MahkemeSuclarTalepDTO result = mahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01");

        // Then
        assertNotNull(result);
        assertEquals(mahkemeSuclarTalepDTO, result);
        verify(dbMahkemeSuclarTalepService).findByMahkemeKararTalepIdAndSucTipKodu(100L, "01");
        verify(mahkemeSuclarTalepMapper).toDto(mahkemeSuclarTalep);
    }

    @Test
    void findByMahkemeKararIdAndMahkemeSucTipKod_shouldThrowException_whenNotExists() {
        // Given
        when(dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01"))
                .thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, 
                () -> mahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01"));
        verify(dbMahkemeSuclarTalepService).findByMahkemeKararTalepIdAndSucTipKodu(100L, "01");
        verify(mahkemeSuclarTalepMapper, never()).toDto(any());
    }

    @Test
    void create_shouldCreateMahkemeSuclarTalep() {
        // Given
        when(dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01"))
                .thenReturn(Optional.empty());
        when(mahkemeSuclarTalepMapper.toEntity(mahkemeSuclarTalepDTO)).thenReturn(mahkemeSuclarTalep);
        when(mahkemeSuclarTalepMapper.toDto(mahkemeSuclarTalep)).thenReturn(mahkemeSuclarTalepDTO);

        // When
        MahkemeSuclarTalepDTO result = mahkemeSuclarTalepService.create(mahkemeSuclarTalepDTO);

        // Then
        assertNotNull(result);
        assertEquals(mahkemeSuclarTalepDTO, result);
        verify(dbMahkemeSuclarTalepService).findByMahkemeKararTalepIdAndSucTipKodu(100L, "01");
        verify(mahkemeSuclarTalepMapper).toEntity(mahkemeSuclarTalepDTO);
        verify(dbMahkemeSuclarTalepService).save(mahkemeSuclarTalep);
        verify(mahkemeSuclarTalepMapper).toDto(mahkemeSuclarTalep);
    }

    @Test
    void create_shouldThrowException_whenMahkemeSuclarTalepAlreadyExists() {
        // Given
        when(dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01"))
                .thenReturn(Optional.of(mahkemeSuclarTalep));

        // When/Then
        assertThrows(ResponseStatusException.class, () -> mahkemeSuclarTalepService.create(mahkemeSuclarTalepDTO));
        verify(dbMahkemeSuclarTalepService).findByMahkemeKararTalepIdAndSucTipKodu(100L, "01");
        verify(mahkemeSuclarTalepMapper, never()).toEntity(any());
        verify(dbMahkemeSuclarTalepService, never()).save(any());
    }

    @Test
    void update_shouldUpdateMahkemeSuclarTalep_whenExists() {
        // Given
        when(dbMahkemeSuclarTalepService.findById(1L)).thenReturn(Optional.of(mahkemeSuclarTalep));
        when(dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01"))
                .thenReturn(Optional.of(mahkemeSuclarTalep));
        when(mahkemeSuclarTalepMapper.updateEntityFromDto(mahkemeSuclarTalep, mahkemeSuclarTalepDTO))
                .thenReturn(mahkemeSuclarTalep);
        when(mahkemeSuclarTalepMapper.toDto(mahkemeSuclarTalep)).thenReturn(mahkemeSuclarTalepDTO);

        // When
        MahkemeSuclarTalepDTO result = mahkemeSuclarTalepService.update(1L, mahkemeSuclarTalepDTO);

        // Then
        assertNotNull(result);
        assertEquals(mahkemeSuclarTalepDTO, result);
        verify(dbMahkemeSuclarTalepService).findById(1L);
        verify(dbMahkemeSuclarTalepService).findByMahkemeKararTalepIdAndSucTipKodu(100L, "01");
        verify(mahkemeSuclarTalepMapper).updateEntityFromDto(mahkemeSuclarTalep, mahkemeSuclarTalepDTO);
        verify(dbMahkemeSuclarTalepService).update(mahkemeSuclarTalep);
        verify(mahkemeSuclarTalepMapper).toDto(mahkemeSuclarTalep);
    }

    @Test
    void update_shouldThrowException_whenMahkemeSuclarTalepNotExists() {
        // Given
        when(dbMahkemeSuclarTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> mahkemeSuclarTalepService.update(1L, mahkemeSuclarTalepDTO));
        verify(dbMahkemeSuclarTalepService).findById(1L);
        verify(mahkemeSuclarTalepMapper, never()).updateEntityFromDto(any(), any());
        verify(dbMahkemeSuclarTalepService, never()).update(any());
    }

    @Test
    void update_shouldThrowException_whenUpdateWouldCreateDuplicate() {
        // Given
        MahkemeSuclarTalep existingMahkemeSuclarTalep = MahkemeSuclarTalep.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .sucTipKodu("01")
                .durumu("AKTIF")
                .build();

        MahkemeSuclarTalep duplicateMahkemeSuclarTalep = MahkemeSuclarTalep.builder()
                .id(2L)
                .mahkemeKararTalepId(100L)
                .sucTipKodu("01")
                .durumu("PASIF")
                .build();

        when(dbMahkemeSuclarTalepService.findById(1L)).thenReturn(Optional.of(existingMahkemeSuclarTalep));
        when(dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(100L, "01"))
                .thenReturn(Optional.of(duplicateMahkemeSuclarTalep));

        // When/Then
        assertThrows(ResponseStatusException.class, () -> mahkemeSuclarTalepService.update(1L, mahkemeSuclarTalepDTO));
        verify(dbMahkemeSuclarTalepService).findById(1L);
        verify(dbMahkemeSuclarTalepService).findByMahkemeKararTalepIdAndSucTipKodu(100L, "01");
        verify(mahkemeSuclarTalepMapper, never()).updateEntityFromDto(any(), any());
        verify(dbMahkemeSuclarTalepService, never()).update(any());
    }

    @Test
    void delete_shouldDeleteMahkemeSuclarTalep_whenExists() {
        // Given
        when(dbMahkemeSuclarTalepService.findById(1L)).thenReturn(Optional.of(mahkemeSuclarTalep));

        // When
        mahkemeSuclarTalepService.delete(1L);

        // Then
        verify(dbMahkemeSuclarTalepService).findById(1L);
        verify(dbMahkemeSuclarTalepService).delete(mahkemeSuclarTalep);
    }

    @Test
    void delete_shouldThrowException_whenMahkemeSuclarTalepNotExists() {
        // Given
        when(dbMahkemeSuclarTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> mahkemeSuclarTalepService.delete(1L));
        verify(dbMahkemeSuclarTalepService).findById(1L);
        verify(dbMahkemeSuclarTalepService, never()).delete(any());
    }
}
