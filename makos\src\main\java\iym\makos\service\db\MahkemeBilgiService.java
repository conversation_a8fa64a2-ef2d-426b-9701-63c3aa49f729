package iym.makos.service.db;

import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.makos.model.dto.db.MahkemeDTO;
import iym.makos.mapper.MahkemeBilgiMapper;
import iym.makos.model.dto.db.MahkemeKoduDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeBilgiService {

    private final DbMahkemeBilgiService dbMahkemeBilgiService;
    private final MahkemeBilgiMapper mahkemeBilgiMapper;

    public MahkemeDTO findByMahkemeKodu(String mahkemeKodu){
        return dbMahkemeBilgiService.findByMahkemeKodu(mahkemeKodu)
                .map(mahkemeBilgiMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme bilgisi bulunamadı"));
    }

    public List<MahkemeKoduDTO> findAllMahkemeKodlari(){
        List<MahkemeBilgi> mahkemeAdlari = dbMahkemeBilgiService.findAll();
        return mahkemeBilgiMapper.toMahkemeKoduDtoList(mahkemeAdlari);
    }

}
