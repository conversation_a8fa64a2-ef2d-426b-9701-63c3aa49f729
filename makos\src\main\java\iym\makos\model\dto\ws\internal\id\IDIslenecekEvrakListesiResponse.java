package iym.makos.model.dto.ws.internal.id;

import iym.common.model.api.ApiResponseBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class IDIslenecekEvrakListesiResponse extends ApiResponseBase {
    private List<IDIslenecekEvrakModel> islenecekEvrakListesi;
} 