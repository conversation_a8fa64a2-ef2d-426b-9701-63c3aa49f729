package iym.makos.model.dto.mahkemekarar.id;

import iym.common.validation.ValidationResult;
import iym.makos.model.MakosRequest;
import iym.makos.validator.custom.MakosRequestValid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDMahkemeKararAtamaRequest implements MakosRequest {

    @NotNull
    Long evrakId;

    @NotNull
    Long atananKullaniciId;

    @NotNull
    UUID id;



    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDMahkemeKararOnaylamaRequest is valid");

        try {

            ValidationResult validationResult = new ValidationResult(true);


            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

}

