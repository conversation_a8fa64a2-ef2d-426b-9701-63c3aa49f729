package iym.makos.domain.talepislem.idislemeaktar;

import iym.common.enums.KararTuru;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.MahkemeSucTipiDetayTalepRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class IDSucTipiGuncellemeIslemeAktarici extends IDMahkemeKararTalepIslemeAktariciBase {

    private final MahkemeSucTipiDetayTalepRepo mahkemeSucTipiDetayTalepRepo;
    private final DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo;

    @Autowired
    public IDSucTipiGuncellemeIslemeAktarici(MahkemeSucTipiDetayTalepRepo mahkemeSucTipiDetayTalepRepo
            , DetayMahkemeKararTalepRepo dMahkemeKararTalepRepo
    ) {
        this.mahkemeSucTipiDetayTalepRepo = mahkemeSucTipiDetayTalepRepo;
        this.dMahkemeKararTalepRepo = dMahkemeKararTalepRepo;
    }

    protected boolean updateRelatedTables(Long mahkemeKararTalepId) {
        boolean result = false;
        try {
            String durum = "ISLEMDE";
            CommonUtils.safeList(dMahkemeKararTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId))
                    .forEach(dMahkemeKararTalep -> {
                        dMahkemeKararTalep.setDurum(durum);
                        dMahkemeKararTalepRepo.save(dMahkemeKararTalep);

                        CommonUtils.safeList(mahkemeSucTipiDetayTalepRepo.findByMahkemeKararDetayTalepId(dMahkemeKararTalep.getId()))
                                .forEach(mahkemeSucTipiDetayTalep -> {
                                    mahkemeSucTipiDetayTalep.setDurum(durum);
                                    mahkemeSucTipiDetayTalepRepo.save(mahkemeSucTipiDetayTalep);
                                });
                    });

            result = true;

        } catch (Exception ex) {
            log.error("IDSucTipiGuncellemeIslemeAktarici process failed, mahkemeKararTalepId:{}", mahkemeKararTalepId, ex);
        }
        return result;
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME;
    }


}

