package iym.makos.domain.mahkemekarar.processor;

import iym.common.validation.ValidationResult;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.model.dto.mahkemekarar.id.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.model.dto.mahkemekarar.id.IDAidiyatBilgisiGuncellemeResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class IDAidiyatBilgisiGuncellemeRequestProcessor extends MakosRequestProcessorBase<IDAidiyatBilgisiGuncellemeRequest, IDAidiyatBilgisiGuncellemeResponse> {

    @Override
    public IDAidiyatBilgisiGuncellemeResponse process(IDAidiyatBilgisiGuncellemeRequest request, UserDetailsImpl islemYapanKullanici) {
        // Early null checks
        if (request == null) {
            log.error("IDAidiyatBilgisiGuncelleme process failed: request is null");
            return IDAidiyatBilgisiGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage("Request cannot be null")
                            .build())
                    .build();
        }

        if (islemYapanKullanici == null) {
            log.error("IDAidiyatBilgisiGuncelleme process failed: user is null, requestId:{}", request.getId());
            return IDAidiyatBilgisiGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.INVALID_REQUEST)
                            .responseMessage("User cannot be null")
                            .build())
                    .build();
        }

        try {
            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {

                return IDAidiyatBilgisiGuncellemeResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            Date kayitTarihi = new Date();
            Long kaydedenKullaniciId = islemYapanKullanici.getId();
            Long mahkemeKararTalepId = requestSaver.kaydet(request, kayitTarihi, kaydedenKullaniciId);

            return IDAidiyatBilgisiGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .evrakId(mahkemeKararTalepId)
                    .build();

        } catch (Exception ex) {
            // Null-safe logging - null checks already done above
            String evrakNo = request.getEvrakDetay() != null ? request.getEvrakDetay().getEvrakNo() : "null";
            log.error("IDAidiyatBilgisiGuncelleme process failed. id:{}, evrakNo:{}", request.getId(), evrakNo, ex);
            return IDAidiyatBilgisiGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("INTERNAL ERROR")
                            .build())
                    .build();
        }
    }

}
