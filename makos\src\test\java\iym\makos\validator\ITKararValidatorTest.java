package iym.makos.validator;

import iym.common.enums.KararTuru;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.model.dto.mahkemekarar.it.ITKararRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static iym.makos.domain.testdata.TestDataBuilder.createValidITKararRequest;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Unit tests for ITKararValidator.
 *
 * Tests validation logic for IT karar requests.
 * Uses LENIENT mock settings for flexibility.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ITKararValidator Unit Tests")
class ITKararValidatorTest extends BaseDomainUnitTest {

    @Mock
    private MahkemeKararRequestCommonValidator mockCommonValidator;

    @InjectMocks
    private ITKararValidator validator;

    private ITKararRequest validRequest;

    @BeforeEach
    void setUp() {
        validRequest = createValidITKararRequest();
        setupValidMocks();
    }

    private void setupValidMocks() {
        // Mock common validator to return valid result
        when(mockCommonValidator.validate(any(ITKararRequest.class)))
                .thenReturn(new ValidationResult(true));

        // Set the common validator mock
        validator.setMahkemeKararRequestCommonValidator(mockCommonValidator);
    }

    @Test
    @DisplayName("Should pass validation when request is valid")
    void shouldPassValidation_whenRequestIsValid() {
        // Given
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationSuccess(result);
    }

    @Test
    @DisplayName("Should return correct karar turu")
    void shouldReturnCorrectKararTuru() {
        // When
        KararTuru result = validator.getRelatedKararTuru();

        // Then
        assertEquals(KararTuru.ILETISIMIN_TESPITI, result);
    }

    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() {
        // When
        Class<ITKararRequest> requestType = validator.getRelatedRequestType();

        // Then
        assertEquals(ITKararRequest.class, requestType);
    }

    @Test
    @DisplayName("Should return early when request.isValid() fails")
    void shouldReturnEarly_whenRequestIsValidFails() {
        // Given - Create request with wrong karar turu to make isValid() fail
        validRequest.setKararTuru(KararTuru.GENEL_EVRAK);

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
                reason.contains("Karar türü: " + KararTuru.ILETISIMIN_TESPITI.name() + " olmalıdır"));
    }

    @Test
    @DisplayName("Should return early when common validation fails")
    void shouldReturnEarly_whenCommonValidationFails() {
        // Given - Mock common validator to return invalid result
        when(mockCommonValidator.validate(any(ITKararRequest.class)))
                .thenReturn(new ValidationResult("Common validation failed"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Common validation failed");
    }

    @Test
    @DisplayName("Should handle validation exception gracefully")
    void shouldHandleValidationException_gracefully() {
        // Given - Mock common validator to throw exception
        when(mockCommonValidator.validate(any(ITKararRequest.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should handle null request gracefully")
    void shouldHandleNullRequest_gracefully() {
        // When
        ValidationResult result = validator.validate(null);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should pass validation with minimal valid request")
    void shouldPassValidation_withMinimalValidRequest() {
        // Given - Create minimal valid request
        ITKararRequest minimalRequest = createValidITKararRequest();
        
        // When
        ValidationResult result = validator.validate(minimalRequest);

        // Then
        assertValidationSuccess(result);
    }

    @Test
    @DisplayName("Should validate doValidate method directly")
    void shouldValidateDoValidateMethod_directly() {
        // Given
        ITKararRequest request = createValidITKararRequest();

        // When - Call doValidate directly (protected method, but we can test through validate)
        ValidationResult result = validator.validate(request);

        // Then
        assertValidationSuccess(result);
    }

    @Test
    @DisplayName("Should maintain validation state across multiple calls")
    void shouldMaintainValidationState_acrossMultipleCalls() {
        // Given
        setupValidMocks();

        // When - Call validation multiple times
        ValidationResult result1 = validator.validate(validRequest);
        ValidationResult result2 = validator.validate(validRequest);

        // Then - Both should be successful
        assertValidationSuccess(result1);
        assertValidationSuccess(result2);
    }

    @Test
    @DisplayName("Should handle exception in doValidate method")
    void shouldHandleException_inDoValidateMethod() {
        // Given - Create a custom validator that throws exception in doValidate
        ITKararValidator testValidator = new ITKararValidator(null, null) {
            @Override
            protected ValidationResult doValidate(ITKararRequest request) {
                throw new RuntimeException("Test exception in doValidate");
            }
        };
        testValidator.setMahkemeKararRequestCommonValidator(mockCommonValidator);

        // When
        ValidationResult result = testValidator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }
}
