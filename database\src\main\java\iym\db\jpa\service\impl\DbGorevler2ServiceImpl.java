package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.Gorevler2;
import iym.common.model.entity.iym.Gorevler2PK;
import iym.common.service.db.DbGorevler2Service;
import iym.db.jpa.dao.Gorevler2Repo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for Gorevler2 entity
 */
@Service
public class DbGorevler2ServiceImpl extends GenericDbServiceImpl<Gorevler2, Gorevler2PK> implements DbGorevler2Service {

    private final Gorevler2Repo gorevler2Repo;

    @Autowired
    public DbGorevler2ServiceImpl(Gorevler2Repo repository) {
        super(repository);
        this.gorevler2Repo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByGorev(String gorev) {
        return gorevler2Repo.findByGorev(gorev);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByGorevKodu(Long gorevKodu) {
        return gorevler2Repo.findByGorevKodu(gorevKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Gorevler2> findByGorevAndGorevKodu(String gorev, Long gorevKodu) {
        return gorevler2Repo.findByGorevAndGorevKodu(gorev, gorevKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByGorevImzaAdi(String gorevImzaAdi) {
        return gorevler2Repo.findByGorevImzaAdi(gorevImzaAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByImzaYetki(String imzaYetki) {
        return gorevler2Repo.findByImzaYetki(imzaYetki);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findBySilindi(Long silindi) {
        return gorevler2Repo.findBySilindi(silindi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByGorevKodu2(String gorevKodu2) {
        return gorevler2Repo.findByGorevKodu2(gorevKodu2);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByGorevTipi(String gorevTipi) {
        return gorevler2Repo.findByGorevTipi(gorevTipi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByOncelik(Long oncelik) {
        return gorevler2Repo.findByOncelik(oncelik);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByBaslamaTarihiBetween(Date startDate, Date endDate) {
        return gorevler2Repo.findByBaslamaTarihiBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByBitisTarihiBetween(Date startDate, Date endDate) {
        return gorevler2Repo.findByBitisTarihiBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByBaslamaTarihiLessThanEqualAndBitisTarihiGreaterThanEqual(Date date1, Date date2) {
        return gorevler2Repo.findByBaslamaTarihiLessThanEqualAndBitisTarihiGreaterThanEqual(date1, date2);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByGorevContainingIgnoreCase(String gorev) {
        return gorevler2Repo.findByGorevContainingIgnoreCase(gorev);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByGorevImzaAdiContainingIgnoreCase(String gorevImzaAdi) {
        return gorevler2Repo.findByGorevImzaAdiContainingIgnoreCase(gorevImzaAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByGorevTipiAndImzaYetki(String gorevTipi, String imzaYetki) {
        return gorevler2Repo.findByGorevTipiAndImzaYetki(gorevTipi, imzaYetki);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Gorevler2> findByGorevTipiAndSilindi(String gorevTipi, Long silindi) {
        return gorevler2Repo.findByGorevTipiAndSilindi(gorevTipi, silindi);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByGorevAndGorevKodu(String gorev, Long gorevKodu) {
        return gorevler2Repo.existsByGorevAndGorevKodu(gorev, gorevKodu);
    }
}
