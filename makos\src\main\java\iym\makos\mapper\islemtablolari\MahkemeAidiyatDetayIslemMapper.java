package iym.makos.mapper.islemtablolari;

import iym.common.model.entity.iym.mk.MahkemeAidiyatDetayIslem;
import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import org.springframework.stereotype.Component;

/**
 * Mapper for MahkemeKararTalep entity and DTO
 */
@Component
public class MahkemeAidiyatDetayIslemMapper {



    public MahkemeAidiyatDetayIslem fromMahkemeAidiyatDetayTalep(MahkemeAidiyatDetayTalep entity){
        if (entity == null) {
            return null;
        }

        return MahkemeAidiyatDetayIslem.builder()
                .mahkemeAidiyatKoduCikar(entity.getMahkemeAidiyatKoduCikar())
                .mahkemeAidiyatKoduEkle(entity.getMahkemeAidiyatKoduEkle())
                .iliskiliMahkemeKararId(entity.getIliskiliMahkemeKararId())
                .mahkemeKararId(entity.getMahkemeKararTalepId())
                .build();
    }




}
