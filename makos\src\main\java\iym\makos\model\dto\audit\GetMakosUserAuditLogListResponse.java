package iym.makos.model.dto.audit;

import iym.common.model.api.ApiResponseBase;
import iym.common.model.entity.makos.MakosUserAuditLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.data.domain.Page;

/**
 * Response DTO for returning a paginated list of MakosUserAuditLog entries
 */
@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class GetMakosUserAuditLogListResponse extends ApiResponseBase {
    private Page<MakosUserAuditLog> auditLogs;
} 