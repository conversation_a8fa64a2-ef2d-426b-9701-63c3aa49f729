package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.HedeflerIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for HedeflerIslem entity
 */
@Repository
public interface HedeflerIslemRepo extends JpaRepository<HedeflerIslem, Long> {

    List<HedeflerIslem> findByMahkemeKararId(Long mahkemeKararId);

}
