package iym.common.testcontainer;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Comprehensive SQL*Plus to JDBC converter
 * Handles Oracle SQL*Plus scripts and converts them to JDBC-compatible statements
 * <p>
 * Features:
 * - Filters SQL*Plus specific commands (CONNECT, SET, SPOOL, etc.)
 * - Properly handles PL/SQL block terminators (/)
 * - Parses multi-statement scripts
 * - Handles nested PL/SQL blocks
 * - Preserves string literals and comments
 * - Supports variable substitution filtering
 */
@Slf4j
public class SqlPlusToJdbcConverter {

    // SQL*Plus commands that should be filtered out
    private static final Set<String> SQLPLUS_COMMANDS = Set.of(
            "CONNECT", "CONN", "DISCONNECT", "DISC",
            "SET", "SHOW", "DESCRIBE", "DESC", "EXPLAIN",
            "SPOOL", "PROMPT", "ACCEPT", "DEFINE", "UNDEFINE",
            "COLUMN", "COL", "BREAK", "COMPUTE", "TTITLE", "BTITLE",
            "REPHEADER", "REPFOOTER", "PAGESIZE", "LINESIZE",
            "FEEDBACK", "HEADING", "VERIFY", "ECHO", "TERMOUT",
            "TIMING", "TIME", "AUTOTRACE", "ARRAYSIZE",
            "COPYCOMMIT", "COPYTYPECHECK", "SQLBLANKLINES",
            "SQLCASE", "SQLCONTINUE", "SQLNUMBER", "SQLPLUSCOMPATIBILITY",
            "SQLPREFIX", "SQLPROMPT", "SQLTERMINATOR", "SUFFIX",
            "TAB", "TRIMOUT", "TRIMSPOOL", "UNDERLINE", "WRAP",
            "WHENEVER", "EXIT", "QUIT", "@", "@@", "RUN"
    );

    // Patterns for different types of content
    private static final Pattern SINGLE_LINE_COMMENT = Pattern.compile("^\\s*--.*$");
    private static final Pattern MULTI_LINE_COMMENT_START = Pattern.compile("/\\*");
    private static final Pattern MULTI_LINE_COMMENT_END = Pattern.compile("\\*/");
    private static final Pattern SQLPLUS_COMMAND_PATTERN = Pattern.compile("^\\s*([A-Z@]+)\\s+.*$", Pattern.CASE_INSENSITIVE);
    private static final Pattern PLSQL_BLOCK_START = Pattern.compile("\\b(DECLARE|BEGIN|CREATE\\s+(OR\\s+REPLACE\\s+)?(PROCEDURE|FUNCTION|PACKAGE|TRIGGER|TYPE))\\b", Pattern.CASE_INSENSITIVE);
    private static final Pattern PLSQL_BLOCK_END = Pattern.compile("\\bEND\\s*;?\\s*$", Pattern.CASE_INSENSITIVE);
    private static final Pattern SLASH_TERMINATOR = Pattern.compile("^\\s*/\\s*$");

//    /**
//     * Converts SQL*Plus script content to JDBC-compatible statements
//     */
//    public List<String> convertToJdbcStatements(String sqlPlusContent) {
//        return convertToJdbcStatements(sqlPlusContent/*, null*/);
//    }

    /**
     * Converts SQL*Plus script content to JDBC-compatible statements with optional file saving
     */
    public List<String> convertToJdbcStatements(String sqlPlusContent/*, String originalFileName*/) {
        if (sqlPlusContent == null || sqlPlusContent.trim().isEmpty()) {
            return Collections.emptyList();
        }

        log.debug("Converting SQL*Plus content: {} characters", sqlPlusContent.length());

        // First pass: Clean and normalize the content
        String cleanedContent = preprocessContent(sqlPlusContent);
        log.debug("After preprocessing: {} characters", cleanedContent.length());

        // Second pass: Parse into statements
        List<String> statements = parseStatements(cleanedContent);
        log.debug("Parsed into {} statements", statements.size());

        // Third pass: Post-process statements
        List<String> finalStatements = postProcessStatements(statements);
        log.debug("Final statements: {}", finalStatements.size());

//        // Save cleaned SQL if filename provided
//        if (originalFileName != null) {
//            saveCleanedSql(finalStatements, originalFileName);
//        }

        return finalStatements;
    }

    /**
     * Saves cleaned SQL statements to a file
     */
    private void saveCleanedSql(List<String> statements, String originalFileName) {
        try {
            // Save to target directory
            Path outputDir = Paths.get("target/cleaned-sql");
            Files.createDirectories(outputDir);

            String cleanedFileName = originalFileName.replace(".sql", ".CLEANED.sql");
            Path outputFile = outputDir.resolve(cleanedFileName);

            StringBuilder content = new StringBuilder();
            content.append("-- Cleaned SQL: ").append(originalFileName).append("\n");
            content.append("-- SQL*Plus Commands filtered by Parser\n");
            content.append("-- Total ").append(statements.size()).append(" statements found\n\n");

            for (int i = 0; i < statements.size(); i++) {
                String statement = statements.get(i).trim();
                content.append("-- Statement #").append(i + 1).append("\n");
                content.append(statement);

                // Ensure all statements end with semicolon
                if (!statement.endsWith(";")) {
                    content.append(";");
                }
                content.append("\n\n");
            }

            Files.write(outputFile, content.toString().getBytes());
            log.info("Cleaned SQL saved: {}", outputFile);

            // ALSO save to source code for analysis (temporary)
            saveToSourceForAnalysis(content.toString(), originalFileName);

        } catch (IOException e) {
            log.warn("Cleaned SQL could not saved: {}", e.getMessage());
        }
    }

    /**
     * Saves cleaned SQL to source code for analysis (temporary)
     */
    private void saveToSourceForAnalysis(String content, String originalFileName) {
        try {
            Path sourceDir = Paths.get("src/test/resources/cleaned-sql-analysis");
            Files.createDirectories(sourceDir);

            String analysisFileName = originalFileName.replace(".sql", "_ANALYSE.sql");
            Path analysisFile = sourceDir.resolve(analysisFileName);

            String analysisContent = "/*\n" +
                    " * GECICI ANALIZ DOSYASI - SqlPlusToJdbcConverter ciktisi\n" +
                    " * Kaynak: " + originalFileName + "\n" +
                    " * Olusturulma: " + java.time.LocalDateTime.now() + "\n" +
                    " * \n" +
                    " * Bu dosya parser'in ciktisini analiz etmek icin olusturulmustur.\n" +
                    " * Gelistirme tamamlandiktan sonra silinecektir.\n" +
                    " */\n\n" +
                    content;

            Files.writeString(analysisFile, analysisContent);
            log.info("Analiz dosyası kaydedildi: {}", analysisFile);

        } catch (IOException e) {
            log.warn("Analiz dosyası kaydedilemedi: {}", e.getMessage());
        }
    }

    /**
     * Preprocesses the content to handle comments and basic cleanup
     */
    private String preprocessContent(String content) {
        StringBuilder result = new StringBuilder();
        String[] lines = content.split("\\r?\\n");
        boolean inMultiLineComment = false;

        for (String line : lines) {
            String trimmedLine = line.trim();

            // Handle multi-line comments
            if (MULTI_LINE_COMMENT_START.matcher(trimmedLine).find()) {
                inMultiLineComment = true;
            }

            if (inMultiLineComment) {
                if (MULTI_LINE_COMMENT_END.matcher(trimmedLine).find()) {
                    inMultiLineComment = false;
                }
                continue; // Skip comment lines
            }

            // Skip single-line comments
            if (SINGLE_LINE_COMMENT.matcher(trimmedLine).matches()) {
                continue;
            }

            // Skip empty lines
            if (trimmedLine.isEmpty()) {
                continue;
            }

            // Filter SQL*Plus commands
            if (isSqlPlusCommand(trimmedLine)) {
                log.debug("preprocessContent:Filtering SQL*Plus command: {}", trimmedLine);
                continue;
            }

            result.append(line).append("\n");
        }

        return result.toString();
    }

    /**
     * Checks if a line is a SQL*Plus command
     */
    private boolean isSqlPlusCommand(String line) {
        String trimmedLine = line.trim().toUpperCase();

        // Handle special cases first
        if (trimmedLine.startsWith("@") || trimmedLine.startsWith("@@")) {
            return true;
        }

        // Handle CONNECT command specifically (more comprehensive)
        if (trimmedLine.startsWith("CONNECT ") ||
                trimmedLine.startsWith("CONN ") ||
                trimmedLine.matches("CONNECT\\s+.*")) {
            log.debug("Filtering CONNECT command: {}", trimmedLine);
            return true;
        }

        // Check against known SQL*Plus commands with special handling
        for (String command : SQLPLUS_COMMANDS) {
            if (trimmedLine.startsWith(command + " ") || trimmedLine.equals(command)) {
                log.debug("isSqlPlusCommand:Filtering SQL*Plus command: {}", trimmedLine);
                return true;
            }
        }

        // Special handling for START command - only filter if it's a script execution
        if (trimmedLine.startsWith("START ") && !trimmedLine.contains("WITH")) {
            log.debug("Filtering START script command: {}", trimmedLine);
            return true;
        }

        // Pattern-based check as fallback
        Matcher matcher = SQLPLUS_COMMAND_PATTERN.matcher(trimmedLine);
        if (matcher.matches()) {
            String command = matcher.group(1).toUpperCase();
            if (SQLPLUS_COMMANDS.contains(command)) {
                log.debug("isSqlPlusCommand:Filtering SQL*Plus command via pattern: {}", trimmedLine);
                return true;
            }
        }

        return false;
    }


    /**
     * Parses the cleaned content into individual SQL or PL/SQL statements.
     * <p>
     * This method splits a given script content into executable SQL or PL/SQL statements.
     * It handles:
     * - Semicolon (;) terminated SQL statements
     * - Slash (/) terminated PL/SQL blocks
     * - Nested PL/SQL blocks with BEGIN/END keywords
     * - Multi-line statements
     * - Preserving string literals and ignoring delimiters inside them
     * <p>
     * Assumes the input is already cleaned from SQL*Plus specific commands.
     */
    private List<String> parseStatements(String content) {
        List<String> statements = new ArrayList<>();
        String[] lines = content.split("\\r?\\n");  // Split script into lines (Windows/Linux safe)

        StringBuilder currentStatement = new StringBuilder();  // Accumulates current statement
        boolean inPlSqlBlock = false;                          // Flag to track if inside a PL/SQL block
        int plSqlDepth = 0;                                    // Supports nested BEGIN...END
        boolean inStringLiteral = false;                       // Flag to track string literal state
        char stringDelimiter = 0;                              // Tracks the quote character (e.g. ' or ")

        for (String line : lines) {
            String trimmedLine = line.trim();

            if (trimmedLine.isEmpty()) {
                // Preserve empty lines within multi-line statements (for readability or formatting)
                if (!currentStatement.isEmpty()) {
                    currentStatement.append("\n");
                }
                continue;
            }

            // Handle PL/SQL block termination using a slash ("/") on a line by itself
            if (SLASH_TERMINATOR.matcher(trimmedLine).matches()) {
                if (!currentStatement.isEmpty()) {
                    String statement = currentStatement.toString().trim();
                    if (!statement.isEmpty()) {
                        statements.add(statement);
                        log.debug("Added PL/SQL block (slash terminated): {} chars", statement.length());
                    }
                    currentStatement.setLength(0);
                    inPlSqlBlock = false;
                    plSqlDepth = 0;
                }
                continue;
            }

            // Add the current line to the statement buffer
            if (!currentStatement.isEmpty()) {
                currentStatement.append("\n");
            }
            currentStatement.append(line);

            // Update the string literal state to ignore delimiters inside strings
            Object[] stringState = updateStringLiteralState(line, inStringLiteral, stringDelimiter);
            inStringLiteral = (Boolean) stringState[0];
            stringDelimiter = (Character) stringState[1];

            // Only analyze PL/SQL or SQL keywords if we are not inside a string literal
            if (!inStringLiteral) {
                // Detect start of PL/SQL block (e.g. BEGIN, DECLARE)
                if (PLSQL_BLOCK_START.matcher(trimmedLine).find()) {
                    if (!inPlSqlBlock) {
                        inPlSqlBlock = true;
                        plSqlDepth = 1;
                        log.debug("PL/SQL block started, depth: {}", plSqlDepth);
                    } else {
                        plSqlDepth++;
                        log.debug("Nested PL/SQL block, depth: {}", plSqlDepth);
                    }
                }

                // Detect end of PL/SQL block (e.g. END;)
                if (inPlSqlBlock && PLSQL_BLOCK_END.matcher(trimmedLine).find()) {
                    plSqlDepth--;
                    log.debug("PL/SQL block end, depth: {}", plSqlDepth);

                    if (plSqlDepth <= 0) {
                        inPlSqlBlock = false;
                        plSqlDepth = 0;
                        // Do not terminate yet; wait for "/" to complete the block
                    }
                }

                // Detect and process standard SQL statement termination using semicolon
                if (!inPlSqlBlock && trimmedLine.endsWith(";")) {
                    String statement = currentStatement.toString().trim();
                    if (!statement.isEmpty()) {
                        statements.add(statement);
                        log.debug("Added SQL statement: {} chars", statement.length());
                    }
                    currentStatement.setLength(0);  // Reset for next statement
                }
            }
        }

        // After all lines are processed, check for any remaining partial statement
        String remaining = currentStatement.toString().trim();
        if (!remaining.isEmpty()) {
            statements.add(remaining);
            log.debug("Added remaining statement: {} chars", remaining.length());
        }

        return statements;
    }


    /**
     * Updates string literal tracking state for a line
     * Returns an array where [0] is the boolean inStringLiteral state and [1] is the char stringDelimiter
     */
    private Object[] updateStringLiteralState(String line, boolean inStringLiteral, char stringDelimiter) {
        boolean currentlyInString = inStringLiteral;
        char currentDelimiter = stringDelimiter;

        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);

            if (!currentlyInString && (c == '\'' || c == '"')) {
                currentlyInString = true;
                currentDelimiter = c;
            } else if (currentlyInString && c == currentDelimiter) {
                // Check for escaped quote (doubled quote)
                if (i + 1 < line.length() && line.charAt(i + 1) == currentDelimiter) {
                    i++; // Skip escaped quote
                } else {
                    currentlyInString = false;
                    currentDelimiter = 0;
                }
            }
        }

        return new Object[]{currentlyInString, currentDelimiter};
    }


    /**
     * Post-processes statements for final cleanup
     */
    private List<String> postProcessStatements(List<String> statements) {
        List<String> result = new ArrayList<>();

        for (String statement : statements) {
            String processed = postProcessStatement(statement);
            if (processed != null && !processed.trim().isEmpty()) {
                result.add(processed);
            }
        }

        return result;
    }

    /**
     * Post-processes a single statement
     */
    private String postProcessStatement(String statement) {
        if (statement == null || statement.trim().isEmpty()) {
            return null;
        }

        String trimmed = statement.trim();

        // Remove trailing semicolon if present (JDBC doesn't need it for single command but The final semicolon after END is REQUIRED.)
        if (trimmed.endsWith(";") && !trimmed.toUpperCase().endsWith("END;")) {
            trimmed = trimmed.substring(0, trimmed.length() - 1).trim();
        }

        // Skip statements that are just comments or whitespace
        if (trimmed.isEmpty() || trimmed.startsWith("--")) {
            return null;
        }

        return trimmed;
    }

    /**
     * Main method for testing the parser
     */
    public static void main(String[] args) {
        SqlPlusToJdbcConverter converter = new SqlPlusToJdbcConverter();

        String testSql = """
            BEGIN
              DBMS_OUTPUT.PUT_LINE('=== STARTING: 01.00_create_schema.sql ===');
              DBMS_OUTPUT.PUT_LINE('Creating tablespace, user, and granting privileges...');
            END;
            /
            CREATE SEQUENCE iym.EVRAK_KAYIT_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            CREATE SEQUENCE iym.KULLANICILAR_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            CREATE SEQUENCE iym.KULLANICI_GOREV2_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            CREATE SEQUENCE iym.EVRAK_GELEN_KURUMLAR_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            CREATE SEQUENCE iym.KULLANICI_KURUM_SEQ
                START WITH 1
                INCREMENT BY 1
                NOCACHE
                NOCYCLE;
            BEGIN
              DBMS_OUTPUT.PUT_LINE('=== COMPLETED: 01.00_create_schema.sql ===');
              DBMS_OUTPUT.PUT_LINE('Schema, user, and sequences created successfully');
            END;
            /
            """;

        List<String> statements = converter.convertToJdbcStatements(testSql);

        System.out.println("Total statements parsed: " + statements.size());
        for (int i = 0; i < statements.size(); i++) {
            System.out.println("Statement " + (i + 1) + ":");
            System.out.println(statements.get(i));
            System.out.println("---");
        }
    }
}
