package iym.makos.validator;

import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.testdata.TestDataBuilder;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.MahkemeKararGuncellemeAlan;
import iym.makos.model.api.MahkemeKararGuncellemeBilgi;
import iym.makos.model.api.MahkemeKararGuncellemeDetay;
import iym.makos.model.dto.mahkemekarar.id.IDMahkemeKararGuncellemeRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDMahkemeKararGuncellemeValidator.
 *
 * Tests validation logic for ID mahkeme karar güncelleme (court decision update) requests.
 * Uses LENIENT mock settings for flexibility.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
class IDMahkemeKararGuncellemeValidatorTest extends BaseDomainUnitTest {

    @Mock
    private DbMahkemeKararService mockDbMahkemeKararService;

    @Mock
    private DbMahkemeBilgiService mockDbMahkemeBilgiService;

    @Mock
    private MahkemeKararRequestCommonValidator mockCommonValidator;

    @InjectMocks
    private IDMahkemeKararGuncellemeValidator validator;

    private IDMahkemeKararGuncellemeRequest validRequest;
    private MahkemeKararDetay validMahkemeKararDetay;
    private MahkemeKarar existingMahkemeKarar;
    private MahkemeBilgi existingMahkemeBilgi;

    @BeforeEach
    void setUp() {
        // Create valid test data
        validRequest = TestDataBuilder.createValidIDMahkemeKararGuncellemeRequest();
        validMahkemeKararDetay = TestDataBuilder.createValidMahkemeKararDetay();
        
        // Create existing entities
        existingMahkemeKarar = new MahkemeKarar();
        existingMahkemeKarar.setMahkemeKodu("001");
        existingMahkemeKarar.setSorusturmaNo("2023/456");
        existingMahkemeKarar.setMahkemeKararNo("2023/123");
        
        existingMahkemeBilgi = new MahkemeBilgi();
        existingMahkemeBilgi.setMahkemeKodu("002");
        
        // Setup default mocks
        setupValidMocks();
    }

    private void setupValidMocks() {
        // Mock common validator to return valid result for any request
        when(mockCommonValidator.validate(any(IDMahkemeKararGuncellemeRequest.class)))
                .thenReturn(new ValidationResult(true));

        // Mock mahkeme karar service to return existing karar
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(existingMahkemeKarar));

        // Mock mahkeme bilgi service to return existing bilgi
        when(mockDbMahkemeBilgiService.findByMahkemeKodu(anyString()))
                .thenReturn(Optional.of(existingMahkemeBilgi));

        // Set the common validator mock
        validator.setMahkemeKararRequestCommonValidator(mockCommonValidator);
    }

    @Test
    @DisplayName("Should pass validation when request is valid")
    void shouldPassValidation_whenRequestIsValid() throws Exception {
        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertTrue(result.isValid());
        assertThat(result.getReasons()).isEmpty();
    }

    @Test
    @DisplayName("Should fail validation when mahkeme karar detay is null")
    void shouldFailValidation_whenMahkemeKararDetayIsNull() throws Exception {
        // Given
        MahkemeKararGuncellemeDetay nullDetay = MahkemeKararGuncellemeDetay.builder()
                .mahkemeKararDetay(null)
                .mahkemeKararGuncellemeBilgiListesi(Arrays.asList(
                        TestDataBuilder.createMahkemeKararGuncellemeBilgi(
                                MahkemeKararGuncellemeAlan.MAHKEME_KODU, "002")
                ))
                .build();
        
        IDMahkemeKararGuncellemeRequest invalidRequest = createTestRequest(Arrays.asList(nullDetay));

        // When
        ValidationResult result = validator.validate(invalidRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("Güncellemeye konu mahkeme karar bilgileri boş olamaz.!");
    }

    @Test
    @DisplayName("Should fail validation when related mahkeme karar not found")
    void shouldFailValidation_whenRelatedMahkemeKararNotFound() throws Exception {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.empty());

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("Mahkeme Karar Bulunamadı: Mahkeme İl/İlçe Kodu: 0600 Mahkeme Kodu: 001, Karar No: 2024/001, Soruşturma No :2024/SOR/001");
    }

    @Test
    @DisplayName("Should fail validation when new mahkeme kodu is same as existing")
    void shouldFailValidation_whenNewMahkemeKoduIsSameAsExisting() throws Exception {
        // Given - Create request with same mahkeme kodu
        MahkemeKararGuncellemeDetay sameKodDetay = MahkemeKararGuncellemeDetay.builder()
                .mahkemeKararDetay(validMahkemeKararDetay)
                .mahkemeKararGuncellemeBilgiListesi(Arrays.asList(
                        TestDataBuilder.createMahkemeKararGuncellemeBilgi(
                                MahkemeKararGuncellemeAlan.MAHKEME_KODU, "001") // Same as existing
                ))
                .build();
        
        IDMahkemeKararGuncellemeRequest invalidRequest = createTestRequest(Arrays.asList(sameKodDetay));

        // When
        ValidationResult result = validator.validate(invalidRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("Mahkeme kodu eski mahkeme kod ile aynıdır.");
    }

    @Test
    @DisplayName("Should fail validation when new mahkeme bilgi not found")
    void shouldFailValidation_whenNewMahkemeBilgiNotFound() throws Exception {
        // Given
        when(mockDbMahkemeBilgiService.findByMahkemeKodu("999"))
                .thenReturn(Optional.empty());
        
        MahkemeKararGuncellemeDetay invalidDetay = MahkemeKararGuncellemeDetay.builder()
                .mahkemeKararDetay(validMahkemeKararDetay)
                .mahkemeKararGuncellemeBilgiListesi(Arrays.asList(
                        TestDataBuilder.createMahkemeKararGuncellemeBilgi(
                                MahkemeKararGuncellemeAlan.MAHKEME_KODU, "999")
                ))
                .build();
        
        IDMahkemeKararGuncellemeRequest invalidRequest = createTestRequest(Arrays.asList(invalidDetay));

        // When
        ValidationResult result = validator.validate(invalidRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("999 numaralı mahkeme bilgisi bulunamadı.");
    }

    @Test
    @DisplayName("Should pass validation for SORUSTURMA_NO update")
    void shouldPassValidation_forSorusturmaNoUpdate() throws Exception {
        // Given
        MahkemeKararGuncellemeDetay sorusturmaDetay = MahkemeKararGuncellemeDetay.builder()
                .mahkemeKararDetay(validMahkemeKararDetay)
                .mahkemeKararGuncellemeBilgiListesi(Arrays.asList(
                        TestDataBuilder.createMahkemeKararGuncellemeBilgi(
                                MahkemeKararGuncellemeAlan.SORUSTURMA_NO, "2024/789")
                ))
                .build();
        
        IDMahkemeKararGuncellemeRequest request = createTestRequest(Arrays.asList(sorusturmaDetay));

        // When
        ValidationResult result = validator.validate(request);

        // Then
        assertTrue(result.isValid());
        assertThat(result.getReasons()).isEmpty();
    }

    @Test
    @DisplayName("Should pass validation for MAHKEMEKARAR_NO update")
    void shouldPassValidation_forMahkemeKararNoUpdate() throws Exception {
        // Given
        MahkemeKararGuncellemeDetay kararNoDetay = MahkemeKararGuncellemeDetay.builder()
                .mahkemeKararDetay(validMahkemeKararDetay)
                .mahkemeKararGuncellemeBilgiListesi(Arrays.asList(
                        TestDataBuilder.createMahkemeKararGuncellemeBilgi(
                                MahkemeKararGuncellemeAlan.MAHKEMEKARAR_NO, "2024/456")
                ))
                .build();
        
        IDMahkemeKararGuncellemeRequest request = createTestRequest(Arrays.asList(kararNoDetay));

        // When
        ValidationResult result = validator.validate(request);

        // Then
        assertTrue(result.isValid());
        assertThat(result.getReasons()).isEmpty();
    }

    @Test
    @DisplayName("Should handle validation exception gracefully")
    void shouldHandleValidationException_gracefully() throws Exception {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertFalse(result.isValid());
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() throws Exception {
        // When
        KararTuru result = validator.getRelatedKararTuru();

        // Then
        assertEquals(KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME, result);
    }

    private IDMahkemeKararGuncellemeRequest createTestRequest(
            java.util.List<MahkemeKararGuncellemeDetay> guncellemeDetayListesi) {
        return IDMahkemeKararGuncellemeRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME)
                .evrakDetay(TestDataBuilder.createValidEvrakDetay())
                .mahkemeKararBilgisi(iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(iym.common.enums.MahkemeKararTip.MAHKEME_KARAR_BILGI_DEGISTIRME)
                        .mahkemeKararDetay(TestDataBuilder.createValidMahkemeKararDetay())
                        .build())
                .mahkemeKararGuncellemeDetayListesi(guncellemeDetayListesi)
                .build();
    }
}
