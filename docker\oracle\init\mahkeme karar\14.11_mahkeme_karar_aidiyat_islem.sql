-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_AIDIYAT_ISLEM_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON><PERSON><PERSON><PERSON>_AIDIYAT_ISLEM_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_AIDIYAT_ISLEM_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

--TODO : byte kalkacak, pk eklenecek
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_AIDIYAT_ISLEM';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_AIDIYAT_ISLEM (
      ID NUMBER NOT NULL
    , <PERSON>HKEME_ID NUMBER NOT NULL
    , AIDIYAT_KOD VARCHAR2(25) NOT NULL
    , CONSTRAINT MAHKEME_AIDIYAT_ISLEM_PK PRIMARY KEY (ID) ENABLE
    )';

  END IF;
END;
/



COMMIT;
