package iym.makos.validator;

import iym.common.enums.EvrakKurum;
import iym.common.enums.HedefTip;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.enums.SureTip;
import iym.common.model.api.Hedef;
import iym.common.model.api.HedefWithAdSoyad;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.SucTipi;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbSucTipiService;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.testdata.TestDataBuilder;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mahkemekarar.id.IDSonlandirmaKarariRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDSonlandirmaKarariValidator.
 * 
 * Tests the validation logic for ID sonlandırma kararı requests.
 * Verifies business rules, edge cases, and error handling.
 * 
 * <AUTHOR> Team
 */
@DisplayName("IDSonlandirmaKarariValidator Unit Tests")
@MockitoSettings(strictness = Strictness.LENIENT)
class IDSonlandirmaKarariValidatorTest extends BaseDomainUnitTest {
    
    @Mock
    private DbMahkemeKararService mockDbMahkemeKararService;
    
    @Mock
    private DbHedeflerService mockDbHedeflerService;
    
    @Mock
    private DbSucTipiService mockDbSucTipiService;
    
    @Mock
    private MahkemeKararRequestCommonValidator mockCommonValidator;
    
    @InjectMocks
    private IDSonlandirmaKarariValidator validator;
    
    private IDSonlandirmaKarariRequest validRequest;
    private IDHedefDetay validHedefDetay;
    private MahkemeKararDetay validMahkemeKararDetay;
    
    @BeforeEach
    void setUp() throws Exception {
        // Setup valid test data
        validMahkemeKararDetay = MahkemeKararDetay.builder()
                .mahkemeIlIlceKodu("34001")
                .mahkemeKodu("001")
                .mahkemeKararNo("2023/123")
                .sorusturmaNo("2023/456")
                .build();
        
        // Create HedefWithAdSoyad
        Hedef hedef = Hedef.builder()
                .hedefNo("H001")
                .hedefTip(HedefTip.GSM)
                .build();
        
        HedefWithAdSoyad hedefWithAdSoyad = HedefWithAdSoyad.builder()
                .hedef(hedef)
                .hedefAd("Test")
                .hedefSoyad("User")
                .tcKimlikNo("12345678901")
                .build();
        
        validHedefDetay = IDHedefDetay.builder()
                .hedefNoAdSoyad(hedefWithAdSoyad)
                .baslamaTarihi(LocalDateTime.now())
                .sureTip(SureTip.AY)
                .sure(3)
                .uzatmaSayisi(null) // Sonlandırma'da null olmalı
                .ilgiliMahkemeKararDetayi(validMahkemeKararDetay)
                .build();
        
        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(TestDataBuilder.createValidEvrakDetay())
                .mahkemeKararBilgisi(TestDataBuilder.createValidMahkemeKararBilgisi())
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("MT12345")) // MIT için uygun format: 7 karakter, MT ile başlayan
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        
        // Setup common validator mock
        when(mockCommonValidator.validate(any())).thenReturn(new ValidationResult(true));
    }
    
    @Test
    @DisplayName("Should validate successfully when all conditions are met")
    void shouldValidateSuccessfully_whenAllConditionsAreMet() throws Exception {
        // Given
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isTrue();
        assertThat(result.getReasons()).isEmpty();
    }
    
    @Test
    @DisplayName("Should fail validation when OHAL date restriction violated")
    void shouldFailValidation_whenOHALDateRestrictionViolated() throws Exception {
        // Given
        // Create new MahkemeKararBilgisi with ADLI_KHK_YAZILI_EMIR type
        iym.makos.model.api.MahkemeKararBilgisi newMahkemeKararBilgisi = 
                iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR)
                        .mahkemeKararDetay(validRequest.getMahkemeKararBilgisi().getMahkemeKararDetay())
                        .build();
        
        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(TestDataBuilder.createValidEvrakDetay())
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("MT12345"))
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .mahkemeKararBilgisi(newMahkemeKararBilgisi)
                .build();
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
    }
    
    @Test
    @DisplayName("Should fail validation when CANAK number is provided")
    void shouldFailValidation_whenCanakNumberProvided() throws Exception {
        // Given
        // Create new IDHedefDetay with CANAK number
        IDHedefDetay newHedefDetay = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(null) // Sonlandırma'da null olmalı
                .ilgiliMahkemeKararDetayi(validHedefDetay.getIlgiliMahkemeKararDetayi())
                .canakNo("CANAK123")
                .build();

        validRequest = createTestRequest(Arrays.asList(newHedefDetay),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Sonlandirma kararında CANAK numarası girilemez.");
    }
    
    @Test
    @DisplayName("Should fail validation when uzatma sayisi is not null")
    void shouldFailValidation_whenUzatmaSayisiIsNotNull() throws Exception {
        // Given
        // Create new IDHedefDetay with non-null uzatmaSayisi
        IDHedefDetay newHedefDetay = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(1) // Sonlandırma'da null olmalı
                .ilgiliMahkemeKararDetayi(validHedefDetay.getIlgiliMahkemeKararDetayi())
                .build();

        validRequest = createTestRequest(Arrays.asList(newHedefDetay),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Sonlandirma Kararinda uzatma sayisi dolu olamaz!");
    }
    
    @Test
    @DisplayName("Should fail validation when ilgili mahkeme karar is null")
    void shouldFailValidation_whenIlgiliMahkemeKararIsNull() throws Exception {
        // Given
        // Create new IDHedefDetay with null ilgiliMahkemeKararDetayi
        IDHedefDetay newHedefDetay = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(null)
                .ilgiliMahkemeKararDetayi(null)
                .build();

        validRequest = createTestRequest(Arrays.asList(newHedefDetay),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Sonlandirma Kararinda ilgili mahkeme karari bos olamaz!");
    }
    
    @Test
    @DisplayName("Should fail validation when related mahkeme karar not found in database")
    void shouldFailValidation_whenRelatedMahkemeKararNotFound() throws Exception {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.empty());
        setupOtherValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .anyMatch(reason -> reason.contains("Mahkeme Karar Bulunamadı"));
    }
    
    @Test
    @DisplayName("Should fail validation when hedef not found in database")
    void shouldFailValidation_whenHedefNotFound() throws Exception {
        // Given
        MahkemeKarar mockMahkemeKarar = new MahkemeKarar();
        mockMahkemeKarar.setId(1L);
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKarar));
        when(mockDbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(any(), anyString(), any()))
                .thenReturn(Optional.empty());
        setupOtherValidMocks();
        
        // When
        ValidationResult result = validator.validate(validRequest);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .anyMatch(reason -> reason.contains("H001 numaralı hedef  ilişkli mahkeme kararda bulunamadı"));
    }
    
    @Test
    @DisplayName("Should return correct related karar turu")
    void shouldReturnCorrectRelatedKararTuru() {
        // When
        KararTuru result = validator.getRelatedKararTuru();

        // Then
        assertThat(result).isEqualTo(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI);
    }

    @Test
    @DisplayName("Should handle null ilgili mahkeme karar detay gracefully")
    void shouldHandleNullIlgiliMahkemeKararDetay_gracefully() throws Exception {
        // Given - Create hedef detay with null ilgiliMahkemeKararDetayi
        IDHedefDetay hedefDetayWithNullKarar = IDHedefDetay.builder()
                .hedefNoAdSoyad(validHedefDetay.getHedefNoAdSoyad())
                .baslamaTarihi(validHedefDetay.getBaslamaTarihi())
                .sureTip(validHedefDetay.getSureTip())
                .sure(validHedefDetay.getSure())
                .uzatmaSayisi(null)
                .ilgiliMahkemeKararDetayi(null)
                .build();

        validRequest = createTestRequest(Arrays.asList(hedefDetayWithNullKarar),
                Arrays.asList("MT12345"), Arrays.asList("SUC001"));
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then - Should pass the null check branch but fail on request.isValid()
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Sonlandirma Kararinda ilgili mahkeme karari bos olamaz!");
    }

    @Test
    @DisplayName("Should validate JANDARMA aidiyat codes for ADLI karar types")
    void shouldValidateJandarmaAidiyatCodes_forAdliKararTypes() throws Exception {
        // Given - Setup JANDARMA kurum and ADLI_HAKIM_KARARI type
        iym.makos.model.api.EvrakDetay jandarmaEvrakDetay =
                iym.makos.model.api.EvrakDetay.builder()
                        .evrakKurumKodu("03") // JANDARMA
                        .geldigiIlIlceKodu("34001")
                        .evrakNo("EVR001")
                        .evrakTarihi(LocalDateTime.now())
                        .build();

        iym.makos.model.api.MahkemeKararBilgisi jandarmaKararBilgisi =
                iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ADLI_HAKIM_KARARI)
                        .mahkemeKararDetay(validRequest.getMahkemeKararBilgisi().getMahkemeKararDetay())
                        .build();

        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(jandarmaEvrakDetay)
                .mahkemeKararBilgisi(jandarmaKararBilgisi)
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("JA12345")) // Valid JANDARMA aidiyat
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isTrue();
    }

    @Test
    @DisplayName("Should fail validation for invalid JANDARMA aidiyat codes for ADLI karar types")
    void shouldFailValidation_forInvalidJandarmaAidiyatCodesForAdliKararTypes() throws Exception {
        // Given - Setup JANDARMA kurum and ADLI_YAZILI_EMIR type with invalid aidiyat
        iym.makos.model.api.EvrakDetay jandarmaEvrakDetay =
                iym.makos.model.api.EvrakDetay.builder()
                        .evrakKurumKodu("03") // JANDARMA
                        .geldigiIlIlceKodu("34001")
                        .evrakNo("EVR001")
                        .evrakTarihi(LocalDateTime.now())
                        .build();

        iym.makos.model.api.MahkemeKararBilgisi jandarmaKararBilgisi =
                iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ADLI_YAZILI_EMIR)
                        .mahkemeKararDetay(validRequest.getMahkemeKararBilgisi().getMahkemeKararDetay())
                        .build();

        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(jandarmaEvrakDetay)
                .mahkemeKararBilgisi(jandarmaKararBilgisi)
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("XX12345")) // Invalid aidiyat - should start with JA
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .anyMatch(reason -> reason.contains("XX12345 kurumunuz Adli Karar Aidiyatları JA ile baslamalidir"));
    }

    @Test
    @DisplayName("Should validate JANDARMA aidiyat codes for ONLEYICI karar types")
    void shouldValidateJandarmaAidiyatCodes_forOnleyiciKararTypes() throws Exception {
        // Given - Setup JANDARMA kurum and ONLEYICI_HAKIM_KARARI type
        iym.makos.model.api.EvrakDetay jandarmaEvrakDetay =
                iym.makos.model.api.EvrakDetay.builder()
                        .evrakKurumKodu("03") // JANDARMA
                        .geldigiIlIlceKodu("34001")
                        .evrakNo("EVR001")
                        .evrakTarihi(LocalDateTime.now())
                        .build();

        iym.makos.model.api.MahkemeKararBilgisi jandarmaKararBilgisi =
                iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ONLEYICI_HAKIM_KARARI)
                        .mahkemeKararDetay(validRequest.getMahkemeKararBilgisi().getMahkemeKararDetay())
                        .build();

        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(jandarmaEvrakDetay)
                .mahkemeKararBilgisi(jandarmaKararBilgisi)
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("Jİ12345", "JG12345", "JK12345", "JT12345", "JB12345")) // Valid JANDARMA istihbari aidiyat codes
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isTrue();
    }

    @Test
    @DisplayName("Should fail validation for invalid JANDARMA aidiyat codes for ONLEYICI karar types")
    void shouldFailValidation_forInvalidJandarmaAidiyatCodesForOnleyiciKararTypes() throws Exception {
        // Given - Setup JANDARMA kurum and ONLEYICI_YAZILI_EMIR type with invalid aidiyat
        iym.makos.model.api.EvrakDetay jandarmaEvrakDetay =
                iym.makos.model.api.EvrakDetay.builder()
                        .evrakKurumKodu("03") // JANDARMA
                        .geldigiIlIlceKodu("34001")
                        .evrakNo("EVR001")
                        .evrakTarihi(LocalDateTime.now())
                        .build();

        iym.makos.model.api.MahkemeKararBilgisi jandarmaKararBilgisi =
                iym.makos.model.api.MahkemeKararBilgisi.builder()
                        .mahkemeKararTipi(MahkemeKararTip.ONLEYICI_YAZILI_EMIR)
                        .mahkemeKararDetay(validRequest.getMahkemeKararBilgisi().getMahkemeKararDetay())
                        .build();

        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(jandarmaEvrakDetay)
                .mahkemeKararBilgisi(jandarmaKararBilgisi)
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("XX12345")) // Invalid aidiyat - should start with Jİ, JG, JK, JT, or JB
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .anyMatch(reason -> reason.contains("XX12345 kurumunuz İstihbari Karar Aidiyatları Jİ ile baslamalidir"));
    }

    @Test
    @DisplayName("Should validate MIT aidiyat codes with 7 character MT format")
    void shouldValidateMitAidiyatCodes_with7CharacterMtFormat() throws Exception {
        // Given - Setup MIT kurum
        iym.makos.model.api.EvrakDetay mitEvrakDetay =
                iym.makos.model.api.EvrakDetay.builder()
                        .evrakKurumKodu("01") // MIT
                        .geldigiIlIlceKodu("34001")
                        .evrakNo("EVR001")
                        .evrakTarihi(LocalDateTime.now())
                        .build();

        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(mitEvrakDetay)
                .mahkemeKararBilgisi(TestDataBuilder.createValidMahkemeKararBilgisi())
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("MT12345")) // Valid MIT aidiyat - 7 characters, starts with MT
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isTrue();
    }

    @Test
    @DisplayName("Should validate MIT aidiyat codes with 4 character MİT format")
    void shouldValidateMitAidiyatCodes_with4CharacterMitFormat() throws Exception {
        // Given - Setup MIT kurum
        iym.makos.model.api.EvrakDetay mitEvrakDetay =
                iym.makos.model.api.EvrakDetay.builder()
                        .evrakKurumKodu("01") // MIT
                        .geldigiIlIlceKodu("34001")
                        .evrakNo("EVR001")
                        .evrakTarihi(LocalDateTime.now())
                        .build();

        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(mitEvrakDetay)
                .mahkemeKararBilgisi(TestDataBuilder.createValidMahkemeKararBilgisi())
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("MİT1")) // Valid MIT aidiyat - 4 characters, starts with MİT
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isTrue();
    }

    @Test
    @DisplayName("Should fail validation for invalid MIT aidiyat codes")
    void shouldFailValidation_forInvalidMitAidiyatCodes() throws Exception {
        // Given - Setup MIT kurum with invalid aidiyat
        iym.makos.model.api.EvrakDetay mitEvrakDetay =
                iym.makos.model.api.EvrakDetay.builder()
                        .evrakKurumKodu("01") // MIT
                        .geldigiIlIlceKodu("34001")
                        .evrakNo("EVR001")
                        .evrakTarihi(LocalDateTime.now())
                        .build();

        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(mitEvrakDetay)
                .mahkemeKararBilgisi(TestDataBuilder.createValidMahkemeKararBilgisi())
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("XX12345")) // Invalid MIT aidiyat
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .anyMatch(reason -> reason.contains("Kurumunuz Karar Aidiyatları MT veya MİT ile baslamalidir"));
    }

    @Test
    @DisplayName("Should validate EGMIDB aidiyat codes with 8 character Y format")
    void shouldValidateEgmidbAidiyatCodes_with8CharacterYFormat() throws Exception {
        // Given - Setup EGMIDB kurum
        iym.makos.model.api.EvrakDetay egmidbEvrakDetay =
                iym.makos.model.api.EvrakDetay.builder()
                        .evrakKurumKodu("02") // EGMIDB
                        .geldigiIlIlceKodu("34001")
                        .evrakNo("EVR001")
                        .evrakTarihi(LocalDateTime.now())
                        .build();

        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(egmidbEvrakDetay)
                .mahkemeKararBilgisi(TestDataBuilder.createValidMahkemeKararBilgisi())
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("********")) // Valid EGMIDB aidiyat - 8 characters, starts with Y
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isTrue();
    }

    @Test
    @DisplayName("Should fail validation for invalid EGMIDB aidiyat codes and break loop")
    void shouldFailValidation_forInvalidEgmidbAidiyatCodesAndBreakLoop() throws Exception {
        // Given - Setup EGMIDB kurum with invalid aidiyat (this should trigger the break statement)
        iym.makos.model.api.EvrakDetay egmidbEvrakDetay =
                iym.makos.model.api.EvrakDetay.builder()
                        .evrakKurumKodu("02") // EGMIDB
                        .geldigiIlIlceKodu("34001")
                        .evrakNo("EVR001")
                        .evrakTarihi(LocalDateTime.now())
                        .build();

        validRequest = IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(egmidbEvrakDetay)
                .mahkemeKararBilgisi(TestDataBuilder.createValidMahkemeKararBilgisi())
                .hedefDetayListesi(Arrays.asList(validHedefDetay))
                .mahkemeAidiyatKodlari(Arrays.asList("XX123456", "********")) // First invalid, second valid - but break should stop processing
                .mahkemeSucTipiKodlari(Arrays.asList("SUC001"))
                .build();
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .anyMatch(reason -> reason.contains("Kurumunuz Karar Aidiyatları Y ile baslamali ve 8 karakter olmalıdır"));
    }

    @Test
    @DisplayName("Should handle exception in doValidate method")
    void shouldHandleException_inDoValidateMethod() throws Exception {
        // Given - Mock to throw exception
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Database error"));
        setupOtherValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons())
                .contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should validate with empty aidiyat and suc tipi lists")
    void shouldValidate_withEmptyAidiyatAndSucTipiLists() throws Exception {
        // Given - Request with empty lists
        validRequest = createTestRequest(Arrays.asList(validHedefDetay),
                Collections.emptyList(), Collections.emptyList());
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isTrue();
    }

    @Test
    @DisplayName("Should validate with null aidiyat and suc tipi lists")
    void shouldValidate_withNullAidiyatAndSucTipiLists() throws Exception {
        // Given - Request with null lists
        validRequest = createTestRequest(Arrays.asList(validHedefDetay), null, null);
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertThat(result.isValid()).isTrue();
    }
    
    private void setupValidMocks() {
        MahkemeKarar mockMahkemeKarar = new MahkemeKarar();
        mockMahkemeKarar.setId(1L);
        
        Hedefler mockHedef = new Hedefler();
        mockHedef.setId(1L);
        
        SucTipi mockSucTipi = new SucTipi();
        mockSucTipi.setSucTipiKodu("SUC001");
        
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKarar));
        when(mockDbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(any(), anyString(), any()))
                .thenReturn(Optional.of(mockHedef));
        when(mockDbSucTipiService.findBySucTipiKodu("SUC001"))
                .thenReturn(Optional.of(mockSucTipi));
    }
    
    private void setupOtherValidMocks() {
        SucTipi mockSucTipi = new SucTipi();
        mockSucTipi.setSucTipiKodu("SUC001");
        
        when(mockDbSucTipiService.findBySucTipiKodu("SUC001"))
                .thenReturn(Optional.of(mockSucTipi));
    }

    private IDSonlandirmaKarariRequest createTestRequest(List<IDHedefDetay> hedefDetayListesi,
                                                        List<String> aidiyatKodlari,
                                                        List<String> sucTipiKodlari) {
        return IDSonlandirmaKarariRequest.builder()
                .id(java.util.UUID.randomUUID())
                .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI)
                .evrakDetay(TestDataBuilder.createValidEvrakDetay())
                .mahkemeKararBilgisi(TestDataBuilder.createValidMahkemeKararBilgisi())
                .hedefDetayListesi(hedefDetayListesi)
                .mahkemeAidiyatKodlari(aidiyatKodlari)
                .mahkemeSucTipiKodlari(sucTipiKodlari)
                .build();
    }
}
