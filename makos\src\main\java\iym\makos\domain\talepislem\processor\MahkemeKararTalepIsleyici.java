package iym.makos.domain.talepislem.processor;

import iym.common.enums.KararTuru;
import iym.common.enums.TalepGuncellemeTuru;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;


public interface MahkemeKararTalepIsleyici {

    String DURUM_ONAYLA = "ONAYLANDI";
    String DURUM_SILINDI = "SILINDI";
    String DURUM_ARSIV = "ARSIV";

    MahkemeKararTalepUpdateResponse process(MahkemeKararTalepUpdateRequest request, Long kullaniciId);

    KararTuru getRelatedKararTuru();

    static String toDurum(TalepGuncellemeTuru talepGuncellemeTuru){
        return switch (talepGuncellemeTuru) {
            case ONAYLA -> DURUM_ONAYLA;
            case SIL -> DURUM_SILINDI;
            case ARSIV -> DURUM_ARSIV;
        };
    }
}

