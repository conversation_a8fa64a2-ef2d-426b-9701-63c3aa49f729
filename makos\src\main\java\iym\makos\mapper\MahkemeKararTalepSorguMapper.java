package iym.makos.mapper;

import iym.common.model.entity.iym.sorgu.MahkemeKararTalepSorguInfo;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mktalep.MahkemeKararTalepIslenecekView;
import iym.makos.model.dto.mahkemekarar.id.sorgulama.view.mktalep.MahkemeKararTalepSorguView;
import org.springframework.stereotype.Component;

/**
 * Mapper for MahkemeKararTalep entity and DTO
 */
@Component
public class MahkemeKararTalepSorguMapper {

    public MahkemeKararTalepIslenecekView toKararTalepViewInfo(MahkemeKararTalepSorguInfo entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeKararTalepIslenecekView.builder()
                .mahkemeKararTalepId(entity.getId())
                .evrakId(entity.getEvrakId())
                .kaydedenKullaniciId(entity.getKaydedenKullaniciId())
                .kararKayitTarihi(entity.getKayitTarihi())
                .durumu(entity.getDurum())
                .mahkemeKararNo(entity.getMahkemeKararNo())
                .sorusturmaNo(entity.getSorusturmaNo())
                //.mahkemeIlIlceKodu(entity.geti)
                .aciklama(entity.getAciklama())
                .build();
    }

    public MahkemeKararTalepSorguView toMahkemeKararTalepSorguView(MahkemeKararTalepSorguInfo entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeKararTalepSorguView.builder()
                .mahkemeKararTalepId(entity.getId())
                .evrakId(entity.getEvrakId())
                .kaydedenKullaniciId(entity.getKaydedenKullaniciId())
                .kararKayitTarihi(entity.getKayitTarihi())
                .durumu(entity.getDurum())
                .mahkemeKararNo(entity.getMahkemeKararNo())
                .sorusturmaNo(entity.getSorusturmaNo())
                //.mahkemeIlIlceKodu(entity.geti)
                .aciklama(entity.getAciklama())
                .build();
    }


}
