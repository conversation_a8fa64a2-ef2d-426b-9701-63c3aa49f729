package iym.makos.model.dto.ws.internal.id;

import iym.common.enums.IDKararTuru;
import iym.common.validation.ValidationResult;
import iym.makos.model.BaseMakosRequest;
import iym.makos.validator.custom.MakosRequestValid;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDIslenecekEvrakListesiRequest extends BaseMakosRequest {

    //null ise tum turleri getir
    IDKararTuru kararTuru;

    Long atananKullaniciId;

    String gorevTipi;

    boolean tanimlama;

    boolean onaylama;

    boolean nobetci;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDIslenecekEvrakListesiRequest is valid");
        ValidationResult validationResult = new ValidationResult(true);

        return validationResult;
    }

}

