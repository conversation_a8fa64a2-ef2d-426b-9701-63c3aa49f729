package iym.makos.mapper;

import iym.common.model.entity.iym.Iller;
import org.springframework.stereotype.Component;
import iym.makos.model.dto.db.IllerDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for Iller entity and DTO
 */
@Component
public class IllerMapper {

    /**
     * Convert entity to DTO
     * @param entity Iller entity
     * @return IllerDTO
     */
    public IllerDTO toDto(Iller entity) {
        if (entity == null) {
            return null;
        }

        return IllerDTO.builder()
                .ilKod(entity.getIlKod())
                .ilAdi(entity.getIlAdi())
                .ilceAdi(entity.getIlceAdi())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto IllerDTO
     * @return Iller entity
     */
    public Iller toEntity(IllerDTO dto) {
        if (dto == null) {
            return null;
        }

        return Iller.builder()
                .ilKod(dto.getIlKod())
                .ilAdi(dto.getIlAdi())
                .ilceAdi(dto.getIlceAdi())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity Iller entity to update
     * @param dto IllerDTO with new values
     * @return Updated Iller entity
     */
    public Iller updateEntityFromDto(Iller entity, IllerDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        // Primary key should not be updated
        entity.setIlAdi(dto.getIlAdi());
        entity.setIlceAdi(dto.getIlceAdi());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of Iller entities
     * @return List of IllerDTO
     */
    public List<IllerDTO> toDtoList(List<Iller> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of IllerDTO
     * @return List of Iller entities
     */
    public List<Iller> toEntityList(List<IllerDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
