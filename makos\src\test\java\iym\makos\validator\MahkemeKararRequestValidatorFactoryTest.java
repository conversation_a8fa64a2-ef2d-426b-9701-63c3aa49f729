package iym.makos.validator;

import iym.common.enums.KararTuru;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.model.dto.mahkemekarar.id.GenelEvrakRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.List;

import static iym.makos.domain.utils.TestAssertions.assertValidatorType;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * Unit tests for MahkemeKararRequestValidatorFactory.
 *
 * Tests the factory pattern implementation for validator creation.
 * Verifies correct validator selection based on request type and KararTuru.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("MahkemeKararRequestValidatorFactory Unit Tests")
class MahkemeKararRequestValidatorFactoryTest extends BaseDomainUnitTest {
    
    @Mock
    private IMahkemeKararRequestValidator<GenelEvrakRequest> mockGenelValidator;
    
    @Mock
    private IMahkemeKararRequestValidator<GenelEvrakRequest> mockAnotherValidator;
    
    private MahkemeKararRequestValidatorFactory validatorFactory;
    
    @BeforeEach
    void setUp() {
        // Given - Mock validator configurations
        when(mockGenelValidator.getRelatedRequestType()).thenReturn(GenelEvrakRequest.class);
        when(mockGenelValidator.getRelatedKararTuru()).thenReturn(KararTuru.GENEL_EVRAK);
        
        when(mockAnotherValidator.getRelatedRequestType()).thenReturn(GenelEvrakRequest.class);
        when(mockAnotherValidator.getRelatedKararTuru()).thenReturn(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR);
        
        // Create factory with mock validators
        List<IMahkemeKararRequestValidator<?>> validators = Arrays.asList(mockGenelValidator, mockAnotherValidator);
        validatorFactory = new MahkemeKararRequestValidatorFactory(validators);
    }
    
    @Test
    @DisplayName("Should return correct validator when request type matches")
    void shouldReturnCorrectValidator_whenRequestTypeMatches() {
        // When
        @SuppressWarnings("unchecked")
        IMahkemeKararRequestValidator<GenelEvrakRequest> result =
            (IMahkemeKararRequestValidator<GenelEvrakRequest>) validatorFactory.getValidator(KararTuru.GENEL_EVRAK);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(mockGenelValidator);
        assertValidatorType(result, IMahkemeKararRequestValidator.class);
    }
    
    @Test
    @DisplayName("Should return correct validator when KararTuru matches")
    void shouldReturnCorrectValidator_whenKararTuruMatches() {
        // When
        IMahkemeKararRequestValidator<?> result = 
            validatorFactory.getValidator(KararTuru.GENEL_EVRAK);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(mockGenelValidator);
        assertValidatorType(result, IMahkemeKararRequestValidator.class);
    }
    
    @Test
    @DisplayName("Should return different validator for different KararTuru")
    void shouldReturnDifferentValidator_forDifferentKararTuru() {
        // When
        IMahkemeKararRequestValidator<?> result = 
            validatorFactory.getValidator(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(mockAnotherValidator);
        assertValidatorType(result, IMahkemeKararRequestValidator.class);
    }
    
    @Test
    @DisplayName("Should return null when no validator found for unknown KararTuru")
    void shouldReturnNull_whenNoValidatorFoundForUnknownKararTuru() {
        // Given - A KararTuru that doesn't have a validator
        KararTuru unknownKararTuru = KararTuru.ILETISIMIN_TESPITI; // Assuming this doesn't have a validator

        // Create empty factory
        List<IMahkemeKararRequestValidator<?>> emptyValidators = Arrays.asList();
        MahkemeKararRequestValidatorFactory emptyFactory =
            new MahkemeKararRequestValidatorFactory(emptyValidators);

        // When
        IMahkemeKararRequestValidator<?> result =
            emptyFactory.getValidator(unknownKararTuru);

        // Then
        assertThat(result).isNull();
    }
    
    @Test
    @DisplayName("Should return null when no validator found for KararTuru")
    void shouldReturnNull_whenNoValidatorFoundForKararTuru() {
        // Given - A KararTuru that doesn't have a validator
        KararTuru unknownKararTuru = KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI; // Assuming this doesn't have a validator
        
        // When
        IMahkemeKararRequestValidator<?> result = 
            validatorFactory.getValidator(unknownKararTuru);
        
        // Then
        assertThat(result).isNull();
    }
    
    @Test
    @DisplayName("Should handle multiple validators correctly")
    void shouldHandleMultipleValidators_correctly() {
        // When - Get validators for different KararTuru values
        IMahkemeKararRequestValidator<?> genelValidator = 
            validatorFactory.getValidator(KararTuru.GENEL_EVRAK);
        IMahkemeKararRequestValidator<?> yeniKararValidator = 
            validatorFactory.getValidator(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR);
        
        // Then
        assertThat(genelValidator).isNotNull();
        assertThat(yeniKararValidator).isNotNull();
        assertThat(genelValidator).isNotEqualTo(yeniKararValidator);
        assertThat(genelValidator).isEqualTo(mockGenelValidator);
        assertThat(yeniKararValidator).isEqualTo(mockAnotherValidator);
    }
    
    @Test
    @DisplayName("Should initialize factory with empty validator list")
    void shouldInitializeFactory_withEmptyValidatorList() {
        // Given
        List<IMahkemeKararRequestValidator<?>> emptyValidators = Arrays.asList();
        
        // When
        MahkemeKararRequestValidatorFactory emptyFactory = 
            new MahkemeKararRequestValidatorFactory(emptyValidators);
        
        // Then - Should not throw exception during initialization
        assertThat(emptyFactory).isNotNull();
        
        // But should return null when trying to get a validator
        IMahkemeKararRequestValidator<?> result = 
            emptyFactory.getValidator(GenelEvrakRequest.class);
        assertThat(result).isNull();
    }
}
