package iym.makos.domain.mahkemekarar.dbhandler;

import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.db.jpa.dao.mk.MahkemeKararRepo;
import iym.db.jpa.dao.mk.HedeflerRepo;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.DetayMahkemeKararTalepRepo;
import iym.db.jpa.dao.HedeflerTalepRepo;
import iym.db.jpa.dao.talep.HedeflerAidiyatTalepRepo;
import iym.db.jpa.dao.talep.MahkemeAidiyatTalepRepo;
import iym.db.jpa.dao.talep.MahkemeSuclarTalepRepo;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.dto.mahkemekarar.id.GenelEvrakRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Date;

import static iym.makos.domain.testdata.TestDataBuilder.createValidGenelEvrakRequest;
import static iym.makos.domain.utils.TestAssertions.assertValidEvrakId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.*;

/**
 * Unit tests for GenelEvrakDBSaveHandler.
 * 
 * Tests the DB handler implementation for general evrak save operations.
 * Verifies database save operations and error handling scenarios.
 * 
 * <AUTHOR> Team
 */
@DisplayName("GenelEvrakDBSaveHandler Unit Tests")
class GenelEvrakDBSaveHandlerTest extends BaseDomainUnitTest {
    
    @Mock
    private MahkemeKararRepo mockMahkemeKararRepo;
    
    @Mock
    private MahkemeKararTalepRepo mockMahkemeKararTalepRepo;
    
    @Mock
    private DetayMahkemeKararTalepRepo mockDetayMahkemeKararTalepRepo;
    
    @Mock
    private HedeflerTalepRepo mockHedeflerTalepRepo;
    
    @Mock
    private HedeflerRepo mockHedeflerRepo;
    
    @Mock
    private HedeflerAidiyatTalepRepo mockHedeflerAidiyatTalepRepo;
    
    @Mock
    private KararRequestMapper mockKararRequestMapper;
    
    @Mock
    private MahkemeAidiyatTalepRepo mockMahkemeAidiyatTalepRepo;
    
    @Mock
    private MahkemeSuclarTalepRepo mockMahkemeSuclarTalepRepo;
    
    @Mock
    private MahkemeKararRequestCommonDbSaver mockCommonDbSaver;
    
    @InjectMocks
    private GenelEvrakDBSaveHandler dbHandler;
    
    private GenelEvrakRequest testRequest;
    private Date testDate;
    private Long testUserId;
    
    @BeforeEach
    void setUp() {
        testRequest = createValidGenelEvrakRequest();
        testDate = createTestDate();
        testUserId = TEST_USER_ID;
    }
    
    @Test
    @DisplayName("Should save evrak successfully when all operations succeed")
    void shouldSaveEvrak_successfully_whenAllOperationsSucceed() throws Exception {
        // Given
        Long expectedEvrakId = 12345L;
        when(mockCommonDbSaver.handleDbSave(testRequest, testDate, testUserId))
            .thenReturn(expectedEvrakId);

        // Mock the repository to return a valid MahkemeKararTalep
        when(mockMahkemeKararTalepRepo.findById(expectedEvrakId))
            .thenReturn(java.util.Optional.of(new MahkemeKararTalep()));

        // When
        Long result = dbHandler.kaydet(testRequest, testDate, testUserId);

        // Then
        assertThat(result).isNotNull();
        assertValidEvrakId(result);
        assertThat(result).isEqualTo(expectedEvrakId);

        // Verify common db saver was called
        verify(mockCommonDbSaver).handleDbSave(testRequest, testDate, testUserId);
        verify(mockMahkemeKararTalepRepo).findById(expectedEvrakId);
    }
    
    @Test
    @DisplayName("Should throw exception when common db saver fails")
    void shouldThrowException_whenCommonDbSaverFails() throws Exception {
        // Given
        String errorMessage = "Database connection failed";
        when(mockCommonDbSaver.handleDbSave(testRequest, testDate, testUserId))
            .thenThrow(new RuntimeException(errorMessage));
        
        // When & Then
        assertThatThrownBy(() -> dbHandler.kaydet(testRequest, testDate, testUserId))
            .isInstanceOf(RuntimeException.class)
            .hasMessageContaining(errorMessage);
        
        // Verify common db saver was called
        verify(mockCommonDbSaver).handleDbSave(testRequest, testDate, testUserId);
    }
    
    @Test
    @DisplayName("Should handle null request gracefully")
    void shouldHandleNullRequest_gracefully() throws Exception {
        // Given - Mock common db saver to throw exception for null request
        when(mockCommonDbSaver.handleDbSave(isNull(), any(), any()))
            .thenThrow(new RuntimeException("Request cannot be null"));

        // When & Then
        assertThatThrownBy(() -> dbHandler.kaydet(null, testDate, testUserId))
            .isInstanceOf(RuntimeException.class);

        // Verify common db saver was called with null request
        verify(mockCommonDbSaver).handleDbSave(isNull(), eq(testDate), eq(testUserId));
    }

    @Test
    @DisplayName("Should handle null date gracefully")
    void shouldHandleNullDate_gracefully() throws Exception {
        // Given - Mock common db saver to throw exception for null date
        when(mockCommonDbSaver.handleDbSave(any(), isNull(), any()))
            .thenThrow(new RuntimeException("Date cannot be null"));

        // When & Then
        assertThatThrownBy(() -> dbHandler.kaydet(testRequest, null, testUserId))
            .isInstanceOf(RuntimeException.class);

        // Verify common db saver was called with null date
        verify(mockCommonDbSaver).handleDbSave(eq(testRequest), isNull(), eq(testUserId));
    }

    @Test
    @DisplayName("Should handle null user ID gracefully")
    void shouldHandleNullUserId_gracefully() throws Exception {
        // Given - Mock common db saver to throw exception for null user ID
        when(mockCommonDbSaver.handleDbSave(any(), any(), isNull()))
            .thenThrow(new RuntimeException("User ID cannot be null"));

        // When & Then
        assertThatThrownBy(() -> dbHandler.kaydet(testRequest, testDate, null))
            .isInstanceOf(RuntimeException.class);

        // Verify common db saver was called with null user ID
        verify(mockCommonDbSaver).handleDbSave(eq(testRequest), eq(testDate), isNull());
    }
    
    @Test
    @DisplayName("Should return correct request type")
    void shouldReturnCorrectRequestType() {
        // When
        Class<GenelEvrakRequest> requestType = dbHandler.getRelatedRequestType();
        
        // Then
        assertThat(requestType).isEqualTo(GenelEvrakRequest.class);
    }
    
    @Test
    @DisplayName("Should delegate to common db saver for main save operation")
    void shouldDelegateToCommonDbSaver_forMainSaveOperation() throws Exception {
        // Given
        Long expectedEvrakId = 54321L;
        when(mockCommonDbSaver.handleDbSave(testRequest, testDate, testUserId))
            .thenReturn(expectedEvrakId);

        // Mock the repository to return a valid MahkemeKararTalep
        when(mockMahkemeKararTalepRepo.findById(expectedEvrakId))
            .thenReturn(java.util.Optional.of(new MahkemeKararTalep()));

        // When
        Long result = dbHandler.kaydet(testRequest, testDate, testUserId);

        // Then
        assertThat(result).isEqualTo(expectedEvrakId);

        // Verify delegation occurred with correct parameters
        verify(mockCommonDbSaver).handleDbSave(
            eq(testRequest),
            eq(testDate),
            eq(testUserId)
        );
        verify(mockMahkemeKararTalepRepo).findById(expectedEvrakId);
    }
    
    @Test
    @DisplayName("Should handle database transaction rollback scenario")
    void shouldHandleDatabaseTransactionRollback_scenario() throws Exception {
        // Given - Common db saver succeeds but subsequent operation fails
        Long evrakId = 12345L;
        when(mockCommonDbSaver.handleDbSave(testRequest, testDate, testUserId))
            .thenReturn(evrakId);

        // Mock the repository to return a valid MahkemeKararTalep
        when(mockMahkemeKararTalepRepo.findById(evrakId))
            .thenReturn(java.util.Optional.of(new MahkemeKararTalep()));

        // When
        Long result = dbHandler.kaydet(testRequest, testDate, testUserId);

        // Then - Should still return the evrak ID from common saver
        assertThat(result).isEqualTo(evrakId);

        // Verify common db saver was called
        verify(mockCommonDbSaver).handleDbSave(testRequest, testDate, testUserId);
        verify(mockMahkemeKararTalepRepo).findById(evrakId);
    }
    
    @Test
    @DisplayName("Should handle concurrent save operations")
    void shouldHandleConcurrentSaveOperations() throws Exception {
        // Given
        Long firstEvrakId = 11111L;
        Long secondEvrakId = 22222L;

        when(mockCommonDbSaver.handleDbSave(any(GenelEvrakRequest.class), any(Date.class), any(Long.class)))
            .thenReturn(firstEvrakId)
            .thenReturn(secondEvrakId);

        // Mock the repository to return valid MahkemeKararTalep for both calls
        when(mockMahkemeKararTalepRepo.findById(firstEvrakId))
            .thenReturn(java.util.Optional.of(new MahkemeKararTalep()));
        when(mockMahkemeKararTalepRepo.findById(secondEvrakId))
            .thenReturn(java.util.Optional.of(new MahkemeKararTalep()));

        // When - Simulate concurrent calls
        Long result1 = dbHandler.kaydet(testRequest, testDate, testUserId);
        Long result2 = dbHandler.kaydet(testRequest, testDate, testUserId + 1);

        // Then
        assertThat(result1).isEqualTo(firstEvrakId);
        assertThat(result2).isEqualTo(secondEvrakId);

        // Verify common db saver was called twice
        verify(mockCommonDbSaver, times(2)).handleDbSave(any(), any(), any());
        verify(mockMahkemeKararTalepRepo).findById(firstEvrakId);
        verify(mockMahkemeKararTalepRepo).findById(secondEvrakId);
    }
}
