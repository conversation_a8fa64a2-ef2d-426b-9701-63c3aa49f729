package iym.makos.mapper;

import iym.common.model.entity.iym.mk.MahkemeKararAidiyat;
import iym.makos.model.dto.db.MahkemeAidiyatDTO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for MahkemeAidiyatTalep entity and DTO
 */
@Component
public class MahkemeAidiyatMapper {

    /**
     * Convert entity to DTO
     * @param entity MahkemeAidiyatTalep entity
     * @return MahkemeAidiyatTalepDTO
     */
    public MahkemeAidiyatDTO toDto(MahkemeKararAidiyat entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeAidiyatDTO.builder()
                .id(entity.getId())
                .mahkemeKararId(entity.getMahkemeKararId())
                .aidiyatKod(entity.getAidiyatKod())
               // .durumu(entity.getDurumu())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto MahkemeAidiyatTalepDTO
     * @return MahkemeAidiyatTalep entity
     */
    public MahkemeKararAidiyat toEntity(MahkemeAidiyatDTO dto) {
        if (dto == null) {
            return null;
        }

        return MahkemeKararAidiyat.builder()
                .id(dto.getId())
                .mahkemeKararId(dto.getMahkemeKararId())
                .aidiyatKod(dto.getAidiyatKod())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity MahkemeAidiyatTalep entity to update
     * @param dto MahkemeAidiyatTalepDTO with new values
     * @return Updated MahkemeAidiyatTalep entity
     */
    public MahkemeKararAidiyat updateEntityFromDto(MahkemeKararAidiyat entity, MahkemeAidiyatDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setMahkemeKararId(dto.getMahkemeKararId());
        entity.setAidiyatKod(dto.getAidiyatKod());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entities List of MahkemeAidiyatTalep entities
     * @return List of MahkemeAidiyatTalepDTO
     */
    public List<MahkemeAidiyatDTO> toDtoList(List<MahkemeKararAidiyat> entities) {
        if (entities == null) {
            return List.of();
        }

        return entities.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
