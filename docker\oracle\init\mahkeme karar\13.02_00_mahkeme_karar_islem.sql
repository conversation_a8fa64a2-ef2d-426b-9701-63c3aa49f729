-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

----------KULLANILIYOR MU?

-- Create sequence for MAHKEME_KARAR_ISLEM_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAHKEME_KARAR_ISLEM_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_KARAR_ISLEM_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

--TODO : Primary key eklenecek
--TODO : vharchar daki byte lar kaldirilacak
-- Create MAHKEME_KARAR_ISLEM table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_KARAR_ISLEM';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_KARAR_ISLEM (
         ID NUMBER NOT NULL
        , EVRAK_ID NUMBER NOT NULL
        , KULLANICI_ID NUMBER NOT NULL
        , KAYIT_TARIHI DATE NOT NULL
        , DURUM VARCHAR2(20)
        , HUKUK_BIRIM VARCHAR2(50)
        , KARAR_TIP VARCHAR2(20)
        , MAH_KARAR_BAS_TAR DATE
        , MAH_KARAR_BITIS_TAR DATE
        , MAHKEME_ADI VARCHAR2(250)
        , MAHKEME_KARAR_NO VARCHAR2(50)
        , MAHKEME_ILI NUMBER
        , ACIKLAMA VARCHAR2(500 )
        , HAKIM_SICIL_NO VARCHAR2(20 )
        , SORUSTURMA_NO VARCHAR2(50 )
        , ISLEM_TIPI VARCHAR2(10 )
        , MAHKEME_KARAR_ID NUMBER
        , ISLEM_NEDENI VARCHAR2(15)
        , MAHKEME_KODU VARCHAR2(10 )
    )';

  END IF;
END;
/

COMMIT;
