package iym.makos.validator;

import iym.common.enums.EvrakKurum;
import iym.common.enums.GuncellemeTip;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.mk.MahkemeKararAidiyat;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.service.db.mk.DbMahkemeKararAidiyatService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.validation.ValidationResult;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.testdata.TestDataBuilder;
import iym.makos.model.api.AidiyatGuncellemeDetay;
import iym.makos.model.api.AidiyatGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mahkemekarar.id.IDAidiyatBilgisiGuncellemeRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Optional;

import static iym.makos.domain.testdata.TestDataBuilder.createValidIDAidiyatBilgisiGuncellemeRequest;
import static iym.makos.domain.utils.TestAssertions.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * Unit tests for IDAidiyatBilgisiGuncellemeValidator.
 *
 * Tests validation logic for ID aidiyat bilgisi güncelleme (affiliation update) requests.
 * Uses LENIENT mock settings for flexibility.
 *
 * <AUTHOR> Team
 */
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("IDAidiyatBilgisiGuncellemeValidator Unit Tests")
class IDAidiyatBilgisiGuncellemeValidatorTest extends BaseDomainUnitTest {

    @Mock
    private DbMahkemeKararService mockDbMahkemeKararService;

    @Mock
    private DbMahkemeKararAidiyatService mockDbMahkemeKararAidiyatService;

    @Mock
    private MahkemeKararRequestCommonValidator mockCommonValidator;

    @InjectMocks
    private IDAidiyatBilgisiGuncellemeValidator validator;

    private IDAidiyatBilgisiGuncellemeRequest validRequest;

    @BeforeEach
    void setUp() {
        validRequest = createValidIDAidiyatBilgisiGuncellemeRequest();
        setupValidMocks();
    }

    private void setupValidMocks() {
        // Mock common validator to return valid result
        when(mockCommonValidator.validate(any(IDAidiyatBilgisiGuncellemeRequest.class)))
                .thenReturn(new ValidationResult(true));

        // Mock mahkeme karar service to return valid mahkeme karar
        MahkemeKarar mockMahkemeKarar = new MahkemeKarar();
        mockMahkemeKarar.setId(1L);
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.of(mockMahkemeKarar));

        // Set the common validator mock
        validator.setMahkemeKararRequestCommonValidator(mockCommonValidator);
    }

    @Test
    @DisplayName("Should pass validation when request is valid")
    void shouldPassValidation_whenRequestIsValid() {
        // Given
        setupValidMocks();

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationSuccess(result);
    }

    @Test
    @DisplayName("Should fail validation when mahkeme karar not found")
    void shouldFailValidation_whenMahkemeKararNotFound() {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Optional.empty());

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
                reason.contains("Mahkeme Karar Bulunamadı"));
    }

    @Test
    @DisplayName("Should fail validation when trying to add existing aidiyat")
    void shouldFailValidation_whenTryingToAddExistingAidiyat() {
        // Given
        MahkemeKararAidiyat existingAidiyat = new MahkemeKararAidiyat();
        when(mockDbMahkemeKararAidiyatService.findByMahkemeKararIdAndAidiyatKod(anyLong(), anyString()))
                .thenReturn(Optional.of(existingAidiyat));

        // Create request with EKLE operation
        AidiyatGuncellemeDetay aidiyatDetay = AidiyatGuncellemeDetay.builder()
                .aidiyatKodu("TEST_AIDIYAT")
                .guncellemeTip(GuncellemeTip.EKLE)
                .build();

        AidiyatGuncellemeKararDetay kararDetay = AidiyatGuncellemeKararDetay.builder()
                .mahkemeKararDetay(MahkemeKararDetay.builder()
                        .mahkemeIlIlceKodu("34001")
                        .mahkemeKodu("34001")
                        .mahkemeKararNo("2024/001")
                        .sorusturmaNo("2024/001")
                        .build())
                .aidiyatGuncellemeDetayListesi(Arrays.asList(aidiyatDetay))
                .build();

        validRequest.setAidiyatGuncellemeKararDetayListesi(Arrays.asList(kararDetay));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
                reason.contains("aidiyatı mahkeme karar bilgisinde bulunduğu için eklenemez"));
    }

    @Test
    @DisplayName("Should fail validation when trying to remove non-existing aidiyat")
    void shouldFailValidation_whenTryingToRemoveNonExistingAidiyat() {
        // Given
        when(mockDbMahkemeKararAidiyatService.findByMahkemeKararIdAndAidiyatKod(anyLong(), anyString()))
                .thenReturn(Optional.empty());

        // Create request with CIKAR operation
        AidiyatGuncellemeDetay aidiyatDetay = AidiyatGuncellemeDetay.builder()
                .aidiyatKodu("NON_EXISTING_AIDIYAT")
                .guncellemeTip(GuncellemeTip.CIKAR)
                .build();

        AidiyatGuncellemeKararDetay kararDetay = AidiyatGuncellemeKararDetay.builder()
                .mahkemeKararDetay(MahkemeKararDetay.builder()
                        .mahkemeIlIlceKodu("34001")
                        .mahkemeKodu("34001")
                        .mahkemeKararNo("2024/001")
                        .sorusturmaNo("2024/001")
                        .build())
                .aidiyatGuncellemeDetayListesi(Arrays.asList(aidiyatDetay))
                .build();

        validRequest.setAidiyatGuncellemeKararDetayListesi(Arrays.asList(kararDetay));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).anyMatch(reason ->
                reason.contains("aidiyatı mahkeme karar bilgisinde bulunamadığı için çıkarılamaz"));
    }

    @Test
    @DisplayName("Should handle validation exception gracefully")
    void shouldHandleValidationException_gracefully() {
        // Given
        when(mockDbMahkemeKararService.findBy(anyString(), anyString(), anyString(), anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Validation failed. Internal error");
    }

    @Test
    @DisplayName("Should return correct karar turu")
    void shouldReturnCorrectKararTuru() {
        // When
        KararTuru result = validator.getRelatedKararTuru();

        // Then
        assertEquals(KararTuru.ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME, result);
    }

    @Test
    @DisplayName("Should return early when request.isValid() fails")
    void shouldReturnEarly_whenRequestIsValidFails() {
        // Given - Mock common validator to return invalid result
        when(mockCommonValidator.validate(any(IDAidiyatBilgisiGuncellemeRequest.class)))
                .thenReturn(new ValidationResult("Request validation failed"));

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        assertThat(result.getReasons()).contains("Request validation failed");
    }

    @Test
    @DisplayName("Should return early when common validation fails")
    void shouldReturnEarly_whenCommonValidationFails() {
        // Given - Create request with wrong karar turu to make isValid() fail
        validRequest.setKararTuru(KararTuru.GENEL_EVRAK);

        // When
        ValidationResult result = validator.validate(validRequest);

        // Then
        assertValidationFailure(result);
        // Should return early without calling database services
    }
}
