package iym.makos.service.db;

import iym.common.model.entity.iym.Iller;
import iym.common.service.db.DbIllerService;
import iym.makos.model.dto.db.IllerDTO;
import iym.makos.mapper.IllerMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class IllerService {

    private final DbIllerService dbIllerService;
    private final IllerMapper illerMapper;

    public IllerDTO findByKodu(String ilIlceKod) {
        Optional<Iller> ilIlce = dbIllerService.getByIlIlceKodu(ilIlceKod);
        return ilIlce.map(illerMapper::toDto).orElse(null);
    }

    /**
     * Get all Iller records
     * @return List of IllerDTO
     */
    public List<IllerDTO> findAll() {
        List<Iller> illerList = dbIllerService.findAll();
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Get all Iller records ordered by ilAdi
     * @return List of IllerDTO
     */
    public List<IllerDTO> findAllOrderedByIlAdi() {
        List<Iller> illerList = dbIllerService.findAllByOrderByIlAdiAsc();
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Get all Iller records ordered by ilKod
     * @return List of IllerDTO
     */
    public List<IllerDTO> findAllOrderedByIlKod() {
        List<Iller> illerList = dbIllerService.findAllByOrderByIlKodAsc();
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Get all Iller records with pagination
     * @param pageable Pagination information
     * @return Page of IllerDTO
     */
    public Page<IllerDTO> findAll(Pageable pageable) {
        Page<Iller> illerPage = dbIllerService.findAll(pageable);
        List<IllerDTO> dtoList = illerMapper.toDtoList(illerPage.getContent());
        return new PageImpl<>(dtoList, pageable, illerPage.getTotalElements());
    }

    /**
     * Get Iller by ilKod
     * @param ilKod İl kodu
     * @return IllerDTO
     */
    public IllerDTO findById(String ilKod) {
        Iller iller = dbIllerService.findById(ilKod)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "İl bulunamadı: " + ilKod));
        return illerMapper.toDto(iller);
    }

    /**
     * Get Iller by ilAdi
     * @param ilAdi İl adı
     * @return List of IllerDTO
     */
    public List<IllerDTO> findByIlAdi(String ilAdi) {
        List<Iller> illerList = dbIllerService.findByIlAdi(ilAdi);
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Get Iller by ilceAdi
     * @param ilceAdi İlçe adı
     * @return List of IllerDTO
     */
    public List<IllerDTO> findByIlceAdi(String ilceAdi) {
        List<Iller> illerList = dbIllerService.findByIlceAdi(ilceAdi);
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Get Iller by ilAdi and ilceAdi
     * @param ilAdi İl adı
     * @param ilceAdi İlçe adı
     * @return List of IllerDTO
     */
    public List<IllerDTO> findByIlAdiAndIlceAdi(String ilAdi, String ilceAdi) {
        List<Iller> illerList = dbIllerService.findByIlAdiAndIlceAdi(ilAdi, ilceAdi);
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Get Iller by ilKod prefix
     * @param ilKodPrefix İl kodu prefix
     * @return List of IllerDTO
     */
    public List<IllerDTO> findByIlKodStartingWith(String ilKodPrefix) {
        List<Iller> illerList = dbIllerService.findByIlKodStartingWith(ilKodPrefix);
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Search Iller by ilAdi
     * @param ilAdi İl adı
     * @return List of IllerDTO
     */
    public List<IllerDTO> findByIlAdiContainingIgnoreCase(String ilAdi) {
        List<Iller> illerList = dbIllerService.findByIlAdiContainingIgnoreCase(ilAdi);
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Search Iller by ilceAdi
     * @param ilceAdi İlçe adı
     * @return List of IllerDTO
     */
    public List<IllerDTO> findByIlceAdiContainingIgnoreCase(String ilceAdi) {
        List<Iller> illerList = dbIllerService.findByIlceAdiContainingIgnoreCase(ilceAdi);
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Get ilçeler by ilAdi ordered by ilceAdi
     * @param ilAdi İl adı
     * @return List of IllerDTO
     */
    public List<IllerDTO> findByIlAdiOrderByIlceAdiAsc(String ilAdi) {
        List<Iller> illerList = dbIllerService.findByIlAdiOrderByIlceAdiAsc(ilAdi);
        return illerMapper.toDtoList(illerList);
    }

    /**
     * Check if Iller exists by ilAdi and ilceAdi
     * @param ilAdi İl adı
     * @param ilceAdi İlçe adı
     * @return true if exists, false otherwise
     */
    public boolean existsByIlAdiAndIlceAdi(String ilAdi, String ilceAdi) {
        return dbIllerService.existsByIlAdiAndIlceAdi(ilAdi, ilceAdi);
    }

    /**
     * Create new Iller
     * @param illerDTO IllerDTO
     * @return Created IllerDTO
     */
    public IllerDTO create(IllerDTO illerDTO) {
        // Check if ilKod already exists
        if (dbIllerService.existsById(illerDTO.getIlKod())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "İl kodu zaten mevcut: " + illerDTO.getIlKod());
        }

        // Check if ilAdi and ilceAdi combination already exists
        if (illerDTO.getIlAdi() != null && illerDTO.getIlceAdi() != null && 
                dbIllerService.existsByIlAdiAndIlceAdi(illerDTO.getIlAdi(), illerDTO.getIlceAdi())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, 
                    "Bu il ve ilçe kombinasyonu zaten mevcut: " + illerDTO.getIlAdi() + " - " + illerDTO.getIlceAdi());
        }

        Iller iller = illerMapper.toEntity(illerDTO);
        dbIllerService.save(iller);
        log.info("İl oluşturuldu: {}", iller.getIlKod());
        return illerMapper.toDto(iller);
    }

    /**
     * Update existing Iller
     * @param ilKod İl kodu
     * @param illerDTO IllerDTO
     * @return Updated IllerDTO
     */
    public IllerDTO update(String ilKod, IllerDTO illerDTO) {
        Iller existingIller = dbIllerService.findById(ilKod)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "İl bulunamadı: " + ilKod));

        // Check if ilAdi and ilceAdi combination already exists for another entity
        if (illerDTO.getIlAdi() != null && illerDTO.getIlceAdi() != null) {
            List<Iller> existingCombinations = dbIllerService.findByIlAdiAndIlceAdi(illerDTO.getIlAdi(), illerDTO.getIlceAdi());
            boolean alreadyExists = existingCombinations.stream()
                    .anyMatch(i -> !i.getIlKod().equals(ilKod));
            
            if (alreadyExists) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, 
                        "Bu il ve ilçe kombinasyonu zaten mevcut: " + illerDTO.getIlAdi() + " - " + illerDTO.getIlceAdi());
            }
        }

        // Ensure ilKod is not changed
        illerDTO.setIlKod(ilKod);

        Iller updatedIller = illerMapper.updateEntityFromDto(existingIller, illerDTO);
        dbIllerService.update(updatedIller);
        log.info("İl güncellendi: {}", updatedIller.getIlKod());
        return illerMapper.toDto(updatedIller);
    }

    /**
     * Delete Iller
     * @param ilKod İl kodu
     */
    public void delete(String ilKod) {
        Iller iller = dbIllerService.findById(ilKod)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "İl bulunamadı: " + ilKod));
        dbIllerService.delete(iller);
        log.info("İl silindi: {}", ilKod);
    }
}
