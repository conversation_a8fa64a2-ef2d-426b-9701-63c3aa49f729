package iym.makos.model.dto.mahkemekarar.id;

import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.dto.mahkemekarar.MahkemeKararRequest;
import iym.makos.validator.custom.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
/*
Herbir hedefin icinde ayri bir detay tutmaktansa, bir detay karara ait
birden fazla herdef gonderilebilirdi. <PERSON>ce daha statil bir yapiya kavusurur
* */

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDSonlandirmaKarariRequest extends MahkemeKararRequest {

    @NotNull
    @Size(min = 1)
    @Valid
    private List<IDHedefDetay> hedefDetayListesi;

    private List<String> mahkemeAidiyatKodlari;

    private List<String> mahkemeSucTipiKodlari;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDKararRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            MahkemeKararTip mahkemeKararTipi = mahkemeKararBilgisi.getMahkemeKararTipi();

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI.name() + " olmalıdır");
                return validationResult;
            }

            boolean sonlandirmaMahkemeKararTipinde = CommonUtils.sonlandirmaMahkemeKararTipi(mahkemeKararTipi);
            if (!sonlandirmaMahkemeKararTipinde) {
                validationResult.addFailedReason("Mahkeme karar tipi, sonlandırma kararı için uygun değildir!");
            }

            for (IDHedefDetay IDHedefDetay : hedefDetayListesi) {
                if (IDHedefDetay.getIlgiliMahkemeKararDetayi() == null) {
                    validationResult.addFailedReason("Sonlandirma Kararinda ilgili mahkeme karari bos olamaz!");
                }

                if (!CommonUtils.isNullOrEmpty(IDHedefDetay.getCanakNo())) {
                    validationResult.addFailedReason("Sonlandirma kararında CANAK numarası girilemez.");
                }

                if (IDHedefDetay.getUzatmaSayisi() != null) {
                    validationResult.addFailedReason("Sonlandirma Kararinda uzatma sayisi dolu olamaz!");
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI;
    }

}

