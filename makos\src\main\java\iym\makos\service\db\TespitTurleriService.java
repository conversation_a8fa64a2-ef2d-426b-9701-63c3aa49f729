package iym.makos.service.db;

import iym.common.model.entity.iym.SorguTipleri;
import iym.common.model.entity.iym.TespitTurleri;
import iym.common.service.db.DbSorguTipleriService;
import iym.common.service.db.DbTespitTurleriService;
import iym.common.util.CommonUtils;
import iym.makos.mapper.SorguTipiMapper;
import iym.makos.mapper.TespitTuruMapper;
import iym.makos.model.dto.db.SorguTipiDTO;
import iym.makos.model.dto.db.TespitTuruDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TespitTurleriService {

    private final DbTespitTurleriService dbTespitTurleriService;
    private final TespitTuruMapper tespitTuruMapper;



    public List<TespitTuruDTO> findAllTespitTurleri(){
        List<TespitTurleri> tespitTurleri = dbTespitTurleriService.findAll();

        List<TespitTuruDTO> result = CommonUtils.safeList(tespitTurleri
                ).stream()
                .map(tespitTuruMapper::toDto)
                .collect(Collectors.toList());

        return result;

    }

}
