package iym.makos.domain.mahkemekarar.dbhandler;

import iym.common.enums.EvrakKurum;
import iym.common.enums.EvrakTuru;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.*;
import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.*;
import iym.db.jpa.dao.talep.HtsMahkemeKararTalepRepo;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.dto.mahkemekarar.MahkemeKararRequest;
import iym.makos.utils.UtilService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

@Service
@Slf4j
public class MahkemeKararRequestCommonDbSaver {

    private UtilService utilService;
    private KararRequestMapper kararRequestMapper;
    private EvrakKayitRepo evrakKayitRepo;
    private MahkemeBilgisiRepo mahkemeBilgisiRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private HtsMahkemeKararTalepRepo htsMahkemeKararTalepRepo;

    @Autowired
    public MahkemeKararRequestCommonDbSaver(UtilService utilService
            , KararRequestMapper kararRequestMapper
            , EvrakKayitRepo evrakKayitRepo
            , MahkemeBilgisiRepo mahkemeBilgisiRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , HtsMahkemeKararTalepRepo htsMahkemeKararTalepRepo) {
        this.utilService = utilService;
        this.kararRequestMapper = kararRequestMapper;
        this.mahkemeBilgisiRepo = mahkemeBilgisiRepo;
        this.evrakKayitRepo = evrakKayitRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.htsMahkemeKararTalepRepo = htsMahkemeKararTalepRepo;
    }

    public Long handleDbSave(MahkemeKararRequest kararRequest, Date kayitTarihi, Long kullaniciId) throws Exception {

        // Validate required parameters
        if (kararRequest == null) {
            throw new RuntimeException("Request cannot be null");
        }
        if (kayitTarihi == null) {
            throw new RuntimeException("Date cannot be null");
        }
        if (kullaniciId == null) {
            throw new RuntimeException("User ID cannot be null");
        }

        Long mahkemeKararTalepId = 0L;
        String fileName = "";
        String evrakGelenKurumKodu = kararRequest.getEvrakDetay().getEvrakKurumKodu();
        EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

        MahkemeKararTip kararTipi = kararRequest.getMahkemeKararBilgisi().getMahkemeKararTipi();
        String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

        EvrakKayit savedEvrak = saveEvrakBilgileri(kararRequest.getEvrakDetay(), evrakTipi, kayitTarihi, kullaniciId, kararRequest.getKararTuru());
        Long savedEvrakId = savedEvrak.getId();

        //Evrak File Kaydet
        EvrakFiles evrakFile = kararRequestMapper.toEvrakFiles(savedEvrakId, fileName, false, 1);

        //Save Mahkeme Karar Talep
        String mahkemeKodu = kararRequest.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu();
        Optional<MahkemeBilgi> mahkemeBilgi = mahkemeBilgisiRepo.findByMahkemeKodu(mahkemeKodu);
        if (mahkemeBilgi.isEmpty()) {
            throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEBILGISI_BULUNAMADI, mahkemeKodu);
        }

        if (kararRequest.getEvrakDetay().getEvrakTuru() == EvrakTuru.ILETISIMIN_TESPITI) {
            HtsMahkemeKararTalep mahkemeKararTalep = kararRequestMapper.toHTSMahkemeKararTalep(kararRequest.getMahkemeKararBilgisi(), savedEvrakId, kullaniciId, kayitTarihi, mahkemeBilgi.get().getMahkemeAdi());
            HtsMahkemeKararTalep savedMahkemeKararTalep = htsMahkemeKararTalepRepo.save(mahkemeKararTalep);
            mahkemeKararTalepId = savedMahkemeKararTalep.getId();
        } else if (kararRequest.getEvrakDetay().getEvrakTuru() == EvrakTuru.ILETISIMIN_DENETLENMESI || kararRequest.getEvrakDetay().getEvrakTuru() == EvrakTuru.GENEL_EVRAK) {
            MahkemeKararTalep mahkemeKararTalep = kararRequestMapper.toMahkemeKararTalep(kararRequest.getMahkemeKararBilgisi(), savedEvrakId, kullaniciId, kayitTarihi, mahkemeBilgi.get().getMahkemeAdi());
            MahkemeKararTalep savedMahkemeKararTalep = mahkemeKararTalepRepo.save(mahkemeKararTalep);
            mahkemeKararTalepId = savedMahkemeKararTalep.getId();
        }

        return mahkemeKararTalepId;
    }

    private EvrakKayit saveEvrakBilgileri(EvrakDetay evrakDetay, String evrakTipi, Date islemTarihi, Long kaydedenKullaniciId, KararTuru kararTuru) throws Exception {

        String evrakGelenKurumKodu = evrakDetay.getEvrakKurumKodu();
        EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

        //MahkemeKararTip kararTipi = kararRequest.getMahkemeKararDetayi().getMahkemeKararBilgisi().getMahkemeKararTipi();
        //String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

        //Evrak sira numarasi
        String evrakSiraNo = utilService.getEvrakSiraNumarasi(evrakDetay.getEvrakKurumKodu(), evrakDetay.getEvrakTuru().name());
        if (CommonUtils.isNullOrEmpty(evrakSiraNo)) {
            throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_KAYIT_HATASI);
        }

        String gelenEvrakNo = evrakDetay.getEvrakNo();
        String evrakIlIlceKodu = evrakDetay.getGeldigiIlIlceKodu();
        String evrakKurumKodu = evrakDetay.getEvrakKurumKodu();

        //Bunu genel validatore koy
        Optional<EvrakKayit> ayniEvrak = evrakKayitRepo.findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(gelenEvrakNo, evrakIlIlceKodu, evrakKurumKodu);
        if (ayniEvrak.isPresent()) {
            throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_ZATEN_VAR, gelenEvrakNo, evrakIlIlceKodu, evrakKurumKodu);
        }

        //Save Evrak
        EvrakKayit evrakKayit = kararRequestMapper.toEvrakKayit(evrakDetay, evrakSiraNo, evrakTipi, islemTarihi, kaydedenKullaniciId);

        return evrakKayitRepo.save(evrakKayit);
    }


}
