package iym.makos.domain.talepislem.service;

import iym.common.enums.EvrakTuru;
import iym.common.enums.KararTuru;
import iym.common.enums.MahkemeKararTip;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.db.jpa.dao.EvrakKayitRepo;
import iym.db.jpa.dao.talep.MahkemeKararTalepRepo;
import iym.makos.domain.talepislem.processor.MahkemeKararTalepIsleyici;
import iym.makos.domain.talepislem.processor.MahkemeKararTalepOnayFactory;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateRequest;
import iym.makos.model.dto.talepislem.MahkemeKararTalepUpdateResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@Slf4j
public class TalepIslemService {

    private final MahkemeKararTalepOnayFactory mahkemeKararTalepOnayFactory;

    private final EvrakKayitRepo evrakKayitRepo;

    private final MahkemeKararTalepRepo mahkemeKararTalepRepo;

    @Autowired
    public TalepIslemService(MahkemeKararTalepOnayFactory mahkemeKararTalepOnayFactory, EvrakKayitRepo evrakKayitRepo, MahkemeKararTalepRepo mahkemeKararTalepRepo) {
        this.mahkemeKararTalepOnayFactory = mahkemeKararTalepOnayFactory;
        this.evrakKayitRepo = evrakKayitRepo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
    }

    public MahkemeKararTalepUpdateResponse process(MahkemeKararTalepUpdateRequest request, Long kullaniciId) {

        try{
            ValidationResult validationResult = validate(request);
            if (!validationResult.isValid()) {
                return MahkemeKararTalepUpdateResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

        /*
        Request'te karar turu direk geldigi halde kaydetmiyoruz. Onun yerine Evrak Kayit'Ta
        	- ILETISIMIN_TESPITI,
	        - ILETISIMIN_DENETLENMESI,
	        - GENEL_EVRAK;
	    Mahkeme karar'da ise MahkemeKararTip'te belirlenen tipleri kaydediyoruz.
	    Kaydedilmis bir mahkemekarartalep'in karar turu lazim olunca asagidaki gibi bir yolun
	    izlenmesi gerekiyor.
	    TODO:Bu yontem yerine mahkemekararTalep kaydedilirken direkt kararTuru de kaydedilip, gerektiginde bu tur dogrudan erisilir.

        * */
            KararTuru kararTuru = null;

            Optional<MahkemeKararTalep> mahkemeKararTalepOpt = mahkemeKararTalepRepo.findById(request.getMahkemeKararTalepId());
            if (mahkemeKararTalepOpt.isEmpty()) {
                return MahkemeKararTalepUpdateResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage("Onaylanacak mahkeme karar talep bulunamadı")
                                .build())
                        .build();
            }

            MahkemeKararTalep mahkemeKararTalep = mahkemeKararTalepOpt.get();
            if(!CommonUtils.isNullOrEmpty(mahkemeKararTalep.getDurum())){
                return MahkemeKararTalepUpdateResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage("Mahkeme karar talep üzerinde işlem yapılmış. Yeniden onay yapılamaz.")
                                .build())
                        .build();
            }

            Optional<EvrakKayit> evrakKayitOpt = evrakKayitRepo.findById(mahkemeKararTalep.getEvrakId());
            if (evrakKayitOpt.isEmpty()) {
                return MahkemeKararTalepUpdateResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage("Onaylanacak evrak bulunamadı")
                                .build())
                        .build();
            }

            EvrakKayit evrakKayit = evrakKayitOpt.get();
            if (!CommonUtils.isNullOrEmpty(evrakKayit.getEvrakYonu())) {
                EvrakTuru evrakTuru = EvrakTuru.fromName(evrakKayit.getEvrakYonu());

                if (evrakTuru == EvrakTuru.GENEL_EVRAK) {
                    kararTuru = KararTuru.GENEL_EVRAK;
                } else if (evrakTuru == EvrakTuru.ILETISIMIN_TESPITI) {
                    kararTuru = KararTuru.ILETISIMIN_TESPITI;
                } else {
                    if (!CommonUtils.isNullOrEmpty(mahkemeKararTalep.getKararTip())) {
                        int kararTipiInt = Integer.parseInt(mahkemeKararTalep.getKararTip());
                        MahkemeKararTip kararTipi = MahkemeKararTip.fromValue(kararTipiInt);
                        kararTuru = CommonUtils.getKararTuru(kararTipi);
                    }
                }
            }

            if (kararTuru != null) {
                MahkemeKararTalepIsleyici processor = mahkemeKararTalepOnayFactory.getKararTalepIsleyici(kararTuru);
                return processor.process(request, kullaniciId);

            } else {
                return MahkemeKararTalepUpdateResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage("Karar Tipi belirlenemedi")
                                .build())
                        .build();
            }
        }catch (Exception ex){
            log.error("MahkemeKararTalepUpdateRequest process failed, requestId:{}, mahkemeKararTalepId:{}", request.getId(), request.getMahkemeKararTalepId(), ex);
            return MahkemeKararTalepUpdateResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
        }
    }

    private ValidationResult validate(MahkemeKararTalepUpdateRequest request) {
        ValidationResult result = new ValidationResult(true);
        // TODO
        return result;
    }
}
