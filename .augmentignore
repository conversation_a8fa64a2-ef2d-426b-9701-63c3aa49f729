# Augment ignore file - patterns for files/directories to exclude from Augment's context engine

# Build outputs and compiled files
target/
build/
out/
*.class
*.jar
*.war
*.ear

# IDE files
.idea/
.vscode/
*.iml
*.ipr
*.iws
.project
.classpath
.settings/

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Test reports and coverage
htmlReport/
jacoco/
surefire-reports/
failsafe-reports/
test-results/

# Temporary files
*.tmp
*.bak
*.swp
*~

# Database files
*.db
*.mv.db
*.trace.db

# Docker
.env
docker-compose.override.yml

# Generated sources
target/generated-sources/
**/generated/

# Node modules (if any frontend components)
node_modules/

# Package files
*.zip
*.tar.gz
*.rar

### Test Reports ###
htmlReport/