package iym.makos.model.dto.auth;

import iym.common.model.api.ApiResponseBase;
import iym.common.enums.KullaniciKurum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.util.Set;

/**
 * Login response DTO for MAKOS authentication
 */
@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class LoginResponse extends ApiResponseBase {

    private String token;
    private Long userId;
    private String username;
    private String actingUserName;
    private Set<String> roles;
    private KullaniciKurum kurum;
}
