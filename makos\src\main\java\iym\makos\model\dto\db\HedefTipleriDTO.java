package iym.makos.model.dto.db;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for HedefTipleri entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Hedef Tipleri bilgilerini içerir")
public class HedefTipleriDTO {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "Hedef kodu", example = "1")
    @NotNull(message = "Hedef kodu boş olamaz")
    private Long hedefKodu;

    @Schema(description = "Hedef tipi", example = "TELEFON")
    @Size(max = 25, message = "Hedef tipi 25 karakterden fazla olamaz")
    private String hedefTipi;

    @Schema(description = "Sonlandırma mı", example = "E")
    @Size(max = 1, message = "Sonlandırma mı 1 karakterden fazla olamaz")
    private String sonlandirmami;

    @Schema(description = "Karşılığı", example = "1")
    private Long karsiligi;

    @Schema(description = "Sıra numarası", example = "1")
    private Long sno;

    @Schema(description = "Hedef tanım", example = "TEL")
    @Size(max = 16, message = "Hedef tanım 16 karakterden fazla olamaz")
    private String hedefTanim;

    @Schema(description = "Durum", example = "AKTIF")
    @Size(max = 8, message = "Durum 8 karakterden fazla olamaz")
    private String durum;

    @Schema(description = "Hitap tip", example = "TEL")
    @Size(max = 8, message = "Hitap tip 8 karakterden fazla olamaz")
    private String hitapTip;

    @Schema(description = "Hitap içerik tip", example = "NUM")
    @Size(max = 8, message = "Hitap içerik tip 8 karakterden fazla olamaz")
    private String hitapIcerikTip;

    @Schema(description = "Hitap içinde mi", example = "E")
    @Size(max = 8, message = "Hitap içinde mi 8 karakterden fazla olamaz")
    private String hitapIcindemi;

    @Schema(description = "Hitap evet/hayır", example = "E")
    @Size(max = 1, message = "Hitap evet/hayır 1 karakterden fazla olamaz")
    private String hitapEh;

    @Schema(description = "Minimum uzunluk", example = "10")
    private Long minl;

    @Schema(description = "Maximum uzunluk", example = "10")
    private Long maxl;

    @Schema(description = "İmha yapılsın mı", example = "E")
    @Size(max = 8, message = "İmha yapılsın mı 8 karakterden fazla olamaz")
    private String imhaYapilsinmi;

    @Schema(description = "Taşınabilir mi", example = "E")
    @Size(max = 1, message = "Taşınabilir mi 1 karakterden fazla olamaz")
    private String tasinabilirmi;

    @Schema(description = "Aktif mi", example = "1")
    private Long aktifmi;

    @Schema(description = "Hitapa gönderilecek mi", example = "0")
    private Long hitapaGonderilecekmi;
}
